//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileCore

open class StockTrackManager: WXTracking {
    public var defaultParameters: [String: String]

    public static var shared: StockTrackManager = {
        let manager = StockTrackManager()
        return manager
    }()

    public init() {
        defaultParameters = [:]
    }

    open func trackPage(_ eventInfo: EventInfo) {
        logDynamic(eventInfo.buildTaggingDictionary().description)
        guard let title = eventInfo.getFormattedTrackLogTitle(prefix: "Page") else { return }
        let text = eventInfo.getFormattedTrackLogContent()
        // TODO: Remove Notification Center Logic from framework and put in Sample App
        log(title: title, text: text)
    }

    open func trackEvent(_ eventInfo: EventInfo) {
        logDynamic(eventInfo.buildTaggingDictionary().description)
        guard let title = eventInfo.getFormattedTrackLogTitle(prefix: "Event") else { return }
        let text = eventInfo.getFormattedTrackLogContent()
        // TODO: Remove Notification Center Logic from framework and put in Sample App
        log(title: title, text: text)
    }

    open func appDynamicTrackEvent(_ message: String, _ methodName: String, _ withArguments: [String]) {
        logDynamic(message)
        log(title: methodName, text: message)
    }

//    /* Logs to the Sample App Console */
    open func log(title: String, text: String) {}
}

extension EventInfo {
    func getFormattedTrackLogTitle(prefix: String) -> String? {
        let dict = buildTaggingDictionary()
        guard let title = dict["page_url"] else { return nil }

        let dateFormatter = GregorianDateFormatter()
        dateFormatter.dateFormat = "HH:mm:ss"

        return "Track \(prefix) : \(title) (\(dateFormatter.string(from: Date())))"
    }

    func getFormattedTrackLogContent() -> String {
        let content = buildTaggingDictionary()
        let sortedContent: [(String, String)] = content.sorted { $0.0 < $1.0 }

        return sortedContent
            .map { (key, value) -> String in
                key + " : " + value
            }
            .joined(separator: "\n")
    }

    func buildTaggingDictionary() -> [String: String] {
        var dict: [String: String] = [
            "page_url": pageURL,
            "page_name": pageName,
            "screen_name": screenName,
            "page_language": pageLanguage
        ]

        if let customDict = customDict {
            dict.merge(customDict) { original, _ in original }
        }

        return dict
    }
}
