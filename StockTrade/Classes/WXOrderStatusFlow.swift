//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit

open class WXOrderStatusFlow {
    var stockMarket: WXMarket
    var tracker: WXTracking
    var orderPresenter: StockOrderPresenterImplementation?
    var priorInvestmentAccount: WXInvestmentAccount?
    var startDate: Date?
    var endDate: Date?
    public weak static var orderStatusNavigateListener: WXOrderStatusNavigationListener?

    public init(
        orderPresenter: StockOrderPresenterImplementation,
        startDate: Date?,
        endDate: Date?,
        market: WXMarket,
        priorInvestmentAccount: WXInvestmentAccount?,
        tracker: WXTracking,
        orderStatusNavigateListener: WXOrderStatusNavigationListener?
    ) {
        self.startDate = startDate
        self.endDate = endDate
        self.priorInvestmentAccount = priorInvestmentAccount
        self.orderPresenter = orderPresenter
        self.tracker = tracker
        stockMarket = market
        WXOrderStatusFlow.orderStatusNavigateListener = orderStatusNavigateListener
        StockTradeJourneyFactory.trackManager = tracker
    }

    public func build() -> UIViewController? {
        orderPresenter?.didEditSection(section: .stockMarket(stockMarket: stockMarket))

        if let priorInvestmentAccount = priorInvestmentAccount {
            orderPresenter?.didEditSection(section: .selectedInvestmentAccount(selectedInvestmentAccount: priorInvestmentAccount))
        }

        if let startDate = startDate, let endDate = endDate {
            orderPresenter?.didEditSection(section: .orderDateRange(startDate: startDate, endDate: endDate))
        }

        let viewController = OrderStatusViewControllerSingapore(tracker: tracker)
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        return viewController
    }
}
