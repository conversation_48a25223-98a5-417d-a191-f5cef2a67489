//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

public protocol WXStockTradeNavigationListener: AnyObject {
    func exitFlow(viewController: UIViewController, instruction: HSBCStockTradeOrderInstruction?)
    // Prior market right here means the stock market user just made the trade
    // and there is requirement @ OrderStatus flow
    // when user finished a trade and got navigate to OrderStatus screen
    // the app need to preselect the tab to the prior market
    func completeFlow(viewController: UIViewController, priorMarket: WXMarket)

    func toOrderStatus(viewController: UIViewController, investmentAccount: WXInvestmentAccount, market: WXMarket)
}
