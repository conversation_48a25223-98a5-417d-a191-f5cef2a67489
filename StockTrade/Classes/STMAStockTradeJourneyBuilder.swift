//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation
import MobileCore
import StockTrade
import UIKit

public class STMAStockTradeJourneyBuilder {
    var navigationController: UINavigationController {
        guard let navigationController = flowBuilders.first?.viewControllers.first?.base?.navigationController else {
            fatalError("NavigationController must be provided")
        }
        return navigationController
    }

    var networkClient: Networking

    var wealthITNetworkClient: Networking

    var accountsProxy: String

    var ordersProxy: String

    var productsProxy: String

    var wealthITProxy: String

    var topMoversProxy: String

    var watchlistProxy: String

    private var endJourneyCompletionHandler: WealthXJourneyCompletionHandler?

    private var flowBuilders = [WXFlowBuilder]()

    private var priorWealthXData: WealthXData?

    private let watchlistFlowResolver = Resolver()

    public init(
        networkClient: Networking,
        wealthITNetworkClient: Networking,
        accountsProxy: String,
        ordersProxy: String,
        productsProxy: String,
        wealthITProxy: String,
        topMoversProxy: String,
        watchlistProxy: String,
        tracker: WXTracking,
        endJourneyCompletionHandler: WealthXJourneyCompletionHandler?
    ) {
        self.networkClient = networkClient
        self.wealthITNetworkClient = wealthITNetworkClient
        self.accountsProxy = accountsProxy
        self.ordersProxy = ordersProxy
        self.productsProxy = productsProxy
        self.wealthITProxy = wealthITProxy
        self.topMoversProxy = topMoversProxy
        self.watchlistProxy = watchlistProxy
        self.endJourneyCompletionHandler = endJourneyCompletionHandler
        registerServices(tracker: tracker)
    }

    // Pop back to the previous flow in the stack
    func popToPreviousFlow() {
        // Make sure there is a previous flow, otherwise exit journey.
        guard flowBuilders.count > 1 else {
            exitJourney()
            return
        }

        // Get the current flow from the stack
        // Remove the reference from `flowBuilders`
        guard let currentFlow = flowBuilders.popLast() else {
            return
        }
        // Get the previous flow from the stack
        guard let previousFlow = flowBuilders.last else {
            return
        }

        // Navigate to the last screen from the previous flow
        guard let destinationViewController = previousFlow.topmostViewController else {
            return
        }
        navigationController.popToViewController(destinationViewController, animated: true)

        // Deinit all the view controllers and let the ARC does the rest
        currentFlow.dispose()
        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")
    }

    // Pop to an earlier/specific flow which exists in the stack
    func popToFlow(flow: WXFlowBuilder) {
        // Navigate to the last screen from the specified flow
        let destinationViewController = flow.topmostViewController! // flowBuilder.viewControllers.last
        navigationController.popToViewController(destinationViewController, animated: true)

        // Get the index of the flow in the `flowBuilders` array
        guard let flowIndex = flowBuilders.firstIndex(where: { $0.identifier == flow.identifier }) else {
            return
        }

        // Get all the flows after the destination flow
        for index in Array((flowIndex + 1) ..< flowBuilders.count).reversed() {
            // Remove the reference from `flowBuilders`
            // Deinit all the view controller and let the ARC does the rest
            flowBuilders.remove(at: index).dispose()
        }
        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")
    }

    // Initialize new flow and push to it
    func pushToFlow(flow: WXFlowBuilder) {
        // Add the referennce to `flowBuilders`
        flowBuilders.append(flow)
        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")

        // Assuming the `topMostViewController` right after the initialization
        // should be the first view controller of the flow
        let destinationViewController = flow.build()
        navigationController.pushViewController(destinationViewController, animated: true)
    }

    // Initialize new flow, push to it and reset the flow stack
    func pushToFlowThenHardreset(flow: WXFlowBuilder) {
        // Add the referennce to `flowBuilders`
        flowBuilders.append(flow)
        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")

        // Assuming the `topMostViewController` right after the initialization
        // should be the first view controller of the flow
        let destinationViewController = flow.build()
        navigationController.pushViewController(destinationViewController, animated: true) { [weak self] in
            guard let self = self else { return }
            logDynamic("\n*****************navigationController.viewControllers before clean up*****************\n \(self.navigationController.viewControllers)\n****************************************************")
            // Get the index of the first viewController in flow
            guard let firstControllerInFlow = self.flowBuilders.first?.viewControllers.first?.base,
                let index = self.navigationController.viewControllers.firstIndex(of: firstControllerInFlow) else {
                return
            }

            // Remove all the viewControllers belong to the flowBuilders that will be disposed from
            // the navigationController stack
            self.navigationController.viewControllers.removeSubrange(index ..< self.navigationController.viewControllers.endIndex - 1)

            // Get the index of the flow in the `flowBuilders` array
            guard let flowIndex = self.flowBuilders.firstIndex(where: { $0.identifier == flow.identifier }) else {
                return
            }

            // Get all the flows after the destination flow
            for index in Array(0 ..< flowIndex).reversed() {
                // Remove the reference from `flowBuilders`
                // Deinit all the view controller and let the ARC does the rest
                self.flowBuilders.remove(at: index).dispose()
            }

            logDynamic("\n*************** Current flowBuilders ***************\n \(self.flowBuilders.map { $0.identifier })\n\n navigationController.viewControllers after clean up\n \(self.navigationController.viewControllers)\n****************************************************")
        }
    }

    func exitJourney() {
        if let viewController = flowBuilders.first?.viewControllers.first?.base,
            let index = navigationController.viewControllers.firstIndex(where: { $0 == viewController }) {
            let previousIndex = index - 1
            if previousIndex < 0 {
                navigationController.dismiss(animated: true, completion: nil)
            } else {
                let viewController = navigationController.viewControllers[previousIndex]
                navigationController.popToViewController(viewController, animated: true)
            }
        }
        Resolver.reset()
        flowBuilders.forEach { $0.dispose() }
        flowBuilders.removeAll()

        endJourneyCompletionHandler?(.endJourney, priorWealthXData)
        endJourneyCompletionHandler = nil
        priorWealthXData = nil

        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")
    }

    public func startWatchlistFlow(
        market: WXMarket
    ) -> UIViewController {
        StockTradeJourneyFactory.suffix = "STMA"

        let flowBuilder = WXWatchListFlowBuilder(
            market: market,
            tracker: Resolver.resolve(),
            resolver: watchlistFlowResolver
        )

        flowBuilders.append(flowBuilder)
        let vc = flowBuilder.build()
        flowBuilder.flowDidBegin()
        return vc
    }

    public func startAddToWatchlistFlow(
        market: WXMarket,
        watchlistItem: WatchlistItem?
    ) -> UIViewController {
        StockTradeJourneyFactory.suffix = "STMA"
        let flowBuilder = WXAddToWatchListFlowBuilder(
            market: market,
            watchlistItem: watchlistItem,
            tracker: Resolver.resolve(),
            resolver: watchlistFlowResolver
        )

        flowBuilders.append(flowBuilder)
        return flowBuilder.build()
    }

    private func registerServices(tracker: WXTracking) {
        /**
         Ref: https://github.com/hmlongco/Resolver/blob/master/Documentation/Scopes.md#scope-cached
         This scope stores a strong reference to the resolved instance. Once created, every subsequent call to resolve will return the same instance.
         This is similar to how an application scope behaves, but unlike an application scope, cached scopes can be reset, releasing their cached objects.
         */
        Resolver.register { tracker as WXTracking }.scope(.cached)
        Resolver.register { self }
            .implements(WXQuoteNavigationListener.self)
            .implements(WXWatchListNavigationListener.self)
            .implements(WXAddToWatchListNavigationListener.self)
            .scope(.cached)

        let watchlistFlowResolver = self.watchlistFlowResolver
        // create watchlistPresenter before starting flow
        let watchlistPresenter = WatchlistPresenterImplementation()
        // assign network client to watchlistPresenter before starting flow
        watchlistPresenter.setInteractorImplementation(
            WatchlistInteractorImplementation(),
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            watchlistProxy: watchlistProxy
        )

        // register instance to watchlistFlowResolver
        watchlistFlowResolver
            .register(name: "WXWatchListFlowBuilder.WatchlistPresenterImplementation")
            { watchlistPresenter }
            .scope(.cached)

        Resolver.main.add(child: watchlistFlowResolver)
    }

    private func unregisterServices() {
        ResolverScope.cached.reset()
        Resolver.main.cache.reset()
    }

    deinit {
        unregisterServices()
    }
}

extension STMAStockTradeJourneyBuilder: WXAddToWatchListNavigationListener {
    public func addToWatchListCompletedFlow(flowBuilder: WXFlowBuilder) {
        exitAddToWatchlist(flowBuilder: flowBuilder)
    }

    public func addToWatchListNavigateClickDone(
        flowBuilder: WXFlowBuilder,
        market: WXMarket,
        watchlistsItem: WatchlistItem,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult?,
        watchlists: [WatchListInfo]
    ) {
        exitAddToWatchlist(flowBuilder: flowBuilder)
    }

    private func exitAddToWatchlist(flowBuilder: WXFlowBuilder) {
        if let viewController = flowBuilder.viewControllers.first?.base {
            if let _ = viewController.presentingViewController {
                viewController.navigationController?.dismiss(animated: true, completion: {
                    if let index = self.flowBuilders.firstIndex(where: { $0 === flowBuilder }) {
                        self.flowBuilders.remove(at: index).dispose()
                    }

                    if self.flowBuilders.isEmpty {
                        Resolver.reset()
                        self.endJourneyCompletionHandler?(.endJourney, self.priorWealthXData)
                        self.endJourneyCompletionHandler = nil
                        self.priorWealthXData = nil
                        StockTradeJourneyFactory.suffix = nil
                        StockTradeJourneyFactory.prefix = nil
                    }
                })
            } else {
                if let index = viewController.navigationController!.viewControllers.firstIndex(where: { $0 is WatchlistViewController }) {
                    let previousIndex = index
                    if previousIndex < 0 {
                        navigationController.dismiss(animated: true, completion: nil)
                    } else {
                        let viewController = navigationController.viewControllers[previousIndex]
                        navigationController.popToViewController(viewController, animated: true)
                    }
                } else {
                    viewController.navigationController?.popViewController(animated: true)
                }

                if let index = flowBuilders.firstIndex(where: { $0 is WXSearchFlow }) {
                    flowBuilders.remove(at: index).dispose()
                }

                if let index = flowBuilders.firstIndex(where: { $0 === flowBuilder }) {
                    flowBuilders.remove(at: index).dispose()
                }

                if flowBuilders.isEmpty {
                    Resolver.reset()
                    endJourneyCompletionHandler?(.endJourney, priorWealthXData)
                    endJourneyCompletionHandler = nil
                    priorWealthXData = nil
                    StockTradeJourneyFactory.suffix = nil
                    StockTradeJourneyFactory.prefix = nil
                }
            }
        }

        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")
    }
}

extension STMAStockTradeJourneyBuilder: WXWatchListNavigationListener {
    public func watchListNavigateToQuotePage(market: WXMarket, watchlistsItem: WatchlistItem, sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult?) {
        let vc = startAddToWatchlistFlow(market: market, watchlistItem: watchlistsItem)
        let navVc = StockTradeJourneyFactory.getNavgationControllerFromSuffix(WXCustomNavigationController.self, rootViewController: vc)
        navVc.navigationBar.prefersLargeTitles = false
        navVc.navigationItem.largeTitleDisplayMode = .never
        navigationController.present(navVc, animated: true, completion: nil)
//        let flowBuilder = WXAddToWatchListFlowBuilder(
//            market: market,
//            watchlistItem: watchlistsItem,
//            tracker: Resolver.resolve(),
//            resolver: watchlistFlowResolver
//        )
//        pushToFlow(flow: flowBuilder)
    }

    public func watchListCompletedFlow() {
        exitWatchlistJourney()
    }

    public func watchListShowMoreClick() {
        exitWatchlistJourney()
    }

    public func watchListNavigateToSearch(market: WXMarket) {
        let accountInvestmentPresenter = AccountInvestmentPresenterImplementation.shared
        accountInvestmentPresenter.journeyDidBegin(
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            accountsProxy: accountsProxy,
            ordersProxy: ordersProxy,
            productsProxy: productsProxy,
            wealthITProxy: wealthITProxy,
            topMoversProxy: topMoversProxy
        )

        let searchPresenter = StockSearchPresenterImplementation.shared
        searchPresenter.journeyDidBegin(
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            accountsProxy: accountsProxy,
            ordersProxy: ordersProxy,
            productsProxy: productsProxy,
            wealthITProxy: wealthITProxy,
            topMoversProxy: topMoversProxy
        )

        let searchFlowBuilder = WXSearchFlow(
            accountPresenter: accountInvestmentPresenter,
            searchPresenter: searchPresenter,
            market: market,
            investmentAccount: nil,
            tracker: Resolver.resolve(),
            searchNavigationListener: self
        )

        pushToFlow(flow: searchFlowBuilder)
    }

    private func exitWatchlistJourney() {
        navigationController.popViewController(animated: true)
        Resolver.reset()
        flowBuilders.forEach { $0.dispose() }
        flowBuilders.removeAll()

        endJourneyCompletionHandler?(.endJourney, priorWealthXData)
        endJourneyCompletionHandler = nil
        priorWealthXData = nil
        StockTradeJourneyFactory.suffix = nil
        StockTradeJourneyFactory.prefix = nil

        logDynamic("\n*************** Current flowBuilders ***************\n \(flowBuilders.map { $0.identifier })\n****************************************************")
    }
}

extension STMAStockTradeJourneyBuilder: WXSearchNavigationListener {
    public func normalEnd(searchViewController: UIViewController) {
        popToPreviousFlow()
    }

    public func selectedSearchRecord(searchViewController: UIViewController, product: StockSearchRecord?) {
        guard let product = product else {
            return
        }

//        let accountInvestmentPresenter = AccountInvestmentPresenterImplementationSingapore.shared
//        accountInvestmentPresenter.journeyDidBegin(
//            networkClient: networkClient,
//            wealthITNetworkClient: wealthITNetworkClient,
//            accountsProxy: accountsProxy,
//            ordersProxy: ordersProxy,
//            productsProxy: productsProxy,
//            wealthITProxy: wealthITProxy,
//            topMoversProxy: topMoversProxy
//        )
//
//        let accountSettlementPresenter = AccountSettlementPresenterImplementationSingapore.shared
//        accountSettlementPresenter.journeyDidBegin(
//            networkClient: networkClient,
//            wealthITNetworkClient: wealthITNetworkClient,
//            accountsProxy: accountsProxy,
//            ordersProxy: ordersProxy,
//            productsProxy: productsProxy,
//            wealthITProxy: wealthITProxy,
//            topMoversProxy: topMoversProxy
//        )
//
//        let quotePresenter = StockQuotePresenterImplementationSingapore.shared
//        quotePresenter.journeyDidBegin(
//            networkClient: networkClient,
//            wealthITNetworkClient: wealthITNetworkClient,
//            accountsProxy: accountsProxy,
//            ordersProxy: ordersProxy,
//            productsProxy: productsProxy,
//            wealthITProxy: wealthITProxy,
//            topMoversProxy: topMoversProxy
//        )
//
//        let sellPresenter = StockSellPresenterImplementationSingapore.shared
//
//        let customerEligibilityPresenter = CustomerEligibilityPresenterImplementationSingapore.shared
//        customerEligibilityPresenter.journeyDidBegin(
//            networkClient: networkClient,
//            wealthITNetworkClient: wealthITNetworkClient,
//            accountsProxy: accountsProxy,
//            ordersProxy: ordersProxy,
//            productsProxy: productsProxy,
//            wealthITProxy: wealthITProxy,
//            topMoversProxy: topMoversProxy
//        )
//
//        let quoteFlow = WXQuoteFlowBuilder(
//            networkClient: networkClient,
//            wealthITNetworkClient: wealthITNetworkClient,
//            accountsProxy: accountsProxy,
//            ordersProxy: ordersProxy,
//            productsProxy: productsProxy,
//            wealthITProxy: wealthITProxy,
//            topMoversProxy: topMoversProxy,
//            market: product.market,
//            tracker: Resolver.resolve(),
//            stockCode: product.stockCode,
//            productType: product.productType,
//            sfcRegulatoryCheckingResult: nil,
//            quotePresenter: quotePresenter,
//            sellPresenter: sellPresenter,
//            accountInvestmentPresenter: accountInvestmentPresenter,
//            accountSettlementPresenter: accountSettlementPresenter,
//            customerEligibilityPresenter: customerEligibilityPresenter
//        )
//        pushToFlow(flow: quoteFlow)
    }

    public func selectedSearchRecordAndMDSDown(
        searchViewController: UIViewController,
        product: StockSearchRecord?
    ) {}

    public func selectedSearchRecordAddToWatchlist(
        searchViewController: UIViewController,
        product: StockSearchRecord?
    ) {
        guard let market = product?.market else {
            return
        }
        let item = WatchlistItem(nil, product?.stockCode, "M", product?.productType.apiValue, nil)
        let flowBuilder = WXAddToWatchListFlowBuilder(
            market: market,
            watchlistItem: item,
            tracker: Resolver.resolve(),
            resolver: watchlistFlowResolver
        )
        pushToFlow(flow: flowBuilder)
    }
}
