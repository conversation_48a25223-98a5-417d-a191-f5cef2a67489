//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

public class RoundedButton: UIButton, Rounded {
    @IBInspectable public var radiusMultiplier: CGFloat = defaultRadiusMultiplier {
        didSet {
            updateCorderRadius()
        }
    }

    @IBInspectable public var borderWidth: CGFloat = defaultBorderWidth {
        didSet {
            updateBorderWidth()
        }
    }

    @IBInspectable public var borderColor: UIColor? {
        didSet {
            updateBoderColor()
        }
    }

    override public var bounds: CGRect {
        didSet {
            updateCorderRadius()
        }
    }
}
