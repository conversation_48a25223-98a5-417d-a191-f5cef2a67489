//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

protocol Rounded {
    var radiusMultiplier: CGFloat { get set }
    var borderWidth: CGFloat { get set }
    var borderColor: UIColor? { get set }
}

extension Rounded where Self: UIView {
    // Default values
    static var defaultRadiusMultiplier: CGFloat {
        0
    }

    static var defaultBorderWidth: CGFloat {
        0
    }

    // Helper functions for updating the layer
    func updateBorderWidth() {
        layer.borderWidth = borderWidth
    }

    func updateBoderColor() {
        layer.borderColor = borderColor?.cgColor
    }

    func updateCorderRadius() {
        guard radiusMultiplier != 0 else {
            layer.cornerRadius = 0
            return
        }
        let shortestDimmension = min(bounds.size.width, bounds.size.height)
        layer.cornerRadius = shortestDimmension / radiusMultiplier
    }
}
