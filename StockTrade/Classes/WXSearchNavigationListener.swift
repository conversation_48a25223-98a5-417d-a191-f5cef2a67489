//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import UIKit

public protocol WXSearchNavigationListener: AnyObject {
    func normalEnd(searchViewController: UIViewController)
    func selectedSearchRecord(searchViewController: UIViewController, product: StockSearchRecord?)
    func selectedSearchRecordAndMDSDown(searchViewController: UIViewController, product: StockSearchRecord?)
    func selectedSearchRecordAddToWatchlist(searchViewController: UIViewController, product: StockSearchRecord?)
}
