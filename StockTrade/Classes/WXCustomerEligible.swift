//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation

public struct WXCustomerEligiblility: Equatable {
    var isCustomerEligible: Bool?
    var isMobileValid: Bool?
    var isEmailValid: Bool?
    var customerEligibilityWarnings: [HSBCWarning]?

    public static func == (lhs: WXCustomerEligiblility, rhs: WXCustomerEligiblility) -> Bool {
        lhs.isCustomerEligible == rhs.isCustomerEligible &&
            lhs.isMobileValid == rhs.isMobileValid &&
            lhs.isEmailValid == rhs.isEmailValid &&
            lhs.customerEligibilityWarnings == rhs.customerEligibilityWarnings
    }
}
