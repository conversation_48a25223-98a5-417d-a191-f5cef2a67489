//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit

public protocol WXOrderStatusFlowNavigationListener: AnyObject {
    func completeOrderStatusFlow()
    func modifyBuy(market: WXMarket, productCode: String, productType: WXInvestmentProductType)
    func modifySell(market: WXMarket, productCode: String, productType: WXInvestmentProductType)
    func cancelBuy(market: WXMarket, productCode: String, productType: WXInvestmentProductType)
    func cancelSell(market: WXMarket, productCode: String, productType: WXInvestmentProductType)
}

open class WXOrderStatusFlowBuilder: WXFlowBuilder {
    public var tracker: WXTracking = Resolver.resolve()

    public var viewControllers: [WeakViewController] {
        _viewControllers
    }

    private var networkClient: Networking
    private var accountsProxy: String
    private var ordersProxy: String
    private var productsProxy: String
    // Network client - WealthIT
    private var wealthITNetworkClient: Networking
    private var wealthITProxy: String
    private var topMoversProxy: String

    private var market: WXMarket

    private var orderPresenter: StockOrderPresenterImplementation

    private var accountInvestmentPresenter: AccountInvestmentPresenterImplementation

    private var accountSettlementPresenter: AccountSettlementPresenterImplementation

    private var priorInvestmentAccount: WXInvestmentAccount?

    private var _viewControllers = [WeakViewController]()

    public weak var orderStatusFlowNavigateListener: WXOrderStatusFlowNavigationListener?

    public init(
        market: WXMarket,
        // Network client - Microservice
        networkClient: Networking,
        accountsProxy: String,
        ordersProxy: String,
        productsProxy: String,
        // Network client - WealthIT
        wealthITNetworkClient: Networking,
        wealthITProxy: String,
        topMoversProxy: String,
        priorInvestmentAccount: WXInvestmentAccount?,
        orderPresenter: StockOrderPresenterImplementation,
        accountInvestmentPresenter: AccountInvestmentPresenterImplementation,
        accountSettlementPresenter: AccountSettlementPresenterImplementation,
        orderStatusFlowNavigateListener: WXOrderStatusFlowNavigationListener
    ) {
        self.market = market
        self.networkClient = networkClient
        self.accountsProxy = accountsProxy
        self.ordersProxy = ordersProxy
        self.productsProxy = productsProxy
        self.wealthITNetworkClient = wealthITNetworkClient
        self.wealthITProxy = wealthITProxy
        self.topMoversProxy = topMoversProxy
        self.orderPresenter = orderPresenter
        self.priorInvestmentAccount = priorInvestmentAccount
        self.accountInvestmentPresenter = accountInvestmentPresenter
        self.accountSettlementPresenter = accountSettlementPresenter
        self.accountSettlementPresenter = accountSettlementPresenter
        self.orderStatusFlowNavigateListener = orderStatusFlowNavigateListener
    }

    public func build() -> UIViewController {
        orderPresenter.didEditSection(section: .stockMarket(stockMarket: market))
        accountInvestmentPresenter.didEditSection(section: .market(market: market))
        accountSettlementPresenter.didEditSection(section: .market(market: market))

        if let priorInvestmentAccount = priorInvestmentAccount {
            accountInvestmentPresenter.didEditSection(
                section: .preselectPriorInvestmentAccount(priorInvestmentAccount: priorInvestmentAccount)
            )
        }

        orderPresenter.journeyDidBegin(
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            accountsProxy: accountsProxy,
            ordersProxy: ordersProxy,
            productsProxy: productsProxy,
            wealthITProxy: wealthITProxy,
            topMoversProxy: topMoversProxy
        )

        accountInvestmentPresenter.journeyDidBegin(
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            accountsProxy: accountsProxy,
            ordersProxy: ordersProxy,
            productsProxy: productsProxy,
            wealthITProxy: wealthITProxy,
            topMoversProxy: topMoversProxy
        )

        accountSettlementPresenter.journeyDidBegin(
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            accountsProxy: accountsProxy,
            ordersProxy: ordersProxy,
            productsProxy: productsProxy,
            wealthITProxy: wealthITProxy,
            topMoversProxy: topMoversProxy
        )

        let viewController = OrderStatusViewControllerSingapore(tracker: tracker, flowBuilder: self)
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        _viewControllers.append(WeakViewController(viewController))
        return viewController
    }

    public func dispose() {
        _viewControllers.removeAll()
    }
}
