//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation
import UIKit

public protocol WXQuoteNavigationListener: AnyObject {
    func completeQuoteFlow()

    func navigateToBuy(market: WXMarket, productCode: String, productType: WXInvestmentProductType)
    func navigateToSell(market: WXMarket, productCode: String, productType: WXInvestmentProductType)
}

open class WXQuoteFlowBuilder: WXFlowBuilder {
    public var tracker: WXTracking

    public var viewControllers: [WeakViewController] {
        _viewControllers
    }

    private var _viewControllers = [WeakViewController]()

    public init(
        networkClient: Networking?,
        wealthITNetworkClient: Networking?,
        accountsProxy: String,
        ordersProxy: String,
        productsProxy: String,
        wealthITProxy: String,
        topMoversProxy: String,
        market: WXMarket,
        tracker: WXTracking,
        stockCode: String,
        currency: WXCurrencyCode? = nil,
        productType: WXInvestmentProductType,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult?,
        quotePresenter: StockQuotePresenterImplementation,
        sellPresenter: StockSellPresenterImplementation,
        accountInvestmentPresenter: AccountInvestmentPresenterImplementation,
        accountSettlementPresenter: AccountSettlementPresenterImplementation,
        customerEligibilityPresenter: CustomerEligibilityPresenterImplementationSingapore
    ) {
        quotePresenter.didEditSection(section: .market(market: market))
        quotePresenter.didEditSection(section: .stockCode(string: stockCode))
        quotePresenter.didEditSection(section: .productType(type: productType))

        sellPresenter.didEditSection(section: .stockCode(string: stockCode))
        sellPresenter.didEditSection(section: .market(market: market))
        sellPresenter.didEditSection(section: .productType(type: productType))
        sellPresenter.journeyDidBegin(
            networkClient: networkClient,
            wealthITNetworkClient: wealthITNetworkClient,
            accountsProxy: accountsProxy,
            ordersProxy: ordersProxy,
            productsProxy: productsProxy,
            wealthITProxy: wealthITProxy,
            topMoversProxy: topMoversProxy
        )

        accountInvestmentPresenter.didEditSection(section: .market(market: market))
        if let sfcRegulatoryCheckingResult = sfcRegulatoryCheckingResult {
            accountInvestmentPresenter.didEditSection(section: .sfcRegulatoryCheckingResult(result: sfcRegulatoryCheckingResult))
        }

        accountSettlementPresenter.didEditSection(section: .market(market: market))

        // Align with current implementation
        // Reference: https://alm-github.systems.uk.hsbc/mobile/stocktrade-ios-lib/blob/e4f79079419805c48b6c485bba2e733414be70b8/StockTrade/StockTradeJourneyFactory.swift#L437-L439
        if let currency = currency {
            accountSettlementPresenter.didEditSection(section: .currency(currency: currency))
        }

        customerEligibilityPresenter.didEditSection(section: .market(market: market))
        customerEligibilityPresenter.didEditSection(section: .productType(productType: productType))
        customerEligibilityPresenter.didEditSection(section: .stockCode(stockCode: stockCode))
        self.tracker = tracker
    }

    public func dispose() {
        _viewControllers.removeAll()
    }

    public func build() -> UIViewController {
        let viewController = QuoteViewControllerSingapore(tracker: Resolver.optional())
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        _viewControllers.append(WeakViewController(viewController))
        return viewController
    }
}
