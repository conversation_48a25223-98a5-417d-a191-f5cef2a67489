//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit

open class WXOrderStatusDetailFlow {
    var stockMarket: WXMarket
    var tracker: WXTracking
    var orderPresenter: StockOrderPresenterImplementation?
    var orderDetailParam: String?
    public weak static var orderStatusNavigateListener: WXOrderStatusNavigationListener?

    public init(
        orderPresenter: StockOrderPresenterImplementation,
        market: WXMarket,
        orderDetailParam: String,
        tracker: WXTracking,
        orderStatusNavigateListener: WXOrderStatusNavigationListener?
    ) {
        self.orderPresenter = orderPresenter
        self.tracker = tracker
        self.orderDetailParam = orderDetailParam
        stockMarket = market
        WXOrderStatusFlow.orderStatusNavigateListener = orderStatusNavigateListener
        StockTradeJourneyFactory.trackManager = tracker
    }

    public func build() -> UIViewController? {
        orderPresenter?.didEditSection(section: .stockMarket(stockMarket: stockMarket))
        orderPresenter?.didEditSection(section: .selectedOrderRecord(selectedOrderRecord: StockOrderRecord(market: stockMarket, stockCode: nil, stockName: nil, instruction: nil, orderType: .limit, totalShares: nil, executedShares: nil, instructionPrice: nil, currency: nil, orderStatus: nil, orderDate: nil, orderDetailParam: orderDetailParam)))
        let viewController = StockOrderDetailViewControllerSingapore()
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        return viewController
    }
}
