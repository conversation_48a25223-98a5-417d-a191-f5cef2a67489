//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

public protocol WXOrderStatusNavigationListener: AnyObject {
    func normalEnd(orderStatusViewController: UIViewController)
    func modifyBuy(orderStatusViewController: UIViewController, orderDetail: StockOrderDetail)
    func modifySell(orderStatusViewController: UIViewController, orderDetail: StockOrderDetail)
    func cancelBuy(orderStatusViewController: UIViewController, orderDetail: StockOrderDetail)
    func cancelSell(orderStatusViewController: UIViewController, orderDetail: StockOrderDetail)
}
