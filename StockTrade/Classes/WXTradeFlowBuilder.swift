//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation
import UIKit

public protocol WXTradeFlowNavigationListener: AnyObject {
    // When user hits the back button on `OrderDetailInputScreen`
    func exitTradeFlow()

    // Prior market right here means the stock market user just made the trade
    // and there is requirement @ OrderStatus flow
    // when user finished a trade and got navigate to OrderStatus screen
    // the app need to preselect the tab to the prior market
    // Prior invesment is for a similiar purpose
    func completeTradeFlow(priorMarket: WXMarket, priorInvestmentAccount: WXInvestmentAccount)
    func navigateToOrderStatus(priorMarket: WXMarket, priorInvestmentAccount: WXInvestmentAccount)
}

final class WXTradeFlowBuilder: WXFlowBuilder {
    var viewControllers: [WeakViewController]
    var navigationListener: WXTradeFlowNavigationListener?

    // MARK: - Network client - Microservice

    private var microServiceNetwork: Networking?
    private var accountsProxy: String?
    private var ordersProxy: String?
    private var productsProxy: String?
    // Network client - WealthIT
    private var wealthITNetwork: Networking?
    private var wealthITProxy: String?
    private var topMoversProxy: String?

    // MARK: - Trade related

    private var productCode: String?
    private var productType: WXInvestmentProductType?
    private var stockMarket: WXMarket?
    private var orderInstruction: HSBCStockTradeOrderInstruction?
    private var priorInvestmentAccount: WXInvestmentAccount?
    private var priorSettlementAccount: WXSettlementAccount?
    // Trade - modify order
    private var overrideObject: OverrideObject?

    // MARK: - Presenters

    private var buyPresenter: StockBuyPresenterImplementation?
    private var sellPresenter: StockSellPresenterImplementation?
    private var quotePresenter: StockQuotePresenterImplementation?
    private var accountSettlementPresenter: AccountSettlementPresenterImplementation?
    private var accountInvestmentPresenter: AccountInvestmentPresenterImplementation?

    init(
        tracker: WXTracking,
        // Network client - Microservice
        microServiceNetwork: Networking,
        accountsProxy: String,
        ordersProxy: String,
        productsProxy: String,
        // Network client - WealthIT
        wealthITNetwork: Networking,
        wealthITProxy: String,
        topMoversProxy: String,
        // Trade related
        productCode: String,
        productType: WXInvestmentProductType,
        stockMarket: WXMarket,
        priorInvestmentAccount: WXInvestmentAccount?,
        priorSettlementAccount: WXSettlementAccount?,
        // TODO: `orderInstruction` may update to optional in order to support `Quote` flow
        //       or `StockTrade.didEditSection` to support resetting `.instruction` to nil
        orderInstruction: HSBCStockTradeOrderInstruction,
        // Trade - modify order
        overrideObject: OverrideObject?,
        // Presenters
        buyPresenter: StockBuyPresenterImplementation,
        sellPresenter: StockSellPresenterImplementation,
        quotePresenter: StockQuotePresenterImplementation,
        accountSettlementPresenter: AccountSettlementPresenterImplementation,
        accountInvestmentPresenter: AccountInvestmentPresenterImplementation,
        navigationListener: WXTradeFlowNavigationListener
    ) {
        viewControllers = []
        self.tracker = tracker
        self.navigationListener = navigationListener

        self.microServiceNetwork = microServiceNetwork
        self.accountsProxy = accountsProxy
        self.ordersProxy = ordersProxy
        self.productsProxy = productsProxy

        self.wealthITNetwork = wealthITNetwork
        self.wealthITProxy = wealthITProxy
        self.topMoversProxy = topMoversProxy

        self.productCode = productCode
        self.productType = productType
        self.stockMarket = stockMarket
        self.priorInvestmentAccount = priorInvestmentAccount
        self.priorSettlementAccount = priorSettlementAccount
        self.orderInstruction = orderInstruction

        self.buyPresenter = buyPresenter
        self.quotePresenter = quotePresenter
        self.accountSettlementPresenter = accountSettlementPresenter
        self.accountInvestmentPresenter = accountInvestmentPresenter
    }

    public func dispose() {
        viewControllers.removeAll()
        navigationListener = nil
    }

    public func build() -> UIViewController {
        switch orderInstruction {
        case .buy where overrideObject == nil:
            // reset https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-8661
            buyPresenter?.didEditSection(section: .orderType(type: nil))
            buyPresenter?.didEditSection(section: .quantity(quantity: nil))
            buyPresenter?.didEditSection(section: .proposedQuantity(nil))
            buyPresenter?.didEditSection(section: .settlementAccount(account: nil))
            buyPresenter?.didEditSection(section: .investmentAccount(account: nil))
            buyPresenter?.didEditSection(section: .goodUntil(date: nil))
            buyPresenter?.didEditSection(section: .investorCharacterizationCheckingWarnings(nil))
            buyPresenter?.didEditSection(section: .investorCharacterizationCheckingResult(nil))
            buyPresenter?.didEditSection(section: .acceptedInvestorTermsAndCondition(nil))

            // WXMA-9171: Reset selected investment account
            accountSettlementPresenter?.didEditSection(section: .selectedInvestmentAccount(account: nil))

            buyPresenter?.didEditSection(section: .market(market: stockMarket!))
            buyPresenter?.didEditSection(section: .stockCode(string: productCode!))
            buyPresenter?.didEditSection(section: .productType(type: productType!))
            buyPresenter?.didEditSection(section: .instruction(instruction: orderInstruction!))

            quotePresenter?.didEditSection(section: .market(market: stockMarket!))
            quotePresenter?.didEditSection(section: .stockCode(string: productCode!))
            quotePresenter?.didEditSection(section: .productType(type: productType!))

            accountSettlementPresenter?.didEditSection(section: .market(market: stockMarket!))
            accountInvestmentPresenter?.didEditSection(section: .market(market: stockMarket!))
            // TODO: Need to decide whether we move it out from flow builder
            accountInvestmentPresenter?.didEditSection(section: .sfcRegulatoryCheckingResult(result: .bothAreValid))

            if let priorInvestmentAccount = self.priorInvestmentAccount {
                accountInvestmentPresenter?.didEditSection(section: .preselectPriorInvestmentAccount(priorInvestmentAccount: priorInvestmentAccount))
            }

            if let priorSettlementAccount = self.priorSettlementAccount {
                accountSettlementPresenter?.didEditSection(section: .preselectPriorSettlementAccount(priorSettlementAccount: priorSettlementAccount))
            }

            // Kick start presenters' `journeyDidBegin` method
            buyPresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            quotePresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            accountSettlementPresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            accountInvestmentPresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            quotePresenter?.didEditSection(section: .isDelay(isDelay: false)) // call isDelay to trigger attempt Quote
            // Can't get flowbuilder here
            // Temporary solution to support navigation when using flow builder
            buyPresenter?.routers[identifier] = self
            let viewController = BuyOrderDetailsInputScreenSGViewController(market: stockMarket!, stockProductCode: productCode!, tracker: tracker, flowBuilder: self)
            viewControllers.append(WeakViewController(viewController))
            return viewController
        case .sell where overrideObject == nil:
            // reset https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-8661
            sellPresenter?.didEditSection(section: .orderType(type: nil))
            sellPresenter?.didEditSection(section: .quantity(quantity: nil))
            sellPresenter?.didEditSection(section: .proposedQuantity(nil))
            sellPresenter?.didEditSection(section: .settlementAccount(account: nil))
            sellPresenter?.didEditSection(section: .investmentAccount(account: nil))
            sellPresenter?.didEditSection(section: .goodUntil(date: nil))
            sellPresenter?.didEditSection(section: .investorCharacterizationCheckingWarnings(nil))
            sellPresenter?.didEditSection(section: .investorCharacterizationCheckingResult(nil))
            sellPresenter?.didEditSection(section: .acceptedInvestorTermsAndCondition(nil))

            // WXMA-9171: Reset selected investment account
            accountSettlementPresenter?.didEditSection(section: .selectedInvestmentAccount(account: nil))

            sellPresenter?.didEditSection(section: .market(market: stockMarket!))
            sellPresenter?.didEditSection(section: .stockCode(string: productCode!))
            sellPresenter?.didEditSection(section: .productType(type: productType!))
            sellPresenter?.didEditSection(section: .instruction(instruction: orderInstruction!))

            quotePresenter?.didEditSection(section: .market(market: stockMarket!))
            quotePresenter?.didEditSection(section: .stockCode(string: productCode!))
            quotePresenter?.didEditSection(section: .productType(type: productType!))

            accountSettlementPresenter?.didEditSection(section: .market(market: stockMarket!))
            accountInvestmentPresenter?.didEditSection(section: .market(market: stockMarket!))
            // TODO: Need to decide whether we move it out from flow builder
            accountInvestmentPresenter?.didEditSection(section: .sfcRegulatoryCheckingResult(result: .bothAreValid))

            if let priorInvestmentAccount = self.priorInvestmentAccount {
                accountInvestmentPresenter?.didEditSection(section: .preselectPriorInvestmentAccount(priorInvestmentAccount: priorInvestmentAccount))
            }

            if let priorSettlementAccount = self.priorSettlementAccount {
                accountSettlementPresenter?.didEditSection(section: .preselectPriorSettlementAccount(priorSettlementAccount: priorSettlementAccount))
            }

            // Kick start presenters' `journeyDidBegin` method
            sellPresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            quotePresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            accountSettlementPresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            accountInvestmentPresenter?.journeyDidBegin(
                networkClient: microServiceNetwork,
                wealthITNetworkClient: wealthITNetwork,
                accountsProxy: accountsProxy!,
                ordersProxy: ordersProxy!,
                productsProxy: productsProxy!,
                wealthITProxy: wealthITProxy!,
                topMoversProxy: topMoversProxy!
            )

            quotePresenter?.didEditSection(section: .isDelay(isDelay: false)) // call isDelay to trigger attempt Quote
            let viewController = SellOrderDetailsInputScreenSGViewController(market: stockMarket!, stockProductCode: productCode!, tracker: tracker)
            // Should use flowControllable protocol?
            viewController.flowBuilder = self
            viewControllers.append(WeakViewController(viewController))
            return viewController
        case .modifyBuy where overrideObject != nil:
            return BuyOrderDetailsInputScreenSGViewController(market: .hongKong, stockProductCode: "5", tracker: tracker)
        case .modifySell where overrideObject != nil:
            return SellOrderDetailsInputScreenSGViewController(market: .hongKong, stockProductCode: "5", tracker: tracker)
        case .cancelBuy where overrideObject != nil:
            return BuyOrderReviewViewControllerSingapore()
        case .cancelSell where overrideObject != nil:
            return SellOrderReviewViewControllerSingapore()
        default:
            fatalError("Unsupported setup >> \(orderInstruction) / \(overrideObject)")
        }
    }

    // MARK: - StockBuyRouting

    var viewController: UIViewController!

    public var tracker: WXTracking

    init(viewController: UIViewController, tracker: WXTracking) {
        self.viewController = viewController
        viewControllers = []
        self.tracker = tracker
    }
}

extension WXTradeFlowBuilder: StockBuyRouter, StockSellRouter {
    func dismissViewController() {}

    func navigateToOrderReviewScreen() {
        if orderInstruction == .buy || orderInstruction == .modifyBuy {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                // TODO: - refactor to load regional viewcontroller respectively according to suffix
                let previewViewController = BuyOrderReviewViewControllerSingapore(flowBuilder: self)
                self.viewControllers.append(WeakViewController(previewViewController))
                self.viewControllers.first?.base?.navigationController?.pushViewController(previewViewController, animated: true)
            }
        } else if orderInstruction == .sell || orderInstruction == .modifySell {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                let previewViewController = SellOrderReviewViewControllerSingapore()
                previewViewController.flowBuilder = self
                self.viewControllers.append(WeakViewController(previewViewController))
                self.viewControllers.first?.base?.navigationController?.pushViewController(previewViewController, animated: true)
            }
        }
    }

    func navigateToOrderConfirmationScreen() {
        if orderInstruction == .buy || orderInstruction == .modifyBuy {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                let confirmationViewController = BuyOrderConfirmationViewControllerSingapore(flowBuilder: self)
                self.viewControllers.append(WeakViewController(confirmationViewController))
                self.viewControllers.first?.base?.navigationController?.pushViewController(confirmationViewController, animated: true)
            }
        } else if orderInstruction == .sell || orderInstruction == .modifySell {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                let confirmationViewController = SellOrderConfirmationViewControllerSingapore()
                confirmationViewController.flowBuilder = self
                self.viewControllers.append(WeakViewController(confirmationViewController))
                self.viewControllers.first?.base?.navigationController?.pushViewController(confirmationViewController, animated: true)
            }
        }
    }
}
