//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit

// public enum WXSearchFlowEndType {
//    case SEARCH_NORMALEND
//    case SELECTED_SEARCH_RECORD
//    case SELECTED_SEARCH_RECORD_AND_MDSDOWN
// }

open class WXSearchFlow: WXFlowBuilder {
    public var viewControllers: [WeakViewController] {
        _viewControllers
    }

    private var _viewControllers = [WeakViewController]()

    var stockMarket: WXMarket?
    public var tracker: WXTracking
    var accountPresenterImpl: AccountInvestmentPresenterImplementation?
    var searchPresenterImpl: StockSearchPresenterImplementation?
    var investmentAccount: WXInvestmentAccount?
    public typealias Listener = WXSearchNavigationListener

    public weak static var searchNavigateListener: Listener?

    public init(
        accountPresenter: AccountInvestmentPresenterImplementation,
        searchPresenter: StockSearchPresenterImplementation,
        market: WXMarket?,
        investmentAccount: WXInvestmentAccount?,
        tracker: WXTracking,
        searchNavigationListener: Listener?
    ) {
        accountPresenterImpl = accountPresenter
        searchPresenterImpl = searchPresenter
        stockMarket = market
        self.tracker = tracker
        WXSearchFlow.searchNavigateListener = searchNavigationListener
        StockTradeJourneyFactory.trackManager = tracker
    }

    public func build() -> UIViewController {
        if let stockMarket = stockMarket {
            searchPresenterImpl?.didEditSection(section: .market(market: stockMarket))
            accountPresenterImpl?.didEditSection(section: .market(market: stockMarket))
        }

        if let investmentAccount = investmentAccount {
            accountPresenterImpl?.didEditSection(section: .accounts(accounts: [investmentAccount]))
        }

        let viewController = PredictiveSearchViewControllerSingapore()
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        _viewControllers.append(WeakViewController(viewController))
        return viewController
    }

    public func dispose() {
        _viewControllers.removeAll()
        WXSearchFlow.searchNavigateListener = nil
    }
}
