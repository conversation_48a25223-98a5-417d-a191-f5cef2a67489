//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileCore
import RxCocoa
import RxSwift
import UIKit

open class WXTradeFlow {
    var orderAction: HSBCStockTradeOrderInstruction!
    var accountInvestmentPresenter: AccountInvestmentPresenterImplementation?
    var accountSettlementPresenter: AccountSettlementPresenterImplementation?
    var tradePresenter: StockTradePresenterImplementation?
    var customerDetailsPresenter: CustomerDetailsPresenterImplementation?
    var quotePresenter: StockQuotePresenterImplementation?
    var stockMarket: WXMarket!
    var stockCode: String!
    var productType: WXInvestmentProductType!
    var priorInvestmentAccount: WXInvestmentAccount?
    var priorSettlementAccount: WXSettlementAccount?
    var sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult!
    var tracker: WXTracking
    var overrideObject: OverrideObject?
    var quoteOverrideObject: QuoteOverrideObject?
    public typealias Listener = WXStockTradeNavigationListener

    public weak static var stockTradeNavigationListener: Listener?

    public var proposedPrice: Decimal?

    public init(
        orderAction: HSBCStockTradeOrderInstruction,
        accountInvestmentPresenter: AccountInvestmentPresenterImplementation?,
        accountSettlementPresenter: AccountSettlementPresenterImplementation?,
        tradePresenter: StockTradePresenterImplementation?,
        quotePresenter: StockQuotePresenterImplementation?,
        market: WXMarket,
        stockCode: String,
        productType: WXInvestmentProductType,
        overrideObject: OverrideObject? = nil,
        quoteOverrideObject: QuoteOverrideObject? = nil,
        proposedPrice: Decimal? = nil,
        tracker: WXTracking,
        priorInvestmentAccount: WXInvestmentAccount?,
        priorSettlementAccount: WXSettlementAccount?,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult,
        stockTradeNavigationListener: Listener?
    ) {
        self.orderAction = orderAction
        self.accountInvestmentPresenter = accountInvestmentPresenter
        self.accountSettlementPresenter = accountSettlementPresenter
        self.priorInvestmentAccount = priorInvestmentAccount
        self.tradePresenter = tradePresenter
        self.quotePresenter = quotePresenter
        stockMarket = market
        self.stockCode = stockCode
        self.productType = productType
        self.overrideObject = overrideObject
        self.quoteOverrideObject = quoteOverrideObject
        self.proposedPrice = proposedPrice
        self.sfcRegulatoryCheckingResult = sfcRegulatoryCheckingResult
        self.tracker = tracker
        WXTradeFlow.stockTradeNavigationListener = stockTradeNavigationListener
        StockTradeJourneyFactory.trackManager = tracker
    }

    public func build() -> UIViewController? {
        tradePresenter?.didEditSection(section: .instruction(instruction: orderAction))
        tradePresenter?.didEditSection(section: .orderType(type: nil))
        tradePresenter?.didEditSection(section: .quantity(quantity: nil))
        tradePresenter?.didEditSection(section: .proposedQuantity(nil))
        tradePresenter?.didEditSection(section: .settlementAccount(account: nil))
        tradePresenter?.didEditSection(section: .investmentAccount(account: nil))
        tradePresenter?.didEditSection(section: .goodUntil(date: nil))
        tradePresenter?.didEditSection(section: .investorCharacterizationCheckingWarnings(nil))
        tradePresenter?.didEditSection(section: .investorCharacterizationCheckingResult(nil))
        tradePresenter?.didEditSection(section: .acceptedInvestorTermsAndCondition(nil))

        // WXMA-9171: Reset selected investment account
        accountSettlementPresenter?.didEditSection(section: .selectedInvestmentAccount(account: nil))
        // reset

        tradePresenter?.didEditSection(section: .market(market: stockMarket))

        tradePresenter?.didEditSection(section: .stockCode(string: stockCode))
        tradePresenter?.didEditSection(section: .productType(type: productType))

        if let priorInvestmentAccount = self.priorInvestmentAccount {
            tradePresenter?.didEditSection(section: .investmentAccount(account: priorInvestmentAccount))
            accountSettlementPresenter?.didEditSection(section: .selectedInvestmentAccount(account: priorInvestmentAccount))
        }

        if let overrideObject = overrideObject {
            tradePresenter?.didEditSection(section: .overrideObject(object: overrideObject))
            if let transactionDate = overrideObject.transactionDate {
                quotePresenter?.didEditSection(section: .orderReceiveDate(date: transactionDate))
            }

            let priorInvestmentAccount = overrideObject.investmentAccount
            accountInvestmentPresenter?.didEditSection(section: .preselectPriorInvestmentAccount(priorInvestmentAccount: priorInvestmentAccount))

            let priorSettlementAccount = overrideObject.settlementAccount
            accountSettlementPresenter?.didEditSection(section: .preselectPriorSettlementAccount(priorSettlementAccount: priorSettlementAccount))
        }

        accountSettlementPresenter?.didEditSection(section: .market(market: stockMarket))

        accountInvestmentPresenter?.didEditSection(section: .market(market: stockMarket))
        accountInvestmentPresenter?.didEditSection(section: .sfcRegulatoryCheckingResult(result: sfcRegulatoryCheckingResult))

        tradePresenter?.attemptGetAvailableInvestmentAccounts()
        if let proposedPrice = self.proposedPrice {
            tradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: proposedPrice))
            tradePresenter?.didEditSection(section: .price(price: proposedPrice))
        }

        if let currency = overrideObject?.currency {
            tradePresenter?.didEditSection(section: .currency(currency: currency))
        }

        /*
         https://jira-digital.systems.uk.hsbc/browse/STM-7380
         [STMA2.0][AOS/IOS][HK][MDS_Down] Cancel review screen legal content is not align on IOS and AOS
         MDS down causing legal section mis behave, by skipping quote presenter, all data should only depends on order status details
         */
        quotePresenter?.didEditSection(section: .market(market: stockMarket))
        quotePresenter?.didEditSection(section: .stockCode(string: stockCode))
        quotePresenter?.didEditSection(section: .productType(type: productType))
        if let quoteOverrideObject = self.quoteOverrideObject {
            quotePresenter?.didEditSection(section: .overrideObject(overrideObject: quoteOverrideObject))
        }
        if ![.cancelBuy, .cancelSell].contains(orderAction) {
            quotePresenter?.didEditSection(section: .isDelay(isDelay: false)) // call isDelay to trigger attempt Quote
        } else {
            quotePresenter?.putDecimalPlaces()
        }

        if [.cancelBuy, .cancelSell].contains(orderAction) {
            let viewController = StockTradeJourneyFactory.getObjectFromSuffix(TradeOrderReviewScreenViewController.self)
            viewController.hidesBottomBarWhenPushed = true
            viewController.navigationItem.largeTitleDisplayMode = .never
            return viewController
        } else {
            let viewController =
                StockTradeJourneyFactory.getObjectFromSuffix(OrderDetailsInputScreenViewController.self)
            viewController.hidesBottomBarWhenPushed = true
            viewController.navigationItem.largeTitleDisplayMode = .never
            return viewController
        }
    }
}
