//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import UIKit

public protocol WXAddToWatchListNavigationListener: AnyObject {
    func addToWatchListCompletedFlow(flowBuilder: WXFlowBuilder)
    func addToWatchListNavigateClickDone(
        flowBuilder: WXFlowBuilder,
        market: WXMarket,
        watchlistsItem: WatchlistItem,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult?,
        watchlists: [WatchListInfo]
    )
}

open class WXAddToWatchListFlowBuilder: WXFlowBuilder {
    public var viewControllers: [WeakViewController] {
        _viewControllers
    }

    private var _viewControllers = [WeakViewController]()

    public var tracker: WXTracking

    public var market: WXMarket?

    public var watchlistItem: WatchlistItem?

    public var resolver: Resolver?

    public var watchlistPresenter: WatchlistPresenterImplementation?

    public init(
        market: WXMarket?,
        watchlistItem: WatchlistItem?,
        tracker: WXTracking,
        resolver: Resolver
    ) {
        let watchlistPresenter: WatchlistPresenterImplementation = resolver.resolve(name: "WXWatchListFlowBuilder.WatchlistPresenterImplementation")
        self.watchlistPresenter = watchlistPresenter
        self.tracker = tracker
        self.resolver = resolver
        self.market = market
        self.watchlistItem = watchlistItem
        resolver.register { watchlistPresenter }
    }

    public func dispose() {
        _viewControllers.removeAll()
    }

    public func build() -> UIViewController {
        var viewController = StockTradeJourneyFactory.getObjectFromSuffix(AddToWatchlistViewController.self)
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        viewController.setflowControllable(self)
        _viewControllers.append(WeakViewController(viewController))
        flowDidBegin()
        return viewController
    }

    public func flowDidBegin() {
        if let market = market {
            watchlistPresenter?.didEditSection(section: .selectedMarket(market: market))
        }

        if let watchlistItem = watchlistItem {
            watchlistPresenter?.didEditSection(section: .selectedWatchlistItem(watchlistItem: watchlistItem))
        }
    }
}
