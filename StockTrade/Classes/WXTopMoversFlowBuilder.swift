//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit

public protocol WXTopMoversNavigationListener: AnyObject {
    func completedFlow()
    func navigateToSearch(market: WXMarket)
    func navigateToQuotePage(
        market: WXMarket,
        stockCode: String,
        currency: WXCurrencyCode,
        productType: WXInvestmentProductType,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult?
    )
}

open class WXTopMoversFlowBuilder: WXFlowBuilder {
    public var viewControllers: [WeakViewController] {
        _viewControllers
    }

    private var _viewControllers = [WeakViewController]()

    public var tracker: WXTracking

    public var topMoversPresenter: TopMoversPresenterImplementation?

    public weak var topMoversNavigateListener: WXTopMoversNavigationListener?

    public init(
        topMoversPresenter: TopMoversPresenterImplementation,
        market: WXMarket?,
        tracker: WXTracking,
        topMoversNavigateListener: WXTopMoversNavigationListener?
    ) {
        self.topMoversPresenter = topMoversPresenter
        self.tracker = tracker
        if let market = market {
            topMoversPresenter.didEditSection(section: .selectedMarket(market: market))
        }
        self.topMoversNavigateListener = topMoversNavigateListener
    }

    public func dispose() {
        _viewControllers.removeAll()
    }

    public func build() -> UIViewController {
        let viewController = TopMoversViewControllerSingapore()
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        viewController.flowBuilder = self
        _viewControllers.append(WeakViewController(viewController))
        return viewController
    }
}
