//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import UIKit

public protocol WXWatchListNavigationListener: AnyObject {
    func watchListCompletedFlow()
    func watchListShowMoreClick()
    func watchListNavigateToSearch(market: WXMarket)
    func watchListNavigateToQuotePage(
        market: WXMarket,
        watchlistsItem: WatchlistItem,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult?
    )
}

open class WXWatchListFlowBuilder: WXFlowBuilder {
    public var viewControllers: [WeakViewController] {
        _viewControllers
    }

    private var _viewControllers = [WeakViewController]()

    public var tracker: WXTracking

    public var market: WXMarket?

    public var resolver: Resolver?

    public var watchlistPresenter: WatchlistPresenterImplementation?

    public init(
        market: WXMarket?,
        tracker: WXTracking,
        resolver: Resolver
    ) {
        let watchlistPresenter: WatchlistPresenterImplementation = resolver.resolve(name: "WXWatchListFlowBuilder.WatchlistPresenterImplementation")
        self.watchlistPresenter = watchlistPresenter
        self.tracker = tracker
        self.resolver = resolver
        self.market = market
        resolver.register { watchlistPresenter }
    }

    public func dispose() {
        resolver?.cache.reset()
        watchlistPresenter?.dispose()
        _viewControllers.removeAll()
    }

    public func build() -> UIViewController {
        var viewController =
            StockTradeJourneyFactory.getObjectFromSuffix(WatchlistViewController.self)
        viewController.hidesBottomBarWhenPushed = true
        viewController.navigationItem.largeTitleDisplayMode = .never
        viewController.setflowControllable(self)
        _viewControllers.append(WeakViewController(viewController))
        return viewController
    }

    public func flowDidBegin() {
        if let market = market {
            watchlistPresenter?.didEditSection(section: .selectedMarket(market: market))
        }
    }
}
