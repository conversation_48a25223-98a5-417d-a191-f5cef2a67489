//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift

class WXRxStandardInputFieldDelegateProxy: DelegateProxy<StandardInputField, StandardInputFieldDelegate>, DelegateProxyType, StandardInputFieldDelegate {
    fileprivate let _didBeginEdit: PublishSubject<StandardInputField> = PublishSubject()

    fileprivate let _didEndEditing: PublishSubject<StandardInputField> = PublishSubject()

    fileprivate let _didChange: PublishSubject<StandardInputField> = PublishSubject()
    fileprivate let _didChangeInRange: PublishSubject<(StandardInputField, NSRange, String)> = PublishSubject()

    fileprivate let _shouldChangeTextInRange: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)

    init(textField: StandardInputField) {
        super.init(parentObject: textField, delegateProxy: WXRxStandardInputFieldDelegateProxy.self)
    }

    static func registerKnownImplementations() {
        register { WXRxStandardInputFieldDelegateProxy(textField: $0) }
    }

    static func currentDelegate(for object: StandardInputField) -> StandardInputFieldDelegate? {
        object.delegate
    }

    static func setCurrentDelegate(_ delegate: StandardInputFieldDelegate?, to object: StandardInputField) {
        object.delegate = delegate
    }

    // MARK: - handle delegate

    func standardInputFieldDidBeginEditing(_ field: StandardInputField) {
        _didBeginEdit.onNext(field)
    }

    func standardInputFieldDidEndEditing(_ field: StandardInputField) {
        _didEndEditing.onNext(field)
    }

    func standardInputField(_ field: StandardInputField, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        _didChangeInRange.onNext((field, range, text))
        if let value = _shouldChangeTextInRange.value {
            return value
        }
        return true
    }

    func standardInputFieldDidChange(_ field: StandardInputField) {
        _didChange.onNext(field)
    }
}

extension Reactive where Base: StandardInputField {
    var delegate: WXRxStandardInputFieldDelegateProxy {
        WXRxStandardInputFieldDelegateProxy.proxy(for: base)
    }

    var didBeginEditing: Observable<StandardInputField> {
        delegate._didBeginEdit
    }

    var didEndEditing: Observable<StandardInputField> {
        delegate._didEndEditing
    }

    var didChange: Observable<StandardInputField> {
        delegate._didChange
    }

    var didChangeInRange: Observable<(StandardInputField, NSRange, String)> {
        delegate._didChangeInRange
    }

    var shouldChangeTextInRange: Binder<Bool?> {
        Binder(delegate) { del, value in
            del._shouldChangeTextInRange.accept(value)
        }
    }
}
