//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

enum PresentationDirection {
    case left
    case top
    case right
    case bottom
}

class WXPanModalViewPresentationController: UIPresentationController {
    // 1

    // MARK: - Properties

    private var viewTranslation = CGPoint(x: 0, y: 0)
    private var direction: PresentationDirection

    override var frameOfPresentedViewInContainerView: CGRect {
        // 1
        var frame: CGRect = .zero
        frame.size = size(
            forChildContentContainer: presentedViewController,
            withParentContainerSize: containerView!.bounds.size
        )

        // 2
        switch direction {
        case .right:
            frame.origin.x = containerView!.frame.width * (1.0 / 3.0)
        case .bottom:
            frame.origin.y = containerView!.frame.height * (1.0 / 3.0)
        default:
            frame.origin = .zero
        }
        return frame
    }

    private lazy var dimmingView: UIView = {
        let dimmingView = UIView()
        dimmingView.translatesAutoresizingMaskIntoConstraints = false
        dimmingView.backgroundColor = ThemeManager.shared.currentTheme.colors.dimmedBackground
        dimmingView.alpha = 0.0
        return dimmingView
    }()

    // 2
    init(
        presentedViewController: UIViewController,
        presenting presentingViewController: UIViewController?,
        direction: PresentationDirection
    ) {
        self.direction = direction

        // 3
        super.init(
            presentedViewController: presentedViewController,
            presenting: presentingViewController
        )

        let recognizer = UITapGestureRecognizer(
            target: self,
            action: #selector(handleTap(recognizer:))
        )
        dimmingView.addGestureRecognizer(recognizer)

        let panGestureRecognizer = UIPanGestureRecognizer(
            target: self,
            action: #selector(handleDragging)
        )
        dimmingView.addGestureRecognizer(panGestureRecognizer)
    }

    override func containerViewWillLayoutSubviews() {
        presentedView?.frame = frameOfPresentedViewInContainerView
    }

    @objc private func handleDragging(sender: UIPanGestureRecognizer) {
        // The rest of the code we will write here
        switch sender.state {
        case .changed:
            viewTranslation = sender.translation(in: presentedViewController.view)
            if viewTranslation.y > 0 {
                UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 1, options: .curveEaseOut, animations: { [weak self] in
                    guard let self = self else { return }
                    self.presentedViewController.view.transform = CGAffineTransform(translationX: 0, y: self.viewTranslation.y)
                })
            } else {
                viewTranslation.y = 0
            }
        case .ended:
            if viewTranslation.y < 200 {
                UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 1, options: .curveEaseOut, animations: { [weak self] in
                    guard let self = self else { return }
                    self.presentedViewController.view.transform = .identity
                    self.viewTranslation.y = 0
                })
            } else {
                handleDragOrTapToDismiss()
            }
        default:
            break
        }
    }

    @objc func handleTap(recognizer: UITapGestureRecognizer) {
        handleDragOrTapToDismiss()
    }

    func handleDragOrTapToDismiss() {
        presentedViewController.dismiss(animated: true, completion: nil)
    }

    override func presentationTransitionWillBegin() {
        // 1
        containerView?.insertSubview(dimmingView, at: 0)

        // 2
        NSLayoutConstraint.activate(
            NSLayoutConstraint.constraints(
                withVisualFormat: "V:|[dimmingView]|",
                options: [],
                metrics: nil,
                views: ["dimmingView": dimmingView]
            ))
        NSLayoutConstraint.activate(
            NSLayoutConstraint.constraints(
                withVisualFormat: "H:|[dimmingView]|",
                options: [],
                metrics: nil,
                views: ["dimmingView": dimmingView]
            ))

        // 3
        guard let coordinator = presentedViewController.transitionCoordinator else {
            dimmingView.alpha = 1.0
            return
        }

        coordinator.animate(alongsideTransition: { _ in
            self.dimmingView.alpha = 1.0
        })
    }

    override func dismissalTransitionWillBegin() {
        guard let coordinator = presentedViewController.transitionCoordinator else {
            dimmingView.alpha = 0.0
            return
        }

        coordinator.animate(alongsideTransition: { _ in
            self.dimmingView.alpha = 0.0
        })
    }

    override func size(
        forChildContentContainer container: UIContentContainer,
        withParentContainerSize parentSize: CGSize
    ) -> CGSize {
        switch direction {
        case .left, .right:
            return CGSize(width: parentSize.width * (2.0 / 3.0), height: parentSize.height)
        case .bottom, .top:
            return CGSize(width: parentSize.width, height: parentSize.height * (2.0 / 3.0))
        }
    }
}

class WXFormPanModalViewControllerTransitioningDelegate: NSObject, UIViewControllerTransitioningDelegate {
    /**
     Returns a modal presentation animator configured for the presenting state
     */
    public func animationController(forPresented presented: UIViewController, presenting: UIViewController, source: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        nil
    }

    /**
     Returns a modal presentation animator configured for the dismissing state
     */
    public func animationController(forDismissed dismissed: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        nil
    }

    public func presentationController(
        forPresented presented: UIViewController,
        presenting: UIViewController?,
        source: UIViewController
    ) -> UIPresentationController? {
        let presentationController = WXPanModalViewPresentationController(
            presentedViewController: presented,
            presenting: presenting,
            direction: .bottom
        )
        return presentationController
    }
}
