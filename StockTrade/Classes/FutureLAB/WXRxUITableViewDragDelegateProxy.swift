//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit
// this creates a delegate proxy class for the table view. Learn more in this article: https://danielt1263.medium.com/convert-a-swift-delegate-to-rxswift-observables-f52afe77f8d6
// https://stackoverflow.com/questions/68954343/rxswift-with-uitableview-how-to-change-swipe-action-based-on-observable
class UITableViewDelegateProxy: DelegateProxy<UITableView, UITableViewDelegate>, DelegateProxyType, UITableViewDelegate {
    static func currentDelegate(for object: UITableView) -> UITableViewDelegate? {
        object.delegate
    }

    static func setCurrentDelegate(_ delegate: UITableViewDelegate?, to object: UITableView) {
        object.delegate = delegate
    }

    public static func registerKnownImplementations() {
        register { UITableViewDelegateProxy(parentObject: $0) }
    }

    init(parentObject: UITableView) {
        super.init(
            parentObject: parentObject,
            delegateProxy: UITableViewDelegateProxy.self
        )
    }

    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        relay.value[indexPath]
    }

    fileprivate let relay = BehaviorRelay<[IndexPath: UISwipeActionsConfiguration]>(value: [:])
}

class WXRxUITableViewDragDelegateProxy: DelegateProxy<UITableView, UITableViewDragDelegate>, DelegateProxyType, UITableViewDragDelegate {
    // outbound
    fileprivate let _didBeginDragSession: PublishSubject<(UITableView, UIDragSession, IndexPath)> = PublishSubject()

    // inbound
    fileprivate let _itemsForDragSession: BehaviorRelay<[UIDragItem]?> = BehaviorRelay(value: nil)

    init(tableView: UITableView) {
        super.init(parentObject: tableView, delegateProxy: WXRxUITableViewDragDelegateProxy.self)
    }

    static func registerKnownImplementations() {
        register { WXRxUITableViewDragDelegateProxy(tableView: $0) }
    }

    static func currentDelegate(for object: UITableView) -> UITableViewDragDelegate? {
        object.dragDelegate
    }

    static func setCurrentDelegate(_ delegate: UITableViewDragDelegate?, to object: UITableView) {
        object.dragDelegate = delegate
    }

    // MARK: - handle delegate

    func tableView(_ tableView: UITableView, itemsForBeginning session: UIDragSession, at indexPath: IndexPath) -> [UIDragItem] {
        _didBeginDragSession.onNext((tableView, session, indexPath))
        if let value = _itemsForDragSession.value {
            return value
        }
        return []
    }
}

class WXRxUITableViewDropDelegateProxy: DelegateProxy<UITableView, UITableViewDropDelegate>, DelegateProxyType, UITableViewDropDelegate {
    // outbound
    fileprivate let _didPerformDropWithCoordinator: PublishSubject<(UITableView, UITableViewDropCoordinator)> = PublishSubject()
    // inbound

    init(tableView: UITableView) {
        super.init(parentObject: tableView, delegateProxy: WXRxUITableViewDropDelegateProxy.self)
    }

    static func registerKnownImplementations() {
        register { WXRxUITableViewDropDelegateProxy(tableView: $0) }
    }

    static func currentDelegate(for object: UITableView) -> UITableViewDropDelegate? {
        object.dropDelegate
    }

    static func setCurrentDelegate(_ delegate: UITableViewDropDelegate?, to object: UITableView) {
        object.dropDelegate = delegate
    }

    // MARK: - handle delegate

    public func tableView(_ tableView: UITableView, performDropWith coordinator: UITableViewDropCoordinator) {
        _didPerformDropWithCoordinator.onNext((tableView, coordinator))
        // TODO: - UITableViewDropCoordinator TBC
    }
}

extension Reactive where Base: UITableView {
    var dragDelegate: WXRxUITableViewDragDelegateProxy {
        WXRxUITableViewDragDelegateProxy.proxy(for: base)
    }

    var dropDelegate: WXRxUITableViewDropDelegateProxy {
        WXRxUITableViewDropDelegateProxy.proxy(for: base)
    }

    var didBeginDragSession: Observable<(UITableView, UIDragSession, IndexPath)> {
        dragDelegate._didBeginDragSession
    }

    var itemsForDragSession: Binder<[UIDragItem]?> {
        Binder(dragDelegate) { delegate, value in
            dragDelegate._itemsForDragSession.accept(value)
        }
    }

    var didPerformDropWithCoordinator: Observable<(UITableView, UITableViewDropCoordinator)> {
        dropDelegate._didPerformDropWithCoordinator
    }

    var delegate: UITableViewDelegateProxy {
        UITableViewDelegateProxy.proxy(for: base)
    }

    var trailingSwipeActionsConfigurationForRowAt: Binder<[IndexPath: UISwipeActionsConfiguration]> {
        Binder(delegate) { del, value in
            del.relay.accept(value)
        }
    }
}
