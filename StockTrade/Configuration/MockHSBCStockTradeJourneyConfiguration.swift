//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation
// import wealthxcore

public struct MockHSBCStockTradeJourneyConfiguration {
    public enum HSBCConfigOrderInstruction: Int {
        case buy, sell, search, quote, orderStatus, iao, topMovers, watchlist, addToWatchlist
    }

    public enum OTPKey: Int {
        case WealthX,
            STMA

        public func key() -> String {
            switch self {
            case .WealthX:
                return "7accujv8c1p70dfd6okrr3blu4"
            default:
                return "c9c55obu72tp3k31u5kr0nc4sn"
            }
        }
    }

    public enum HSBCConfigCountry: Int {
        case hk
        case sg
    }

    public enum HSBCConfigMarket: Int {
        case hk
        case us
        case cn
        case can
        case sg

        public func stockExchangeMarket() -> WXMarket {
            switch self {
            case .hk:
                return .hongKong
            case .cn:
                return .china
            case .us:
                return .america
            case .sg:
                return .singapore
            default:
                return .others
            }
        }
    }

    public enum HSBCAccountPriceQuote: Int {
        case unlimited,
            negative,
            nonNegative
    }

    public enum StockTrend: Int {
        case up,
            down,
            none
    }

    public enum StockTradingHours: Int {
        case orderInputPeriod, /* 9:00:00 - 9:15:00 */
            preOrderMatchingPeriod, /* 9:15:01 - 9:20:00 */
            orderMatchingPeriod, /* 9:20:01 - 9:29:59 */
            morningTradingSession, /* 9:30:00 - 12:00:00 */
            extendedMorningSession, /* 12:01:00 - 12:59:59 */
            afternoonTradingSession, /* 13:00:00 - 15:59:59 */
            extendedHours, /* 16:01:00 - 16:10:00 */
            nonTradingHours
    }

    //   MARK: follow WXLocaleType values   ["en", "zh-Hans", "zh-Hant", "en_SG"]

    public enum Language: Int {
        case en, zhHans, zhHant, enSG
    }

    // TODO: debug only remove it before commit changes
    public static var username: String = ""
    public static var otpKey = OTPKey.STMA
    public static var orderInstruction: HSBCConfigOrderInstruction = .buy
    public static var country: HSBCConfigCountry = .hk
    public static var stockMarket: HSBCConfigMarket = .hk
    public static var stockCode: String = "00005"
    public static var productName: String = "HSBC Holding plc"
    public static var productType: WXInvestmentProductType = .stock
    public static var accountType: HSBCAccountPriceQuote = .unlimited
    public static var quoteCount: Int = 205
    public static var quoteCountThreshold: Int = 200
    public static var stockTrend: StockTrend = .up
    public static var stockTradingHours: StockTradingHours = .morningTradingSession
    public static var language: Language = localLanguage()
    public static var numOfSecurityAccounts: Int = 1
    public static var numOfSettlementAccounts: Int = 1
    public static var lotSize: Int = 400
    public static var buyingPower: String = "********"
    public static var isRiskyStock: String = "false"
    public static var casPriceUpperLimit = Decimal(string: "120.24")!
    public static var casPriceLowerLimit = Decimal(string: "60.24")!
    public static var sfcChecking: SFCRegulatoryCheckingResult = .bothAreValid

    // local language
    static func localLanguage() -> Language {
        if let appleLanguages = UserDefaults.standard.object(forKey: "AppleLanguages") as? [String],
            let localization = appleLanguages.first {
            if localization.contains(WXLocaleType.SimplifiedChinese.rawValue) {
                return .zhHans
            } else if localization.contains(WXLocaleType.TraditionalChinese.rawValue) {
                return .zhHant
            } else if localization.contains(WXLocaleType.SingaporeEnglish.rawValue) {
                return .enSG
            }
            return .en
        }
        return .en
    }
}
