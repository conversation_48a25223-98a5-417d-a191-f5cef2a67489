/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class SellFromAccountLoadingIndicator: WXViewWithLoadingIndicator {
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    public let _investmentAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)
    public let _investmentAccounts: BehaviorRelay<[WXInvestmentAccount]> = BehaviorRelay(value: [])
    public let _sellFromSectionStatus: BehaviorRelay<SectionStatus> = BehaviorRelay(value: .notLoad)
    public let _showLoadingIndicator: BehaviorRelay<(Bool, PresenterRequestType)?> = BehaviorRelay(value: nil)

    override open func initializeVariables() {
        super.initializeVariables()
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
        _sellFromSectionStatus.accept(.loading)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()
        bindOverridableData()
    }

    open func bindOverridableData() {
        BehaviorRelay.combineLatest(
            _sellFromSectionStatus.distinctUntilChanged(),
            _investmentAccounts.distinctUntilChanged(),
            _investmentAccount.distinctUntilChanged()
        ).map { sellFromSectionStatus, investmentAccounts, _investmentAccount -> SectionStatus in
            if let _ = _investmentAccount {
                return .loaded
            } else if !investmentAccounts.isEmpty {
                return .loaded
            } else {
                return .notLoad
            }
        }.asDriver(onErrorJustReturn: .notLoad).flatMap(ignoreNil).distinctUntilChanged().asObservable().observe(on: MainScheduler.instance).subscribe(onNext: { [weak self] status in
            guard let self = self else { return }
            // FIXEDME : error indicator ? reload ?
            switch status {
            case .loading, .notLoad, .error:
                self.viewModel.inputs.updateLoadingIndicatorVisibility(visible: true)
                self.viewModel.inputs.updateChildViewsInteractivity(interactable: false)
            default:
                self.viewModel.inputs.updateLoadingIndicatorVisibility(visible: false)
                self.viewModel.inputs.updateChildViewsInteractivity(interactable: true)
            }
        }).disposed(by: bag)
    }

    override open func setupViews() {
        super.setupViews()
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
    }

    open func didUpdateInvestmentAccount(account: WXInvestmentAccount, tradableQuantity: Int64) {
        _investmentAccount.accept(account)
    }

    open func didUpdateAvailableInvestmentAccountSection(accounts: [WXInvestmentAccount]) {
        _investmentAccounts.accept(accounts)
    }

    open func didReceivedShowLoadingIndicator(_ type: PresenterRequestType) {
        _showLoadingIndicator.accept((true, type))
    }

    open func didReceivedHideLoadingIndicator(_ type: PresenterRequestType) {
        _showLoadingIndicator.accept((false, type))
    }
}

extension SellFromAccountLoadingIndicator: StockTradeView {
    public func showLoadingIndicator(_ type: PresenterRequestType) {
        didReceivedShowLoadingIndicator(type)
    }

    public func hideLoadingIndicator(_ type: PresenterRequestType) {
        didReceivedHideLoadingIndicator(type)
    }

    public func updateInvestmentAccountSection(account: WXInvestmentAccount, tradableQuantity: Int64, error: HSBCErrorCode) {
        didUpdateInvestmentAccount(account: account, tradableQuantity: tradableQuantity)
    }

    public func updateAvailableInvestmentAccountsSection(accounts: [[WXInvestmentAccount: Int64]], error: HSBCErrorCode) {
        didUpdateAvailableInvestmentAccountSection(accounts: accounts.compactMap { $0.keys.first })
    }
}
