//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit

class PICSConsentFormErrorScreen: WXFullScreenErrorMessage, WXViewControllable {
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    weak var viewControllable: UIViewController?

    var _errorCode: BehaviorRelay<HSBCErrorCode?> = BehaviorRelay(value: nil)
    var errorCode: Driver<HSBCErrorCode?> {
        _errorCode.asDriver().distinctUntilChanged()
    }

    var _presenterRequestType: BehaviorRelay<PresenterRequestType?> = BehaviorRelay(value: nil)
    var presenterRequestType: Driver<PresenterRequestType?> {
        _presenterRequestType.asDriver().distinctUntilChanged()
    }

    var _warnings: BehaviorRelay<[HSBCWarning]?> = BehaviorRelay(value: nil)
    var warnings: Driver<[HSBCWarning]?> {
        _warnings.asDriver().distinctUntilChanged()
    }

    var _primaryButtonEnable: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    var _hideLoadingIndicator: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    var _showLoadingIndicator: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    func setViewControllable(_ viewControllable: UIViewController) {
        self.viewControllable = viewControllable
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        bindViewData()
    }

    private func bindViewData() {
        viewModel?.outputs.didClickDismissButton.asObservable().subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            self.viewModel.updateModuleVisibility(false)
        }).disposed(by: bag)

        Driver.combineLatest(
            presenterRequestType.flatMap(ignoreNil),
            errorCode.flatMap(ignoreNil),
            warnings.flatMap(ignoreNil)
        ).asObservable().subscribe(onNext: { [weak self] type, errorCode, warnings in
            guard let self = self,
                type == .PRESENTER_REQUEST_UPDATE_PICS_CONSENT_CHARACTERIZATION else {
                return
            }
            let picsWarnings = warnings.filter {
                if let code = $0.code {
                    return code.starts(with: "VLD")
                }
                return false
            }
            if let viewControllable = self.viewControllable,
                errorCode == .jointAccountNotSigned {
                viewControllable.dismiss(animated: true, completion: nil)
                self.stockTradePresenter.didClosePicsConsentCharacterization()
            }
            guard let viewControllable = self.viewControllable,
                errorCode == .jointAccountNotSigned else {
                self.header = ErrorMessageStrings.generalErrorMessageHeader()
                self.body = ErrorMessageStrings.generalErrorMessageBody() + String(format: ErrorMessageStrings.errorCodeInfo(), picsWarnings.first?.code ?? String(errorCode.rawValue))
                self.dismissButtonTitle = ErrorMessageStrings.messageCTA()
                self.viewModel.inputs.updateModuleVisibility(true)
                self._errorCode.accept(nil)
                self._presenterRequestType.accept(nil)
                self._warnings.accept(nil)
                return
            }
            viewControllable.dismiss(animated: false, completion: nil)
            self.stockTradePresenter.didClosePicsConsentCharacterization()
        }).disposed(by: bag)
    }

    override func setupTealiumTaggings() {}

    func didReciveErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]) {
        _warnings.accept(warnings)
        _errorCode.accept(errorCode)
        _presenterRequestType.accept(type)
    }

    func didHideLoadingIndicator(_ type: PresenterRequestType) {
        _hideLoadingIndicator.accept(())
    }

    func didShowLoadingIndicator(_ type: PresenterRequestType) {
        _showLoadingIndicator.accept(())
    }
}

extension PICSConsentFormErrorScreen: StockTradeView {
    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        if errorCode == .cancelRequest {
            return
        }
        didReciveErrorMessage(type, errorCode: errorCode, warnings: warnings ?? [])
    }

    func showLoadingIndicator(_ type: PresenterRequestType) {
        guard type == .PRESENTER_REQUEST_UPDATE_PICS_CONSENT_CHARACTERIZATION else {
            return
        }
        didShowLoadingIndicator(type)
    }

    func hideLoadingIndicator(_ type: PresenterRequestType) {
        guard type == .PRESENTER_REQUEST_UPDATE_PICS_CONSENT_CHARACTERIZATION else {
            return
        }
        didHideLoadingIndicator(type)
    }
}
