//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class PICSConsentFormScrollView: WXFormViewWithSecondaryButton, WXViewControllable {
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    public weak var viewControllable: UIViewController?
    var views: [WXBaseView]!
    var textView: PICSConsentFormTextView!
    var dataPrivacyNoticeItem: PICSConsentFormDataPrivacyNoticeItem!
    var checkBoxItem: PICSConsentFormCheckBoxItem!
    var picsConsentFormErrorScreen: PICSConsentFormErrorScreen!

    var _primaryButtonEnable: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    var _acceptedPics: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    var _customerEligibilityWarnings: BehaviorRelay<[HSBCWarning]?> = BehaviorRelay(value: nil)
    public var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.captionMedium
        label.accessibilityTraits = .header
        label.isAccessibilityElement = true
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.numberOfLines = 0
        return label
    }()

    private lazy var scrollView: WXScrollView = {
        let scrollView = WXScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        return scrollView
    }()

    private lazy var scrollStackView: UIStackView = {
        let view = UIStackView()
        view.axis = .vertical
        view.spacing = 1
        view.distribution = .fill
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    override open func initializeVariables() {
        super.initializeVariables()
        titleLabel.text = localizedString("wx_pics_consent_header")
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
        textView = PICSConsentFormTextView()
        textView.setViewControllable(viewControllable)
        dataPrivacyNoticeItem = StockTradeJourneyFactory.getObjectFromSuffix(PICSConsentFormDataPrivacyNoticeItem.self)
        dataPrivacyNoticeItem.setViewControllable(viewControllable)
        checkBoxItem = StockTradeJourneyFactory.getObjectFromSuffix(PICSConsentFormCheckBoxItem.self)
        picsConsentFormErrorScreen = StockTradeJourneyFactory.getObjectFromSuffix(PICSConsentFormErrorScreen.self)
        picsConsentFormErrorScreen.setViewControllable(viewControllable)
        picsConsentFormErrorScreen.setListener(self)
        defaultFormSecondaryButtonTitle = localizedString("wx_pics_consent_disagree_and_cannot_proceed")
        defaultFormButtonTitle = localizedString("wx_pics_consent_agree_and_continue")
        defaultButtonState = .disabled
    }

    override open func setupViews() {
        super.setupViews()
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        translatesAutoresizingMaskIntoConstraints = false

        stackView.addArrangedSubview(scrollView)
        scrollView.addSubview(scrollStackView)

        scrollStackView.addArrangedSubview(titleLabel)
        scrollStackView.addArrangedSubview(textView)
        scrollStackView.addArrangedSubview(dataPrivacyNoticeItem)
        scrollStackView.addArrangedSubview(checkBoxItem)

        NSLayoutConstraint.activate([
            scrollStackView.centerXAnchor.constraint(equalTo: scrollView.centerXAnchor),
            scrollStackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: -8),
            scrollStackView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            scrollStackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: scrollStackView.leadingAnchor, constant: 16),
            titleLabel.topAnchor.constraint(equalTo: scrollStackView.topAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(equalTo: scrollStackView.trailingAnchor, constant: -16)
        ])
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
        views = [
            textView,
            dataPrivacyNoticeItem,
            checkBoxItem,
            picsConsentFormErrorScreen
        ]
        views.forEach { $0.viewDidLoad() }
    }

    override open func viewDidAppear() {
        super.viewDidAppear()
        for view in views {
            view.viewDidAppear()
        }
    }

    override open func viewWillDisappear() {
        super.viewWillDisappear()
        for view in views {
            view.viewWillDisappear()
        }
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
        views.forEach { $0.viewDeinit() }
    }

    override open func bindViewModel() {
        super.bindViewModel()
    }

    override open func bindViewData() {
        _primaryButtonEnable.flatMap(ignoreNil).distinctUntilChanged().subscribe(onNext: { [weak self] clicked in
            guard let self = self else {
                return
            }
            self.viewModel.inputs.updateModuleState(clicked ? .enabled : .disabled)
        }).disposed(by: bag)

        viewModel.outputs.didTapButton.asObservable().throttle(.milliseconds(3000), latest: false, scheduler: MainScheduler.instance).subscribe(onNext: { [weak self] _ in
            guard let self = self else {
                return
            }
            self.viewModel.inputs.updateModuleState(.loading(announcement: "loading"))
            self.stockTradePresenter.attemptUpdatePicsConsentCharacterization(view: self)
        }).disposed(by: bag)

        viewModel.outputs.didTapSecondaryButton.asObservable().throttle(.milliseconds(300), latest: false, scheduler: MainScheduler.instance).subscribe(onNext: { [weak self] _ in
            guard let self = self,
                let viewControllable = self.viewControllable else {
                return
            }
            self.stockTradePresenter.didClosePicsConsentCharacterization()
            viewControllable.dismiss(animated: true, completion: nil)
        }).disposed(by: bag)

        _acceptedPics.distinctUntilChanged().flatMap(ignoreNil).withLatestFrom(
            _customerEligibilityWarnings,
            resultSelector: { ($0, $1) }
        ).subscribe(onNext: { [weak self] accepted, customerEligibilityWarnings in
            guard let self = self,
                accepted == true,
                let viewControllable = self.viewControllable else {
                return
            }
            viewControllable.dismiss(animated: true, completion: nil)
        }).disposed(by: bag)
    }

    override open func setupAccessibilityIndentifiers() {
        accessibilityIdentifier = "pics_scroll_view"
        titleLabel.accessibilityIdentifier = "pics_scroll_view_header"
    }

    func didUpdateIsCustomerPicsAccept(_ isAccept: Bool) {
        _primaryButtonEnable.accept(isAccept)
    }

    func didUpdatePicsConsentCharacterization(_ accept: Bool) {
        _acceptedPics.accept(accept)
        guard accept else { return }
        viewModel.inputs.updateModuleState(.success(announcement: "success", onAnimationCompletionHandler: {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                guard let self = self else { return }
                self.viewModel.inputs.updateModuleState(.enabled)
            }
        }))
    }

    func didUpdateCustomerEligibilitySection(_ isCustomerEligible: Bool?, _ isMobileValid: Bool?, _ isEmailValid: Bool?, _ customerEligibilityWarnings: [HSBCWarning]?) {
        _customerEligibilityWarnings.accept(customerEligibilityWarnings)
    }

    func didUpdateInstructionSection(_ instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }
}

extension PICSConsentFormScrollView: StockTradeView {
    public func updateCustomerEligibilitySection(isCustomerEligible: Bool?, isMobileValid: Bool?, isEmailValid: Bool?, customerEligibilityWarnings: [HSBCWarning]?) {
        didUpdateCustomerEligibilitySection(isCustomerEligible, isMobileValid, isEmailValid, customerEligibilityWarnings)
    }

    public func updateIsCustomerPicsAccept(_ isAccept: Bool) {
        didUpdateIsCustomerPicsAccept(isAccept)
    }

    public func updatePicsConsentCharacterization(_ accept: Bool) {
        didUpdatePicsConsentCharacterization(accept)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction)
    }
}

extension PICSConsentFormScrollView: WXFullScreenErrorMessageDelegate {
    func showFullScreenErrorMessage(_ module: WXFullScreenErrorMessage, _ viewController: UIViewController?) {
        guard let viewController = viewController, let viewControllable = viewControllable, viewControllable.isVisible(), module.isKind(of: PICSConsentFormErrorScreen.self) else {
            return
        }
        viewModel.inputs.updateModuleState(.enabled)
        viewControllable.present(viewController, animated: true, completion: nil)
    }

    func hideFullScreenErrorMessage(_ module: WXFullScreenErrorMessage, _ viewController: UIViewController?) {
        guard let viewController = viewController, viewController.isVisible(), module.isKind(of: PICSConsentFormErrorScreen.self) else {
            return
        }
        viewController.dismiss(animated: true, completion: nil)
    }
}
