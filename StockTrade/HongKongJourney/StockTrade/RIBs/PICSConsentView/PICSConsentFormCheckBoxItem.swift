//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PICSConsentFormCheckBoxItem: WXCheckBoxItem {
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "wx_pics_consent_agree_notice"
        checkboxButton.accessibilityIdentifier = "wx_pics_consent_agree_notice_check_box"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override func setupViews() {
        super.setupViews()
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        let separaterView = UIView()
        separaterView.backgroundColor = ThemeManager.shared.currentTheme.colors.divider
        separaterView.translatesAutoresizingMaskIntoConstraints = false

        let separaterViewBottom = UIView()
        separaterViewBottom.backgroundColor = ThemeManager.shared.currentTheme.colors.divider
        separaterViewBottom.translatesAutoresizingMaskIntoConstraints = false

        addSubview(separaterView)
        addSubview(separaterViewBottom)

        NSLayoutConstraint.activate([
            separaterView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: -16),
            separaterView.topAnchor.constraint(equalTo: topAnchor),
            separaterView.trailingAnchor.constraint(equalTo: trailingAnchor),
            separaterView.heightAnchor.constraint(equalToConstant: 1.0),
            separaterViewBottom.leadingAnchor.constraint(equalTo: leadingAnchor, constant: -16),
            separaterViewBottom.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -8),
            separaterViewBottom.trailingAnchor.constraint(equalTo: trailingAnchor),
            separaterViewBottom.heightAnchor.constraint(equalToConstant: 1.0)
        ])
    }

    override func bindViewModel() {
        super.bindViewModel()
    }

    override func bindViewData() {
        viewModel.inputs.updateMessage(message: localizedString("wx_pics_consent_agree_notice"))
        viewModel.outputs.checkboxClicked.asObservable().subscribe(onNext: { [weak self] checkboxClicked in
            guard let self = self else {
                return
            }
            self.stockTradePresenter.didEditSection(section: .isCustomerPicsAccept(checkboxClicked))
        }).disposed(by: bag)
    }
}

extension PICSConsentFormCheckBoxItem: StockTradeView {}
