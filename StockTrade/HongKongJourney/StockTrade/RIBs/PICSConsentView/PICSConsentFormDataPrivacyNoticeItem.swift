//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PICSConsentFormDataPrivacyNoticeItem: WXClickableListItemWithLeadingLabelAndTrailingLabelIcon, WXViewControllable {
    weak var viewControllable: UIViewController?

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()

        isAccessibilityElement = false
        listItem.isAccessibilityElement = true
        listItem.accessibilityTraits = .button
        listItem.accessibilityIdentifier = "pics_data_privacy_notice_view"
        Driver.combineLatest(
            viewModel.outputs.title,
            viewModel.outputs.trailingLabel
        ).asObservable().subscribe(onNext: { [weak self] title, value in
            guard let self = self else { return }
            self.listItem.accessibilityLabel = String(format: "%@", title)
            self.listItem.accessibilityValue = String(format: "%@", value)
        }).disposed(by: bag)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
    }

    override func viewDeinit() {
        super.viewDeinit()
    }

    override func setupViews() {
        super.setupViews()
        NSLayoutConstraint.activate([
            listItem.leadingAnchor.constraint(equalTo: leadingAnchor, constant: -16)
        ])
    }

    override func bindViewModel() {
        super.bindViewModel()

        viewModel?.outputs.clicked.asObservable().subscribe(onNext: { [weak self] _ in
            guard let self = self, let viewControllable = self.viewControllable else {
                return
            }
            var urlString = ""
            switch StockTradeJourneyFactory.locale {
            case .TraditionalChinese:
                urlString = "https://www.hsbc.com.hk/zh-hk/misc/data-privacy-notice/"
            case .SimplifiedChinese:
                urlString = "https://www.hsbc.com.hk/zh-cn/misc/data-privacy-notice/"
            default:
                urlString = "https://www.hsbc.com.hk/en-hk/misc/data-privacy-notice/"
            }
            _ = WebComponentFactory.startWebComponentWithUrl(aClass: PICSConsentFormDataPrivacyNoticeItem.self, vc: viewControllable, title: localizedString("wx_pics_consent_data_privacy_notice"), urlString: urlString)
        }).disposed(by: bag)

        viewModel.inputs.updateTitle(localizedString("wx_pics_consent_data_privacy_notice"))
        viewModel.inputs.updateTrailingLabel("")
        viewModel.inputs.updateTrailingIcon(ThemeManager.shared.currentTheme.systemIcons.chevronRightThick)
    }

    override func setupTealiumTaggings() {
        super.setupTealiumTaggings()
    }
}
