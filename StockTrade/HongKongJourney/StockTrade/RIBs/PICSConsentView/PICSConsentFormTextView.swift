//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class PICSConsentFormTextView: WXTextView, WXViewControllable {
    weak var viewControllable: UIViewController?
    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "wx_pics_consent_text"

        NotificationCenter.default.rx.notification(UIContentSizeCategory.didChangeNotification).subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            let htmlString = """
                <span style=\"font-family: '-apple-system', 'HelveticaNeue'; font-size:\(self.getHtmlFontSize())\">\(localizedString("wx_pics_consent_body"))</span>
            """
            self.viewModel.inputs.updateHTMLTitle(htmlString)
        }).disposed(by: bag)
    }

    override func initializeVariables() {
        super.initializeVariables()
        translatesAutoresizingMaskIntoConstraints = false
    }

    override func setupViews() {
        addSubview(titleLabel)
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: topAnchor, constant: 16),
            titleLabel.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -16),
            titleLabel.leadingAnchor.constraint(equalTo: leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -8)
        ])
    }

    override func bindViewModel() {
        super.bindViewModel()
        let htmlString = """
            <span style=\"font-family: '-apple-system', 'HelveticaNeue'; font-size:\(getHtmlFontSize())\">\(localizedString("wx_pics_consent_body"))</span>
        """
        viewModel.inputs.updateHTMLTitle(htmlString)
//        (ThemeManager.shared.currentTheme.fonts.captionMedium.pointSize)
    }

    private func getHtmlFontSize() -> String {
        if let viewController = viewControllable {
            return viewController.traitCollection.preferredContentSizeCategory.htmlFontSize
        }
        return "100%"
    }

    override func viewDidLoad() {
        super.viewDidLoad()
    }

    override func viewDeinit() {
        super.viewDeinit()
    }
}
