//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift

open class PICSConsentFormNavigationBar: WXNavigationBarWithImagebaseLeftRightButton {
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    override open func initializeVariables() {
        super.initializeVariables()
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
        leftButtonImage = ThemeManager.shared.currentTheme.systemIcons.close
        leftButtonImageContentDescription = "wx_pics_consent_title_back"
        title = localizedString("wx_pics_consent_title")
        titleLabelContentDescription = "wx_pics_consent_title"
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func viewDidAppear() {
        super.viewDidAppear()
        viewModel?.inputs.updateModuleInteractivity(true)
    }

    override open func viewWillDisappear() {
        super.viewWillDisappear()
        viewModel?.inputs.updateModuleInteractivity(false)
    }

    override open func bindViewModel() {
        super.bindViewModel()

        viewModel?.outputs.didClickLeftButton.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self, let viewControllable = self.viewControllable else { return }
            self.stockTradePresenter.didClosePicsConsentCharacterization()
            viewControllable.dismiss(animated: true, completion: nil)
        }).disposed(by: bag)
    }
}

extension PICSConsentFormNavigationBar: StockTradeView {}
