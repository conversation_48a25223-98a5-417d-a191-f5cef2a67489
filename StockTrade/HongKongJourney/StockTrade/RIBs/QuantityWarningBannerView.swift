/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class QuantityWarningBannerView: WXBannerWarning, WXTrackable, WXViewControllable {
    weak var viewControllable: UIViewController?
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    public let _stockLotSize: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    public let _stockQuantityErrorCode: BehaviorRelay<HSBCErrorCode> = BehaviorRelay(value: .noError)
    public let _quantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    public let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    public let _rightButtonDidClick: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)

    override open func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        bannerView?.accessibilityIdentifier = "banner_quantity_warning"
    }

    override open func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()
        // Show quote limit banner
        BehaviorRelay.combineLatest(
            _quantity
                .flatMap(ignoreNil)
                .distinctUntilChanged(),
            _stockQuantityErrorCode.distinctUntilChanged()
        )
        .withLatestFrom(
            BehaviorRelay.combineLatest(
                _stockLotSize.flatMap(ignoreNil).distinctUntilChanged(),
                _stockQuantityErrorCode,
                _market.flatMap(ignoreNil).distinctUntilChanged()
            )
        )
        .asObservable()
        .subscribe(onNext: { [weak self] lotSize, errorCode, market in
            guard let self = self else { return }

            var bannerMessage = ""
            switch errorCode {
            case .noError:
                self.viewModel?.inputs.updateModuleVisibility(visible: false)
                return
            case .almostReachUpperLimit: // STM-6400 align with AOS behaviour
                bannerMessage = String(format: localizedString("banner_quantity_warning"), String(lotSize))
            case .oddLotNotAllow:
                bannerMessage = String(format: localizedString("banner_quantity_warning"), String(lotSize))
            // "Quantity must be placed in lot size of \(lotSize)"
            case .oddLotSizeNotMatch:
                bannerMessage = String(format: localizedString("banner_quantity_warning"), String(lotSize)) // "Quantity must be placed in lot size of \(lotSize). Adjust the Quantity or change to market order"
            case .oddLotChangeToMarketOrder:
                bannerMessage = String(format: localizedString("banner_quantity_warning_change_market"), String(lotSize)) // "Quantity must be placed in lot size of \(lotSize). Adjust the Quantity or change to market order"
            case .oddLotLongerTimeToProcess:
                self.viewModel.updateModuleVisibility(visible: false)
                return
            default:
                return
            }

            self.viewModel?.inputs.updateViewData(
                BannerView.ViewModel(
                    message: bannerMessage,
                    leftButtonAccessibleValue: AccessibleValue(localizedString("banner_button_learn_more"), accessibilityText: "learn-more-quantity-banner"),
                    leftButtonAction: { [weak self] in
                        guard let self = self else { return }
                        let helpViewController = StockTradeJourneyFactory.getObjectFromSuffix(QuantityBannerLearnMoreScreen.self)
                        helpViewController.setListener(self)
                        self.viewControllable?.present(helpViewController.embedInNavigationController(), animated: true, completion: nil)
                    },
                    rightButtonAccessibleValue: AccessibleValue(localizedString("banner_button_dismiss"), accessibilityText: "dismiss-quantity-banner"),
                    rightButtonAction: { [weak self] in
                        guard let self = self else { return }
                        self.viewModel?.inputs.updateModuleVisibility(visible: false)
                        self.rightButtonDidClick()
                    }
                ))

            // US stock does not have the odd lot concept
            guard market != .america else { return }
            self.viewModel?.inputs.updateModuleVisibility(visible: true)
        }).disposed(by: bag)
    }

    override open func setupTealiumTaggings() {
        viewModel
            .outputs
            .moduleVisibility
            .filter { $0 == true }
            .withLatestFrom(
                Driver.combineLatest(
                    _stockLotSize.asDriver().flatMap(ignoreNil).distinctUntilChanged(),
                    _stockQuantityErrorCode.asDriver(),
                    _market.asDriver().flatMap(ignoreNil).distinctUntilChanged()
                )
            ).asObservable()
            .subscribe(onNext: { [weak self] lotSize, errorCode, market in
                guard let self = self else { return }
                switch errorCode {
                case .oddLotNotAllow:
                    self.tracker.trackEvent(EventInfo.buyQuantityError(defaultParameters: self.tracker.defaultParameters))
                case .oddLotSizeNotMatch:
                    self.tracker.trackEvent(EventInfo.buyQuantityAboveLotSizeError(defaultParameters: self.tracker.defaultParameters))
                case .oddLotChangeToMarketOrder:
                    self.tracker.trackEvent(EventInfo.buyQuantityError(defaultParameters: self.tracker.defaultParameters))
                default:
                    return
                }
            }).disposed(by: bag)

        viewModel.moduleVisibility.asObservable().asObservable().debounce(.microseconds(500), scheduler: MainScheduler.instance).subscribe(onNext: { [weak self] visibility in
            guard let self = self, visibility == false else { return }
            self.tracker.trackEvent(EventInfo.buyPageDismissBanner(defaultParameters: self.tracker.defaultParameters))
        }).disposed(by: bag)
    }

    public func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        _stockLotSize.accept(quote.lotSize)
        _market.accept(quote.stockMarket)
    }

    public func didUpdateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        _stockQuantityErrorCode.accept(error)
        _quantity.accept(quantity)
    }

    public func rightButtonDidClick() {
        _rightButtonDidClick.accept(())
    }

    public func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }
}

extension QuantityWarningBannerView: StockQuoteView {
    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateStockQuoteSection(quote: quote)
    }
}

extension QuantityWarningBannerView: StockTradeView {
    public func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        didUpdateQuantitySection(quantity: quantity, error: error)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }
}

extension QuantityWarningBannerView: WXInfoScreenViewControllerListener {
    public func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    public func showModule(_ module: WXInfoScreenViewController) {
        /* empty */
    }
}
