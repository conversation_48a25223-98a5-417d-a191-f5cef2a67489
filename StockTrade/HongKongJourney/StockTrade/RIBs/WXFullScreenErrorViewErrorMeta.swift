//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

// WXFullScreenErrorMessageProtocol
public struct WXFullScreenErrorViewErrorMeta {
    public var errorCode: HSBCErrorCode?
    public var presenterRequestType: PresenterRequestType?
    public var warnings: [HSBCWarning]?
}

extension WXFullScreenErrorViewErrorMeta: Equatable {
    public static func == (lhs: WXFullScreenErrorViewErrorMeta, rhs: WXFullScreenErrorViewErrorMeta) -> Bool {
        lhs.errorCode == rhs.errorCode &&
            lhs.presenterRequestType == rhs.presenterRequestType &&
            lhs.warnings == rhs.warnings
    }
}
