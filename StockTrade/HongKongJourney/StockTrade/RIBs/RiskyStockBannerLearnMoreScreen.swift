/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileCore
import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class RiskyStockBannerLearnMoreScreen: WXInfoScreenViewController {
    override public func initializeVariables() {
        defaultTitle = localizedString("risky_stock_title")
        defaultBodyText = localizedString("default_item_value")
        defaultSecondaryButtonTitle = nil
    }

    override public func viewDidLoad() {
        super.viewDidLoad()
        viewModel.updateMessage(title: defaultTitle, body: localizedString("risky_stock_content"))
    }
}
