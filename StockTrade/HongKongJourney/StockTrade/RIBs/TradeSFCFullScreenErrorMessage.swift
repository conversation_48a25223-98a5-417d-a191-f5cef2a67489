//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class TradeSFCFullScreenErrorMessage: WXFullScreenErrorMessage, WXTrackable, WXViewControllable {
    weak var viewControllable: UIViewController?
    weak var stockTradePresenter: StockTradePresenterImplementation!

    var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    var _customerEligibility: BehaviorRelay<WXCustomerEligiblility?> = BehaviorRelay(value: nil)
    var _acceptedPics: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)

    override func initializeVariables() {
        super.initializeVariables()
        header = localizedString("sfc_trade_blocking_screen_title")
        dismissButtonTitle = localizedString("close_cta")

        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?
            .outputs
            .didClickDismissButton
            .asObservable()
            .subscribe(onNext: { [weak self] _ in
                guard let self = self else { return }
                self.viewModel?.inputs.updateModuleVisibility(false)
            }).disposed(by: bag)

        _customerEligibility.flatMap(ignoreNil).subscribe(onNext: { [weak self] customerEligibility in
            guard let self = self else { return }
            let isCustomerEligible = customerEligibility.isCustomerEligible
            let isMobileValid = customerEligibility.isMobileValid
            let isEmailValid = customerEligibility.isEmailValid
            let ceWarnings = customerEligibility.customerEligibilityWarnings
            let picsWarnings = ceWarnings?.filter {
                if let code = $0.code {
                    return code.starts(with: "VLD")
                }
                return false
            } ?? []
            let isAcceptPics = picsWarnings.isEmpty // || self._picsAccept.value == true

            switch (isCustomerEligible, isMobileValid, isEmailValid, isAcceptPics) {
            case (_, false, false, _):
                self.body = localizedString("wx_sfc_no_mobile_no_email")
                self.viewModel.inputs.updateModuleVisibility(true)
            case (_, false, _, _):
                self.body = localizedString("wx_sfc_no_mobile")
                self.viewModel.inputs.updateModuleVisibility(true)
            case (false, _, _, false):
                // PICS checking
                self.showPicsCharacterizationCheckScreen()
            case (false, _, _, true):
                // IC checking
                self.displayInvestorCharacterizationFormViewController()
            default:
                break
            }
            self._customerEligibility.accept(nil)
        }).disposed(by: bag)

        _acceptedPics.distinctUntilChanged().flatMap(ignoreNil).withLatestFrom(
            _customerEligibility,
            resultSelector: { ($0, $1) }
        ).subscribe(onNext: { [weak self] accepted, customerEligibility in
            guard let self = self, accepted == true else {
                return
            }
            if let customerEligibility = customerEligibility,
                let customerEligibilityWarnings = customerEligibility.customerEligibilityWarnings,
                customerEligibilityWarnings.contains(where: {
                    guard let code = $0.code else {
                        return false
                    }
                    return code.starts(with: "IC") || code.starts(with: "CUS") || code.starts(with: "PRD")
                }) {
                // dismiss pics page, then present ic check page.
                DispatchQueue.main.async {
                    self.displayInvestorCharacterizationFormViewController()
                }
            }
        }).disposed(by: bag)
    }

    // assume trigger once from presenter
    func didUpdatCustomerEligibilitySection(isCustomerEligible: Bool?, isMobileValid: Bool?, isEmailValid: Bool?, customerEligibilityWarnings: [HSBCWarning]?) {
        _customerEligibility.accept(WXCustomerEligiblility(isCustomerEligible: isCustomerEligible, isMobileValid: isMobileValid, isEmailValid: isEmailValid, customerEligibilityWarnings: customerEligibilityWarnings))
    }

    func didUpdatePicsConsentCharacterization(_ accept: Bool) {
        _acceptedPics.accept(accept)
    }

    private func displayInvestorCharacterizationFormViewController() {
        let viewController = StockTradeJourneyFactory.getObjectFromSuffix(TradeInvestorCharacterizationFormViewController.self)
        let barButtonStyle = UINavigationItem.LeftBarButtonStyle.close(target: viewController, action: #selector(viewController.dismissViewController), accessibilityLabel: "button_toolbar_close", accessibilityIdentifier: "button_toolbar_close")
        viewController.navigationItem.setLeftBarButton(with: barButtonStyle)
        viewController.navigationItem.largeTitleDisplayMode = .never
        let navigationController = DefaultNavigationController(rootViewController: viewController)
        navigationController.modalPresentationStyle = .fullScreen
        viewControllable?.present(navigationController, animated: true, completion: nil)
    }

    private func showPicsCharacterizationCheckScreen() {
        guard let viewControllable = viewControllable else {
            return
        }
        let vc = StockTradeJourneyFactory.getObjectFromSuffix(PICSConsentFormViewController.self)
        let navigationController = UINavigationController(rootViewController: vc)
        navigationController.modalPresentationStyle = .fullScreen
        viewControllable.present(navigationController, animated: true)
    }
}

extension TradeSFCFullScreenErrorMessage: StockTradeView {
    func updateCustomerEligibilitySection(isCustomerEligible: Bool?, isMobileValid: Bool?, isEmailValid: Bool?, customerEligibilityWarnings: [HSBCWarning]?) {
        didUpdatCustomerEligibilitySection(isCustomerEligible: isCustomerEligible, isMobileValid: isMobileValid, isEmailValid: isEmailValid, customerEligibilityWarnings: customerEligibilityWarnings)
    }

    func updatePicsConsentCharacterization(_ accept: Bool) {
        didUpdatePicsConsentCharacterization(accept)
    }
}
