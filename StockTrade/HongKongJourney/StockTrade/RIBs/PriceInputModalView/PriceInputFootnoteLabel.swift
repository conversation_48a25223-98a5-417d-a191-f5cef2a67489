//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PriceInputFootnoteLabel: WXLabel {
    override func bindViewModel() {
        super.bindViewModel()
        #warning("test only")
        viewModel.inputs.updateString(value: String(format: localizedString("text_price_input_in_cas_range"), PriceStringFormattingUtil.formattedPriceString(price: 0, decimalPlaces: 3) ?? "N/A", PriceStringFormattingUtil.formattedPriceString(price: 0, decimalPlaces: 3) ?? "N/A"))
    }
}
