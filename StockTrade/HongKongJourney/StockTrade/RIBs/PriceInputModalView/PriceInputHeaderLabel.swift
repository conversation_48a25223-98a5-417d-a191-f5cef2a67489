/** @RIB */
//
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PriceInputHeaderLabel: WXLabel {
    override func bindViewModel() {
        super.bindViewModel()
        #warning("test only")

        let string = localizedString("title_vcm")
        let rangeOfFullString = NSRange(location: 0, length: string.count)
        let attributedString = NSMutableAttributedString(string: string)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 8
        paragraphStyle.alignment = .center
        attributedString.addAttribute(.font, value: ThemeManager.shared.currentTheme.fonts.body, range: rangeOfFullString)
        attributedString.addAttribute(.foregroundColor, value: ThemeManager.shared.currentTheme.colors.textPrimary, range: rangeOfFullString)
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: rangeOfFullString)
        viewModel.inputs.updateAttributedString(value: attributedString)
    }
}
