//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PriceInputTextField: WXPriceInputTextField {
    override func initializeVariables() {
        super.initializeVariables()
        defaultTitle = localizedString("order_input_enter_price")
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateCurrency(currency: LocalizationUtil.localizedCurrencyName(code: .hongKongDollar))
        viewModel?.inputs.updateDecimalPlaces(decimalPlaces: 3)
        viewModel?.inputs.updateSubtractButtonInteractivity(false)
    }
}
