/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileCore
import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class PriceInputLearnMoreScreen: WXInfoScreenViewController, WXTrackable {
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    public var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)

    override public func initializeVariables() {
        defaultTitle = localizedString("price_explained_title")
        defaultBodyText = localizedString("price_explained_content")
        defaultSecondaryButtonTitle = nil
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override public func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override public func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func setupTealiumTaggings() {
        super.setupTealiumTaggings()
        viewModel.dismissButton.asObservable().subscribe { [weak self] Void in
            guard let self = self, let instruction = self._instruction.value else { return }
            switch instruction {
            case .buy:
                self.tracker.trackEvent(EventInfo.buyPriceHelpCloseClick(defaultParameters: self.tracker.defaultParameters))
            case .sell:
                self.tracker.trackEvent(EventInfo.sellClickWhatIsPrice(defaultParameters: self.tracker.defaultParameters))
            default:
                return
            }
        }.disposed(by: bag)
    }

    public func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        _instruction.accept(instruction)
    }
}

extension PriceInputLearnMoreScreen: StockTradeView {
    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction, error: error)
    }
}
