//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PriceInputInlinedWarningMessage: WXInlinedWarningMessageView {
    override func bindViewModel() {
        super.bindViewModel()

        #warning("test only")
        viewModel?.inputs.updateModuleVisibility(visible: true)
        warningMessage = localizedString("text_price_ten_percent_higher_than_previous_value")
    }
}
