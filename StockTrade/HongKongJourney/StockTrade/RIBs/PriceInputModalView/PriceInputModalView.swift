/** @RIB */
//
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
//

import MobileDesign
import UIKit

class PriceInputModalView: WXInputValidationFormPanModal {
    var views: [WXBaseView]!

    var headerLabel: PriceInputHeaderLabel!

    var subheaderLabel: PriceInputSubheaderLabel!

    var priceInputTextField: PriceInputTextField!

    var footnote: PriceInputFootnoteLabel!

    var inlinedWarningMessageView: PriceInputInlinedWarningMessage!

    private let bidPriceButton: SecondaryButton = {
        // https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-8503
        let button = SecondaryButton(title: String(format: localizedString("text_bid_price"), localizedString("default_item_value")))
        return button
    }()

    private let askPriceButton: SecondaryButton = {
        // https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-8503
        let button = SecondaryButton(title: String(format: localizedString("text_ask_price"), localizedString("default_item_value")))
        return button
    }()

    override func initializeVariables() {
        super.initializeVariables()

        priceInputTextField = PriceInputTextField()

        headerLabel = PriceInputHeaderLabel()

        subheaderLabel = PriceInputSubheaderLabel()

        inlinedWarningMessageView = PriceInputInlinedWarningMessage()

        footnote = PriceInputFootnoteLabel()

        views = [
            headerLabel,
            subheaderLabel,
            priceInputTextField,
            footnote,
            inlinedWarningMessageView
        ]
        headerTitle = localizedString("price")
        confirmButtonTitle = localizedString("btn_done")
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateLearnMoreButtonVisibility(visible: true)
        viewModel?.outputs.didClickLearnMoreButton.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            let priceInputLearnMoreScreen = PriceInputLearnMoreScreen()
            priceInputLearnMoreScreen.setListener(self)
            self.present(priceInputLearnMoreScreen.embedInNavigationController(), animated: true, completion: nil)
        }).disposed(by: bag)
    }

    override func setupViews() {
        super.setupViews()
        stackView.addArrangedSubview(headerLabel)
        stackView.addArrangedSubview(subheaderLabel)
        stackView.addArrangedSubview(priceInputTextField)
        stackView.addArrangedSubview(footnote)
        stackView.setCustomSpacing(8, after: priceInputTextField)
        stackView.addArrangedSubview(inlinedWarningMessageView)

        let bidAndAskStackView = UIStackView()
        bidAndAskStackView.translatesAutoresizingMaskIntoConstraints = false
        bidAndAskStackView.axis = .horizontal
        bidAndAskStackView.distribution = .fill
        let spacingView = UIView()
        spacingView.translatesAutoresizingMaskIntoConstraints = false

        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false

        stackView.addArrangedSubview(view)
        bidAndAskStackView.addArrangedSubview(bidPriceButton)
        bidAndAskStackView.addArrangedSubview(spacingView)
        bidAndAskStackView.addArrangedSubview(askPriceButton)
        view.addSubview(bidAndAskStackView)
        NSLayoutConstraint.activate([
            bidPriceButton.widthAnchor.constraint(equalTo: askPriceButton.widthAnchor),
            spacingView.widthAnchor.constraint(equalToConstant: 30.0),
            bidAndAskStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16.0),
            bidAndAskStackView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16.0),
            bidAndAskStackView.topAnchor.constraint(equalTo: view.topAnchor),
            bidAndAskStackView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        /** stackView.addArrangedSubview(view) */
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        views.forEach { $0.viewDidLoad() }
    }

    override func viewDeinit() {
        super.viewDeinit()
        views.forEach { $0.viewDeinit() }
    }
}

extension PriceInputModalView: WXInfoScreenViewControllerListener {
    func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    func showModule(_ module: WXInfoScreenViewController) {
        /** empty */
    }
}

extension PriceInputModalView: WXInlinedWarningMessageViewDelegate {
    func showInlinedWarningMessageViewModule(_ module: WXInlinedWarningMessageView) {
        module.isHidden = false
    }

    func hideInlinedWarningMessageViewModule(_ module: WXInlinedWarningMessageView) {
        module.isHidden = true
    }
}

extension PriceInputModalView: WXLabelDelegate {
    func showModule(_ module: WXLabel) {
        module.isHidden = false
    }

    func hideModule(_ module: WXLabel) {
        module.isHidden = true
    }
}
