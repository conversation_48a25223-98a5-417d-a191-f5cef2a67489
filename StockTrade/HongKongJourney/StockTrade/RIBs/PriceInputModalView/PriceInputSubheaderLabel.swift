/** @RIB */
//
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class PriceInputSubheaderLabel: WXLabel {
    override func bindViewModel() {
        super.bindViewModel()

        let string = String(format: localizedString("text_vcm_start_end"), "HH:mm:ss", "HH:mm:ss")

        let rangeOfFullString = NSRange(location: 0, length: string.count)
        let attributedString = NSMutableAttributedString(string: string)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 8
        paragraphStyle.alignment = .center
        attributedString.addAttribute(.font, value: ThemeManager.shared.currentTheme.fonts.caption, range: rangeOfFullString)
        attributedString.addAttribute(.foregroundColor, value: ThemeManager.shared.currentTheme.colors.textSecondary, range: rangeOfFullString)
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: rangeOfFullString)
        viewModel.inputs.updateAttributedString(value: attributedString)
    }
}
