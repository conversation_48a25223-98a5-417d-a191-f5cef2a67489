//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit
// import wealthxcore

// MARK: - Inputs Protocol

protocol _CalendarScreenViewModelInputs {
    // View life cycle
    func viewDidLoad()
    func viewDidDisappear()
    func viewDeinit()

    // User interaction
    func didClickCloseButton()
    func didClickDoneButton()
    func didSelectDate(date: Date)

    // Data input
    func didUpdateSelectedDate(date: Date?) // from entity
    func didUpdateAvailableDates(dates: [Date])
    func didUpdateMarket(market: WXMarket)
    func didUpdateSeletecOrderType(type: HSBCStockTradeOrderType)
    func didUpdateErrorCodeWithWarnings(errorCode: HSBCErrorCode, warnings: [HSBCWarning]?)
    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode)
}

// MARK: - Outputs Protocol

protocol _CalendarScreenViewModelOutputs {
    var selectedDate: Driver<Date?> { get }
    var availableGoodUntilDates: Driver<[Date]> { get }

    var market: Driver<WXMarket> { get }
    var selectedOrderType: Driver<HSBCStockTradeOrderType> { get }
}

open class _CalendarScreenViewModel: NSObject, ViewModelType, _CalendarScreenViewModelInputs, _CalendarScreenViewModelOutputs, WXTrackable {
    internal var inputs: _CalendarScreenViewModelInputs { self }
    internal var outputs: _CalendarScreenViewModelOutputs { self }

    weak var view: _CalendarScreen?
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!

    public let _proposedDate: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _selectedDate: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _availableGoodUntilDates: BehaviorRelay<[Date]?> = BehaviorRelay(value: nil)
    public let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    public let _selectedOrderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)
    public let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)

    // Rx dispose bag
    public let bag = DisposeBag()

    // MARK: - Outputs Implementation

    public var market: Driver<WXMarket> { _market.asDriver().flatMap(ignoreNil) }
    public var selectedDate: Driver<Date?> { _selectedDate.asDriver() }
    public var availableGoodUntilDates: Driver<[Date]> { _availableGoodUntilDates.asDriver().flatMap(ignoreNil) }

    public var selectedOrderType: Driver<HSBCStockTradeOrderType> { _selectedOrderType.asDriver().flatMap(ignoreNil) }

    override public init() {
        super.init()
    }

    init(view: _CalendarScreen) {
        super.init()
        setupView(view: view)
    }

    // MARK: - Inputs Implementation

    // Life cycle
    public func viewDidLoad() {
        // If user picked a date on the calendar, emit the value to `_selectedDate`
        _proposedDate.asObservable().subscribe(onNext: { [weak self] date in
            guard let self = self else { return }
            if let date = date {
                self._selectedDate.accept(date)
            }
        }).disposed(by: bag)
        stockQuotePresenter?.viewDidLoad(view: self)

        setupTealiumTaggings()
    }

    public func viewDidDisappear() {}

    public func viewDeinit() {
        stockQuotePresenter?.viewDeinit(view: self)
    }

    open func setupTealiumTaggings() {}

    func setupView(view: _CalendarScreen) {
        self.view = view
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    // View Interaction
    public func didClickCloseButton() {
        view?.dismissView()
    }

    public func didClickDoneButton() {
        view?.dismissView()
    }

    public func didSelectDate(date: Date) {
        _proposedDate.accept(date)
    }

    // Data input
    public func didUpdateSelectedDate(date: Date?) {
        _selectedDate.accept(date)
    }

    public func didUpdateAvailableDates(dates: [Date]) {
        _availableGoodUntilDates.accept(dates)
    }

    public func didUpdateMarket(market: WXMarket) {
        _market.accept(market)
    }

    public func didUpdateErrorCodeWithWarnings(errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        view?.showErrorDialog(errorCode: errorCode, warnings: warnings)
    }

    public func didUpdateSeletecOrderType(type: HSBCStockTradeOrderType) {
        _selectedOrderType.accept(type)
    }

    public func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        _instruction.accept(instruction)
    }
}

extension _CalendarScreenViewModel: _CalendarViewControllerDelegate {
    func calendarViewController(_ controller: GoodUntilSelectionScreen, isSelectableAtDay day: _CalendarHelper.Day?) -> Bool {
        guard let market = _market.value,
            let date = day?.toNSDateWithTimeZone(market.timeZone)?.startOfDay,
            let selectedOrderType = _selectedOrderType.value,
            let firstAvailableGoodUntilDate = _availableGoodUntilDates.value?.first,
            let availableGoodUntilDates = selectedOrderType == .atAuctionLimit ? [firstAvailableGoodUntilDate] : _availableGoodUntilDates.value else { return false }

        let formatter = GregorianDateFormatter()
        formatter.dateFormat = "yyyy-MM-dd" // compare Year Month Day only
        for availableDate in availableGoodUntilDates {
            if formatter.string(from: availableDate) == formatter.string(from: date) {
                return true
            }
        }
        return false
    }

    func calendarViewController(_ controller: GoodUntilSelectionScreen, didSelectDate date: _CalendarHelper.Day) {
        market.asObservable().subscribe(onNext: { [weak self] market in
            guard let self = self else { return }
            let selectedDate = date.toNSDateWithTimeZone(market.timeZone)
            self._proposedDate.accept(selectedDate)
        }).disposed(by: bag)
    }

    func calendarViewController(_ controller: GoodUntilSelectionScreen, didSelectDateRangeWithStartDate startDate: _CalendarHelper.Day, endDate: _CalendarHelper.Day) {}

    func calendarViewController(_ controller: GoodUntilSelectionScreen, didScrollToBottom scrollToBottom: Bool) {}
}

extension _CalendarScreenViewModel: StockQuoteView {
    public func updateAvailableGoodUntilDatesSection(dates: [Date], error: HSBCErrorCode) {
        inputs.didUpdateAvailableDates(dates: dates)
    }

    public func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        inputs.didUpdateErrorCodeWithWarnings(errorCode: errorCode, warnings: warnings)
    }
}
