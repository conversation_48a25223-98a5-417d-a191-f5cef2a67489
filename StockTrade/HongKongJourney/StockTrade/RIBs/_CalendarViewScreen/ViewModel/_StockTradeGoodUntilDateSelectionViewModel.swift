//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit
// import wealthxcore

open class _StockTradeGoodUntilDateSelectionViewModel: _CalendarScreenViewModel {
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    override public init() {
        super.init()
    }

    override init(view: _CalendarScreen) {
        super.init(view: view)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func setupTealiumTaggings() {
        _instruction.flatMap(ignoreNil).subscribe(onNext: { [weak self] instruction in
            guard let self = self else {
                return
            }
            switch instruction {
            case .buy:
                self.tracker.trackPage(EventInfo.buyGoodUntilPageLoad(defaultParameters: self.tracker.defaultParameters))
            case .modifyBuy:
                self.tracker.trackPage(EventInfo.orderStatusOrderModifyDateClick(defaultParameters: self.tracker.defaultParameters))
            default:
                return
            }
        }).disposed(by: bag)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func didClickDoneButton() {
        super.didClickDoneButton()
        // Take a snapshot of `_proposedDate` and update the module
        if let date = _proposedDate.value {
            stockTradePresenter?.didEditSection(section: .goodUntil(date: date))
        }
    }

    override func setupView(view: _CalendarScreen) {
        super.setupView(view: view)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }
}

// MARK: - StockBuyView

extension _StockTradeGoodUntilDateSelectionViewModel: StockTradeView {
    public func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        inputs.didUpdateMarket(market: market)
    }

//    func updateAvailableGoodUntilDatesSection(dates: [Date], error: HSBCErrorCode) {
//        inputs.didUpdateAvailableDates(dates: dates)
//    }

    public func updateGoodUntilSection(date: Date?, error: HSBCErrorCode) {
        inputs.didUpdateSelectedDate(date: date)
    }

    public func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        inputs.didUpdateSeletecOrderType(type: type)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        inputs.didUpdateInstructionSection(instruction: instruction, error: error)
    }
}
