/** @View */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

#warning("demo only")

let _kCalendarCellReuseID = "com.hsbc.collectionview.reuse.cell.calendar"
let _kCalendarHeaderReuseID = "com.hsbc.collectionview.reuse.header.calendar"

typealias _CalendarDayRange = (startDate: _CalendarHelper.Day, endDate: _CalendarHelper.Day)

protocol _CalendarViewControllerDelegate: AnyObject {
    func calendarViewController(_ controller: GoodUntilSelectionScreen, isSelectableAtDay day: _CalendarHelper.Day?) -> Bool

    func calendarViewController(_ controller: GoodUntilSelectionScreen, didSelectDate date: _CalendarHelper.Day)
    func calendarViewController(_ controller: GoodUntilSelectionScreen, didSelectDateRangeWithStartDate startDate: _CalendarHelper.Day, endDate: _CalendarHelper.Day)

    func calendarViewController(_ controller: GoodUntilSelectionScreen, didScrollToBottom scrollToBottom: Bool)
}

extension GoodUntilSelectionScreen: _CalendarScreen {
    func showErrorDialog(errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        ErrorMessagesHandler.showErrorDialog(errorCode, warnings: warnings, viewController: self)
    }

    func dismissView() {
        navigationController?.popViewController(animated: true)
    }

    func displaySelectedDate(date: Date) {}
}

open class GoodUntilSelectionScreen: UIViewController {
    public var viewModel: _CalendarScreenViewModel!
    open var defaultNavigationBarBackgroundColor: UIColor { ThemeManager.shared.currentTheme.colors.defaultNavigationBarBackgroundColor }
    open var defaultNavigationBarTintColor: UIColor { ThemeManager.shared.currentTheme.colors.defaultNavigationBarTintColor }
    override open var preferredStatusBarStyle: UIStatusBarStyle { .default }

    // For accessibility only
    weak var accessibilityListener: AccessibilityDelegate?

    // Rx dispose bag
    public let bag = DisposeBag()

    // Stack view
    var stackView: UIStackView = {
        let view = UIStackView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.distribution = .fill
        view.spacing = 16
        view.axis = .vertical
        return view
    }()

    var weekDayStackView: UIStackView = {
        let view = UIStackView()
        view.distribution = .fillEqually
        view.axis = .horizontal

        for i in 0 ..< 7 {
            let label = UILabel()
            label.textAlignment = .center
            label.adjustsFontForContentSizeCategory = true
            label.translatesAutoresizingMaskIntoConstraints = false
            switch i {
            case 0:
                label.text = localizedString("calendar_weekday_sun")
            case 1:
                label.text = localizedString("calendar_weekday_mon")
            case 2:
                label.text = localizedString("calendar_weekday_tue")
            case 3:
                label.text = localizedString("calendar_weekday_wed")
            case 4:
                label.text = localizedString("calendar_weekday_thur")
            case 5:
                label.text = localizedString("calendar_weekday_fri")
            case 6:
                label.text = localizedString("calendar_weekday_sat")
            default:
                label.text = ""
            }
            label.isAccessibilityElement = false
            label.font = ThemeManager.shared.currentTheme.fonts.footnote
            label.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
            view.addArrangedSubview(label)
        }
        return view
    }()

    var collectionView: UICollectionView!
    var offScreenHeaderView: _CalendarHeaderView!
    fileprivate var isFirstLayoutSubview = true

    weak var delegate: _CalendarViewControllerDelegate?

    var dataSource: _CalendarDataSource!
    var shouldScrollToBottomAtInitial = true

    public lazy var backButton: UIButton = {
        let button = UIButton(type: .custom)
        return button
    }()

    var saveButton: UIButton!

    override open func viewDidLoad() {
        super.viewDidLoad()
        viewModel = StockTradeJourneyFactory.getObjectFromSuffix(_StockTradeGoodUntilDateSelectionViewModel.self)
        viewModel.setupView(view: self)
        view.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted

        offScreenHeaderView = (UINib(nibName: "_CalendarHeaderView", bundle: stockTradeBundle).instantiate(withOwner: nil, options: nil).first as! _CalendarHeaderView)

        view.addSubview(stackView)
        stackView.addArrangedSubview(weekDayStackView)

        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        switch traitCollection.preferredContentSizeCategory {
        case .accessibilityExtraExtraExtraLarge,
             .accessibilityExtraExtraLarge,
             .accessibilityExtraLarge,
             .accessibilityLarge,
             .accessibilityMedium:
            layout.minimumLineSpacing = 25
        case .extraExtraExtraLarge,
             .extraExtraLarge,
             .extraLarge,
             .large:
            layout.minimumLineSpacing = 20
        default:
            layout.minimumLineSpacing = 15
        }
        layout.minimumInteritemSpacing = 0

        collectionView = UICollectionView(frame: CGRect.zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.dataSource = dataSource
        collectionView.delegate = self
        collectionView.register(UINib(nibName: "_CalendarCollectionViewCell", bundle: stockTradeBundle), forCellWithReuseIdentifier: _kCalendarCellReuseID)
        collectionView.register(UINib(nibName: "_CalendarHeaderView", bundle: stockTradeBundle), forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: _kCalendarHeaderReuseID)
        collectionView.showsVerticalScrollIndicator = false
        collectionView.showsHorizontalScrollIndicator = false
        stackView.addArrangedSubview(collectionView)

        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16.0),
            stackView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])

        setupNavigationBar()
        setupViewModel()
    }

    override open func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        viewModel.inputs.viewDidDisappear()
    }

    deinit {
        viewModel?.inputs.viewDeinit()
    }

    override open func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        if isFirstLayoutSubview {
            isFirstLayoutSubview = false

            guard shouldScrollToBottomAtInitial else { return }

            guard let dataSource = dataSource else { return }

            if let lastMonth = self.dataSource.months.last {
                let indexPath = IndexPath(row: lastMonth.numberOfItems() - 1, section: dataSource.months.count - 1)
                collectionView.scrollToItem(at: indexPath, at: .top, animated: false)
            }
        }
    }

    func setupViewModel() {
        viewModel.inputs.viewDidLoad()
        delegate = viewModel
        Driver.combineLatest(viewModel.outputs.availableGoodUntilDates, viewModel.outputs.market, viewModel.outputs.selectedDate).asObservable().take(1).subscribe(onNext: { [weak self] dates, market, selected in
            guard let firstDate = dates.first else { return }
            guard let lastDate = dates.last else { return }
            let timeZone = market.timeZone

            guard let startMonth = _CalendarHelper.toCalendarDayWithTimeZone(date: firstDate, timeZone)?.toMonth() else { return }
            guard let endMonth = _CalendarHelper.toCalendarDayWithTimeZone(date: lastDate, timeZone)?.toMonth() else { return }
            guard let today = _CalendarHelper.toCalendarDayWithTimeZone(date: Date(), timeZone) else { return }
            guard let self = self else { return }

            // If `selected` is null, then do not set any `preferredSelectedDate`
            var selectedDay: _CalendarHelper.Day?

            if let selected = selected {
                selectedDay = _CalendarHelper.toCalendarDayWithTimeZone(date: selected, timeZone)
            }

            self.dataSource = _CalendarDataSource(
                startMonth: startMonth,
                endMonth: endMonth,
                today: today,
                multipleSelection: false,
                withTimeZone: timeZone,
                preferredSelectedDate: selectedDay
            )
            self.dataSource.delegate = self
            self.collectionView.dataSource = self.dataSource
            self.callDelegateForUpdatingStateIfNeeded()
        }).disposed(by: bag)

        saveButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickDoneButton()
        }.disposed(by: bag)

        backButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickCloseButton()
            // For accessibility only
            self.accessibilityListener?.shouldSelectAccessibilityView()
        }.disposed(by: bag)
    }

    func setupNavigationBar() {
        let titleLabel = UILabel()
        titleLabel.text = localizedString("good_until_title")
        titleLabel.font = ThemeManager.shared.currentTheme.fonts.bodyEmphasized
        titleLabel.textColor = defaultNavigationBarTintColor
        titleLabel.accessibilityIdentifier = "text_toolbar_title"
        navigationItem.titleView = titleLabel

        // Back button
        backButton.setImage(ThemeManager.shared.currentTheme.systemIcons.close, for: .normal)
        backButton.frame = CGRect(x: 0, y: 0, width: 44, height: 44)
        backButton.accessibilityLabel = localizedString("close")
        backButton.accessibilityIdentifier = "button_toolbar_close"
        backButton.tintColor = defaultNavigationBarTintColor
        if UIAccessibility.isVoiceOverRunning {
            backButton.becomeFirstResponder()
        }
        backButton.setNeedsFocusUpdate()

        saveButton = UIButton(type: .custom)
        saveButton.setTitle(localizedString("alt_done"), for: .normal)
        saveButton.setTitleColor(defaultNavigationBarTintColor, for: .normal)
        saveButton.frame = CGRect(x: 0, y: 0, width: 44, height: 44)
        saveButton.accessibilityIdentifier = "calendar"

        navigationItem.leftBarButtonItem = UIBarButtonItem(customView: backButton)
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: saveButton)
    }
}

extension GoodUntilSelectionScreen {
    func callDelegateForUpdatingStateIfNeeded() {
        guard let state = dataSource.state else { return }

        switch state {
        case .single(let date, _):
            delegate?.calendarViewController(self, didSelectDate: date)
        case .multiple(let startDate, let endDate, _, _):
            delegate?.calendarViewController(self, didSelectDateRangeWithStartDate: startDate, endDate: endDate)
        }
    }
}

extension GoodUntilSelectionScreen: UICollectionViewDelegateFlowLayout {
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        let month = dataSource.months[section]
        dataSource.configHeaderView(offScreenHeaderView, withMonth: month)

        offScreenHeaderView.setNeedsUpdateConstraints()
        offScreenHeaderView.updateConstraintsIfNeeded()

        offScreenHeaderView.bounds = CGRect(x: 0, y: 0, width: collectionView.bounds.width, height: offScreenHeaderView.bounds.height)

        offScreenHeaderView.setNeedsLayout()
        offScreenHeaderView.layoutIfNeeded()

        let size = offScreenHeaderView.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize)
        return CGSize(width: collectionView.bounds.width, height: ceil(size.height))
    }

    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let length = round(collectionView.bounds.width / 7.0) - 1
        return CGSize(width: length, height: 60)
    }

    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        dataSource.updateSelectionStateAtSelectedIndexPath(indexPath)
    }
}

extension GoodUntilSelectionScreen: _CalendarDataSourceDelegate {
    func calendarDataSource(_ dataSource: _CalendarDataSource, isSelectableAtDay day: _CalendarHelper.Day?) -> Bool {
        delegate?.calendarViewController(self, isSelectableAtDay: day) ?? false
    }

    func calendarDataSource(_ dataSource: _CalendarDataSource, didUpdateState: _CalendarDataSource.State?) {
        collectionView.reloadItems(at: collectionView.indexPathsForVisibleItems)
        callDelegateForUpdatingStateIfNeeded()
    }
}
