//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

public class _CalendarHelper: NSObject {
    public struct Day: Comparable {
        let year: Int
        let month: Int
        let day: Int
        let timeZone: Foundation.TimeZone

        public init?(year: Int, month: Int, day: Int, timeZone: Foundation.TimeZone) {
            guard month >= 1, month <= 12 else { return nil }
            guard day >= 1, day <= 31 else { return nil }

            switch month {
            case 1, 3, 5, 7, 8, 10, 12:
                guard day <= 31 else { return nil }
            case 4, 6, 9, 11:
                guard day <= 30 else { return nil }
            case 2:
                let isLeapYear = ((year % 100 != 0) && (year % 4 == 0) || (year % 400) == 0)
                if isLeapYear {
                    guard day <= 29 else { return nil }
                } else {
                    guard day <= 28 else { return nil }
                }
            default:
                break
            }

            self.year = year
            self.month = month
            self.day = day
            self.timeZone = timeZone
        }

        public func isEqual(to day: Day) -> Bool {
            year == day.year
                && month == day.month
                && self.day == day.day
        }

        public func toMonth() -> _CalendarHelper.Month? {
            _CalendarHelper.Month(year: year, month: month, timeZone: timeZone)
        }

        public static func < (lhs: _CalendarHelper.Day, rhs: _CalendarHelper.Day) -> Bool {
            if lhs.year < rhs.year {
                return true
            } else if lhs.year > rhs.year {
                return false
            }

            if lhs.month < rhs.month {
                return true
            } else if lhs.month > rhs.month {
                return false
            }

            return lhs.day < rhs.day
        }

        public func toNSDateWithTimeZone(_ timeZone: TimeZone) -> Date? {
            var dateComponents = DateComponents()
            dateComponents.year = year
            dateComponents.month = month
            dateComponents.day = day

            var calendar = Calendar.gregorian
            calendar.timeZone = timeZone
            return calendar.date(from: dateComponents)
        }
    }

    public struct Month: Comparable {
        let year: Int
        let month: Int
        let numberOfDays: Int
        let numberOfWeeks: Int
        let weekdayOfFirstDay: Weekday
        let timeZone: Foundation.TimeZone

        public init?(year: Int, month: Int, timeZone: Foundation.TimeZone) {
            guard month >= 1, month <= 12 else { return nil }

            self.year = year
            self.month = month
            numberOfDays = _CalendarHelper.numberOfDaysWithYear(year, month: month, withTimeZone: timeZone)
            numberOfWeeks = _CalendarHelper.numberOfWeeksWithYear(year, month: month, withTimeZone: timeZone)
            weekdayOfFirstDay = _CalendarHelper.weekdayOfFirstDayWithYear(year, month: month, withTimeZone: timeZone)
            self.timeZone = timeZone
        }

        public func nextMonth() -> _CalendarHelper.Month? {
            var month = self.month + 1
            var year = self.year

            if month > 12 {
                month = 1
                year += 1
            }

            return _CalendarHelper.Month(year: year, month: month, timeZone: timeZone)
        }

        public static func < (lhs: _CalendarHelper.Month, rhs: _CalendarHelper.Month) -> Bool {
            if lhs.year < rhs.year {
                return true
            } else if lhs.year > rhs.year {
                return false
            }

            return lhs.month < rhs.month
        }
    }

    public enum Weekday: Int {
        case sunday = 1
        case monday = 2
        case tuesday = 3
        case wednesday = 4
        case thursday = 5
        case friday = 6
        case saturday = 7

        var indexWeekday: Int {
            rawValue - 1
        }
    }

    public class func firstDateWithYear(_ year: Int, month: Int, withTimeZone timeZone: Foundation.TimeZone) -> Date {
        var dateComponents = DateComponents()
        dateComponents.year = year
        dateComponents.month = month
        dateComponents.day = 1

        let calendar = calendarWithTimeZone(timeZone)
        let date = calendar.date(from: dateComponents)
        return date!
    }

    public class func numberOfWeeksWithYear(_ year: Int, month: Int, withTimeZone timeZone: Foundation.TimeZone) -> Int {
        let date = firstDateWithYear(year, month: month, withTimeZone: timeZone)
        let range = (Calendar.gregorian as NSCalendar).range(of: .weekOfMonth, in: .month, for: date)
        let length = range.length
        return length
    }

    public class func weekdayOfFirstDayWithYear(_ year: Int, month: Int, withTimeZone timeZone: Foundation.TimeZone) -> Weekday {
        let calendar = calendarWithTimeZone(timeZone)
        let firstDay = firstDateWithYear(year, month: month, withTimeZone: timeZone)
        let dateComponent = (calendar as NSCalendar).components([.weekday], from: firstDay)

        let weekday = Weekday(rawValue: dateComponent.weekday!)!
        return weekday
    }

    public class func numberOfDaysWithYear(_ year: Int, month: Int, withTimeZone timeZone: Foundation.TimeZone) -> Int {
        let calendar = calendarWithTimeZone(timeZone)
        let date = firstDateWithYear(year, month: month, withTimeZone: timeZone)

        let numberOfDays = (calendar as NSCalendar).range(of: .day, in: .month, for: date)
        return numberOfDays.length
    }

    public static func calendarWithTimeZone(_ timeZone: Foundation.TimeZone) -> Foundation.Calendar {
        var calendar = Calendar.gregorian
        calendar.timeZone = timeZone
        return calendar
    }

    public static func toCalendarDayWithTimeZone(date: Date, _ timeZone: Foundation.TimeZone) -> _CalendarHelper.Day? {
        var calendar = Calendar.gregorian
        calendar.timeZone = timeZone
        let dateComponents = (calendar as NSCalendar).components([.year, .month, .day], from: date)
        return _CalendarHelper.Day(year: dateComponents.year!, month: dateComponents.month!, day: dateComponents.day!, timeZone: timeZone)
    }
}
