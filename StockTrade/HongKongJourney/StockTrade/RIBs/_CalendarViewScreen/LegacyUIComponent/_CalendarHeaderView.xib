<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionReusableView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="7pY-3y-NBM" customClass="_CalendarHeaderView" customModule="StockTrade" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="61"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GdQ-OJ-PkE">
                    <rect key="frame" x="0.0" y="15" width="320" height="17"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="miv-Tw-b1U">
                            <rect key="frame" x="228.66666666666666" y="0.0" width="45.666666666666657" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="S8l-eU-lCm">
                            <rect key="frame" x="45.666666666666671" y="0.0" width="45.666666666666671" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mkM-jU-jjf">
                            <rect key="frame" x="0.0" y="0.0" width="45.666666666666664" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Rse-pN-j7G">
                            <rect key="frame" x="274.33333333333331" y="0.0" width="45.666666666666686" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="jy0-yW-dOL">
                            <rect key="frame" x="91.333333333333329" y="0.0" width="45.666666666666671" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ltz-3O-eZr">
                            <rect key="frame" x="137" y="0.0" width="46" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sun" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="lbG-tl-aff">
                            <rect key="frame" x="183" y="0.0" width="45.666666666666657" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="miv-Tw-b1U" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="4Cb-6j-Mwa"/>
                        <constraint firstAttribute="bottom" secondItem="S8l-eU-lCm" secondAttribute="bottom" id="9CA-rp-PvP"/>
                        <constraint firstItem="Rse-pN-j7G" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="ER2-51-1rW"/>
                        <constraint firstItem="S8l-eU-lCm" firstAttribute="width" secondItem="mkM-jU-jjf" secondAttribute="width" id="EqR-Rb-xBJ"/>
                        <constraint firstItem="Rse-pN-j7G" firstAttribute="width" secondItem="mkM-jU-jjf" secondAttribute="width" id="I3y-zU-9dn"/>
                        <constraint firstAttribute="bottom" secondItem="jy0-yW-dOL" secondAttribute="bottom" id="LNm-1u-dhy"/>
                        <constraint firstItem="S8l-eU-lCm" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="SvZ-de-ZqG"/>
                        <constraint firstItem="ltz-3O-eZr" firstAttribute="width" secondItem="mkM-jU-jjf" secondAttribute="width" id="TEZ-DO-Z3f"/>
                        <constraint firstItem="jy0-yW-dOL" firstAttribute="leading" secondItem="S8l-eU-lCm" secondAttribute="trailing" id="TOR-7E-4BU"/>
                        <constraint firstItem="miv-Tw-b1U" firstAttribute="width" secondItem="mkM-jU-jjf" secondAttribute="width" id="TcJ-zh-Y2k"/>
                        <constraint firstItem="jy0-yW-dOL" firstAttribute="width" secondItem="mkM-jU-jjf" secondAttribute="width" id="Wbg-gf-1Gw"/>
                        <constraint firstAttribute="bottom" secondItem="mkM-jU-jjf" secondAttribute="bottom" id="WyQ-rw-RcZ"/>
                        <constraint firstItem="mkM-jU-jjf" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="YBK-zO-hOc"/>
                        <constraint firstAttribute="bottom" secondItem="Rse-pN-j7G" secondAttribute="bottom" id="arN-1t-7FJ"/>
                        <constraint firstItem="lbG-tl-aff" firstAttribute="width" secondItem="mkM-jU-jjf" secondAttribute="width" id="bis-Bz-klE"/>
                        <constraint firstItem="miv-Tw-b1U" firstAttribute="leading" secondItem="lbG-tl-aff" secondAttribute="trailing" id="dZl-5K-yIx"/>
                        <constraint firstAttribute="bottom" secondItem="miv-Tw-b1U" secondAttribute="bottom" id="e7K-8c-xtT"/>
                        <constraint firstAttribute="bottom" secondItem="ltz-3O-eZr" secondAttribute="bottom" id="iRP-mp-MuF"/>
                        <constraint firstItem="lbG-tl-aff" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="l2U-o9-udy"/>
                        <constraint firstItem="mkM-jU-jjf" firstAttribute="leading" secondItem="GdQ-OJ-PkE" secondAttribute="leading" id="lhV-dE-mBW"/>
                        <constraint firstItem="S8l-eU-lCm" firstAttribute="leading" secondItem="mkM-jU-jjf" secondAttribute="trailing" id="m9s-o6-FCp"/>
                        <constraint firstItem="ltz-3O-eZr" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="mwW-AW-MMe"/>
                        <constraint firstItem="jy0-yW-dOL" firstAttribute="top" secondItem="GdQ-OJ-PkE" secondAttribute="top" id="nfh-qM-RTA"/>
                        <constraint firstAttribute="bottom" secondItem="lbG-tl-aff" secondAttribute="bottom" id="r8Y-MN-c7X"/>
                        <constraint firstItem="Rse-pN-j7G" firstAttribute="leading" secondItem="miv-Tw-b1U" secondAttribute="trailing" id="rsP-k4-JWx"/>
                        <constraint firstItem="lbG-tl-aff" firstAttribute="leading" secondItem="ltz-3O-eZr" secondAttribute="trailing" id="sWz-Pf-Zmm"/>
                        <constraint firstAttribute="trailing" secondItem="Rse-pN-j7G" secondAttribute="trailing" id="ucF-OQ-rCT"/>
                        <constraint firstItem="ltz-3O-eZr" firstAttribute="leading" secondItem="jy0-yW-dOL" secondAttribute="trailing" id="zbr-I3-hy4"/>
                    </constraints>
                </view>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="huG-59-a3H">
                    <rect key="frame" x="0.0" y="4" width="320" height="43"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4R0-9k-aUC">
                            <rect key="frame" x="0.0" y="0.0" width="45.666666666666664" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fN4-0f-A2S">
                            <rect key="frame" x="45.666666666666671" y="0.0" width="45.666666666666671" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xjl-At-Iqf">
                            <rect key="frame" x="91.333333333333329" y="0.0" width="45.666666666666671" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z2v-ls-YAs">
                            <rect key="frame" x="137" y="0.0" width="46" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bna-QH-nUF">
                            <rect key="frame" x="183" y="0.0" width="45.666666666666657" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rIb-kh-Ha0">
                            <rect key="frame" x="228.66666666666666" y="0.0" width="45.666666666666657" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="v5P-12-toh">
                            <rect key="frame" x="274.33333333333331" y="0.0" width="45.666666666666686" height="43"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                </stackView>
            </subviews>
            <constraints>
                <constraint firstItem="huG-59-a3H" firstAttribute="top" secondItem="7pY-3y-NBM" secondAttribute="top" constant="4" id="JQI-s6-uhe"/>
                <constraint firstAttribute="trailing" secondItem="GdQ-OJ-PkE" secondAttribute="trailing" id="NYr-MP-6ad"/>
                <constraint firstItem="huG-59-a3H" firstAttribute="leading" secondItem="7pY-3y-NBM" secondAttribute="leading" id="W1x-Us-fcY"/>
                <constraint firstItem="GdQ-OJ-PkE" firstAttribute="leading" secondItem="7pY-3y-NBM" secondAttribute="leading" id="gNP-eg-PAI"/>
                <constraint firstItem="GdQ-OJ-PkE" firstAttribute="top" secondItem="7pY-3y-NBM" secondAttribute="top" constant="15" id="ghf-8c-QDB"/>
                <constraint firstAttribute="bottom" secondItem="huG-59-a3H" secondAttribute="bottom" constant="14" id="lj7-c7-6vX"/>
                <constraint firstAttribute="trailing" secondItem="huG-59-a3H" secondAttribute="trailing" id="om2-LT-nga"/>
            </constraints>
            <connections>
                <outlet property="fridayLabel" destination="miv-Tw-b1U" id="P0U-8D-Ptv"/>
                <outlet property="fridayMonthLabel" destination="rIb-kh-Ha0" id="gSj-ac-sWM"/>
                <outlet property="mondayLabel" destination="S8l-eU-lCm" id="HN0-5m-WMK"/>
                <outlet property="mondayMonthLabel" destination="fN4-0f-A2S" id="Wbd-Yv-5N3"/>
                <outlet property="saturdayLabel" destination="Rse-pN-j7G" id="Ou4-KJ-h1v"/>
                <outlet property="saturdayMonthLabel" destination="v5P-12-toh" id="wUO-MB-sYN"/>
                <outlet property="sundayLabel" destination="mkM-jU-jjf" id="Ron-IT-iYB"/>
                <outlet property="sundayMonthLabel" destination="4R0-9k-aUC" id="HRg-zj-OdQ"/>
                <outlet property="thursdayLabel" destination="lbG-tl-aff" id="7As-JG-WWv"/>
                <outlet property="thursdayMonthLabel" destination="bna-QH-nUF" id="iHB-jQ-Ebw"/>
                <outlet property="tuesdayLabel" destination="jy0-yW-dOL" id="DW2-Bx-rZ2"/>
                <outlet property="tuesdayMonthLabel" destination="Xjl-At-Iqf" id="Baq-Qy-eVt"/>
                <outlet property="wednesdayLabel" destination="ltz-3O-eZr" id="Y7c-hs-g4y"/>
                <outlet property="wednesdayMonthLabel" destination="Z2v-ls-YAs" id="3sm-Bn-wy1"/>
                <outletCollection property="weekdayLabels" destination="mkM-jU-jjf" collectionClass="NSMutableArray" id="UHQ-OI-Xrt"/>
                <outletCollection property="weekdayLabels" destination="S8l-eU-lCm" collectionClass="NSMutableArray" id="HyR-Hc-Q0A"/>
                <outletCollection property="weekdayLabels" destination="jy0-yW-dOL" collectionClass="NSMutableArray" id="bYa-Wt-vmX"/>
                <outletCollection property="weekdayLabels" destination="ltz-3O-eZr" collectionClass="NSMutableArray" id="tCz-6H-IZb"/>
                <outletCollection property="weekdayLabels" destination="lbG-tl-aff" collectionClass="NSMutableArray" id="bp1-Vd-a70"/>
                <outletCollection property="weekdayLabels" destination="miv-Tw-b1U" collectionClass="NSMutableArray" id="K8G-aL-NFg"/>
                <outletCollection property="weekdayLabels" destination="Rse-pN-j7G" collectionClass="NSMutableArray" id="FMM-qj-iUI"/>
            </connections>
            <point key="canvasLocation" x="469.56521739130437" y="386.04910714285711"/>
        </collectionReusableView>
    </objects>
</document>
