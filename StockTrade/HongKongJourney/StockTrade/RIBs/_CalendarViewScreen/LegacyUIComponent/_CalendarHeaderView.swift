//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class _CalendarHeaderView: UICollectionReusableView {
    @IBOutlet var sundayLabel: UILabel!
    @IBOutlet var mondayLabel: UILabel!
    @IBOutlet var tuesdayLabel: UILabel!
    @IBOutlet var wednesdayLabel: UILabel!
    @IBOutlet var thursdayLabel: UILabel!
    @IBOutlet var fridayLabel: UILabel!
    @IBOutlet var saturdayLabel: UILabel!
    @IBOutlet var weekdayLabels: [UILabel]!

    @IBOutlet var mondayMonthLabel: UILabel!
    @IBOutlet var tuesdayMonthLabel: UILabel!
    @IBOutlet var wednesdayMonthLabel: UILabel!
    @IBOutlet var thursdayMonthLabel: UILabel!
    @IBOutlet var fridayMonthLabel: UILabel!
    @IBOutlet var saturdayMonthLabel: UILabel!
    @IBOutlet var sundayMonthLabel: UILabel!
}

extension _CalendarHeaderView {
    override func awakeFromNib() {
        super.awakeFromNib()

        for label in weekdayLabels {
            if [sundayLabel, saturdayLabel].contains(label) {
                label.textColor = ThemeManager.shared.currentTheme.colors.textDisabled
            } else {
                label.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
            }
            label.font = ThemeManager.shared.currentTheme.fonts.footnoteEmphasized
        }

        configFontStyle()

        sundayLabel.text = "S"
        mondayLabel.text = "M"
        tuesdayLabel.text = "T"
        wednesdayLabel.text = "W"
        thursdayLabel.text = "T"
        fridayLabel.text = "F"
        saturdayLabel.text = "S"
    }

    func configFontStyle() {
        for label in weekdayLabels {
            label.font = ThemeManager.shared.currentTheme.fonts.footnoteEmphasized
        }
    }

    func resetMonthLabel() {
        mondayMonthLabel.text = ""
        sundayMonthLabel.text = ""
        tuesdayMonthLabel.text = ""
        wednesdayMonthLabel.text = ""
        thursdayMonthLabel.text = ""
        fridayMonthLabel.text = ""
        saturdayMonthLabel.text = ""
    }
}
