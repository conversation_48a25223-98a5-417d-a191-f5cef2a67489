<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" multipleTouchEnabled="YES" contentMode="center" id="ZSw-ay-VsK" customClass="_CalendarCollectionViewCell" customModule="StockTrade" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uwL-HA-PHM" customClass="CalendarSelectionMaskView" customModule="StockTrade" customModuleProvider="target">
                        <rect key="frame" x="-1" y="14" width="52" height="30"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="30" id="TkF-bg-5AT"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="532-o8-cuO" customClass="RoundedView" customModule="StockTrade" customModuleProvider="target">
                        <rect key="frame" x="12" y="12" width="26" height="26"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="532-o8-cuO" secondAttribute="height" multiplier="1:1" id="pKU-V8-9kV"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="radiusMultiplier">
                                <real key="value" value="2"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="0.0"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="11" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GHS-cq-isg">
                        <rect key="frame" x="17.666666666666668" y="14.666666666666664" width="15.000000000000004" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Today" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iKS-VB-VdI">
                        <rect key="frame" x="5.6666666666666679" y="47.666666666666664" width="39" height="16.999999999999993"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YXa-7W-ldu">
                        <rect key="frame" x="-0.66666666666666785" y="0.0" width="51.333333333333343" height="1"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.16" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="jUA-2y-y6W"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="GHS-cq-isg" firstAttribute="centerY" secondItem="ZSw-ay-VsK" secondAttribute="centerY" id="0P9-ht-hua"/>
                <constraint firstItem="iKS-VB-VdI" firstAttribute="centerX" secondItem="GHS-cq-isg" secondAttribute="centerX" id="2v4-0m-KyX"/>
                <constraint firstItem="uwL-HA-PHM" firstAttribute="top" secondItem="ZSw-ay-VsK" secondAttribute="top" constant="14" id="7JD-Gb-rYN"/>
                <constraint firstItem="GHS-cq-isg" firstAttribute="height" secondItem="532-o8-cuO" secondAttribute="height" multiplier="0.8" id="DSX-f2-4W8"/>
                <constraint firstItem="GHS-cq-isg" firstAttribute="centerX" secondItem="ZSw-ay-VsK" secondAttribute="centerX" id="GZh-bo-ZKf"/>
                <constraint firstItem="GHS-cq-isg" firstAttribute="centerX" secondItem="ZSw-ay-VsK" secondAttribute="centerX" id="ICF-xc-yHq"/>
                <constraint firstItem="uwL-HA-PHM" firstAttribute="leading" secondItem="ZSw-ay-VsK" secondAttribute="leading" constant="-1" placeholder="YES" id="L0c-Hp-ncA"/>
                <constraint firstItem="532-o8-cuO" firstAttribute="centerY" secondItem="ZSw-ay-VsK" secondAttribute="centerY" id="NaN-Ea-xf2"/>
                <constraint firstAttribute="trailing" secondItem="YXa-7W-ldu" secondAttribute="trailing" constant="-0.5" id="PZW-gn-Ota"/>
                <constraint firstItem="YXa-7W-ldu" firstAttribute="trailing" secondItem="GHS-cq-isg" secondAttribute="trailing" id="SbH-fL-4P6"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="532-o8-cuO" secondAttribute="trailing" id="Zsh-sQ-8aV"/>
                <constraint firstItem="YXa-7W-ldu" firstAttribute="leading" secondItem="ZSw-ay-VsK" secondAttribute="leading" constant="-0.5" id="buv-gC-INc"/>
                <constraint firstItem="532-o8-cuO" firstAttribute="centerX" secondItem="ZSw-ay-VsK" secondAttribute="centerX" id="cGE-Cy-OhB"/>
                <constraint firstItem="GHS-cq-isg" firstAttribute="centerY" secondItem="ZSw-ay-VsK" secondAttribute="centerY" id="g41-3l-wKb"/>
                <constraint firstItem="iKS-VB-VdI" firstAttribute="top" secondItem="GHS-cq-isg" secondAttribute="bottom" constant="12" id="hSc-Ug-9ov"/>
                <constraint firstItem="532-o8-cuO" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="ZSw-ay-VsK" secondAttribute="leading" id="nvK-ax-Zfc"/>
                <constraint firstAttribute="top" secondItem="YXa-7W-ldu" secondAttribute="top" id="ofD-3d-QoM"/>
                <constraint firstAttribute="trailing" secondItem="uwL-HA-PHM" secondAttribute="trailing" constant="-1" placeholder="YES" id="t2x-f5-1qO"/>
                <constraint firstItem="YXa-7W-ldu" firstAttribute="leading" secondItem="GHS-cq-isg" secondAttribute="leading" id="zFg-r0-DgJ"/>
            </constraints>
            <variation key="default">
                <mask key="constraints">
                    <exclude reference="SbH-fL-4P6"/>
                    <exclude reference="zFg-r0-DgJ"/>
                    <exclude reference="Zsh-sQ-8aV"/>
                    <exclude reference="nvK-ax-Zfc"/>
                </mask>
            </variation>
            <connections>
                <outlet property="contentLabel" destination="GHS-cq-isg" id="OoW-xa-ifk"/>
                <outlet property="secondarySelectionView" destination="uwL-HA-PHM" id="d0p-mH-rCE"/>
                <outlet property="selectionView" destination="532-o8-cuO" id="xNl-gz-Ymo"/>
                <outlet property="selectionViewLeadingConstraint" destination="nvK-ax-Zfc" id="luZ-Pw-iKl"/>
                <outlet property="selectionViewTrailingConstraint" destination="Zsh-sQ-8aV" id="bE8-FU-Ubs"/>
                <outlet property="todayIndicatorLabel" destination="iKS-VB-VdI" id="obt-bG-QMC"/>
                <outlet property="upperlineView" destination="YXa-7W-ldu" id="Dqe-Os-QrH"/>
            </connections>
            <point key="canvasLocation" x="460.86956521739131" y="225.66964285714283"/>
        </collectionViewCell>
    </objects>
</document>
