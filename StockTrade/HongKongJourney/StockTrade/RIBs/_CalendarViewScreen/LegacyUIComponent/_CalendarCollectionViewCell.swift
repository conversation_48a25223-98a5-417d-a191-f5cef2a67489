//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class _CalendarCollectionViewCell: UICollectionViewCell {
    @IBOutlet var selectionView: RoundedView!
    @IBOutlet var secondarySelectionView: _CalendarSelectionMaskView!
    @IBOutlet var contentLabel: UILabel!
    @IBOutlet var upperlineView: UIView!
    @IBOutlet var selectionViewLeadingConstraint: NSLayoutConstraint!
    @IBOutlet var selectionViewTrailingConstraint: NSLayoutConstraint!
    @IBOutlet var todayIndicatorLabel: UILabel!

    @IBOutlet var selectionViewWidthConstraint: NSLayoutConstraint!
    weak var secondarySelectionLeadingConstraint: NSLayoutConstraint?
    weak var secondarySelectionTrailingConstraint: NSLayoutConstraint?
    weak var secondarySelectionToSelectionLeadingConstraint: NSLayoutConstraint?
    weak var secondarySelectionToSelectionTrailingConstraint: NSLayoutConstraint?
}

extension _CalendarCollectionViewCell {
    override func prepareForReuse() {
        super.prepareForReuse()
        upperlineView.isHidden = true
    }

    override func awakeFromNib() {
        super.awakeFromNib()

        selectionView.backgroundColor = ThemeManager.shared.currentTheme.colors.dataVisualization9

        secondarySelectionView.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundStandard

        contentLabel.textColor = ThemeManager.shared.currentTheme.colors.textOverPrimary
        contentLabel.adjustsFontForContentSizeCategory = true
        contentLabel.translatesAutoresizingMaskIntoConstraints = false

        selectionView.isHidden = true
        secondarySelectionView.isHidden = true
        upperlineView.isHidden = true

        alignSecondarySelectionViewLeadingToCell()
        alignSecondarySelectionViewTrailingToCell()

        todayIndicatorLabel.accessibilityIdentifier = "text_today"
        todayIndicatorLabel.text = localizedString("good_until_today")
        todayIndicatorLabel.adjustsFontForContentSizeCategory = true
        todayIndicatorLabel.translatesAutoresizingMaskIntoConstraints = false
    }

    func configFontStyle() {
        contentLabel.font = ThemeManager.shared.currentTheme.fonts.body
    }
}

extension _CalendarCollectionViewCell {
    func configCellWithDay(_ day: _CalendarHelper.Day?, indexPath: IndexPath, isToday: Bool = false, isSelected: Bool = false, selectable: Bool = false) {
        guard let aDay = day else {
            contentLabel.text = nil
            selectionView.isHidden = true
            return
        }

        contentLabel.text = String(aDay.day)
        selectionView.isHidden = !isSelected
        upperlineView.isHidden = false
        if isSelected {
            contentLabel.textColor = ThemeManager.shared.currentTheme.colors.textOverPrimary
            contentLabel.font = ThemeManager.shared.currentTheme.fonts.bodyEmphasized
        } else if selectable {
            contentLabel.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
            contentLabel.font = ThemeManager.shared.currentTheme.fonts.body
        } else {
            contentLabel.textColor = ThemeManager.shared.currentTheme.colors.textDisabled
            contentLabel.font = ThemeManager.shared.currentTheme.fonts.body
        }

        contentLabel.isAccessibilityElement = true

        let accessibilityText = getAccessibilityDateTextWithDay(aDay, andIndexPath: indexPath)

        if isToday {
            todayIndicatorLabel.isHidden = false
            contentLabel.accessibilityLabel = localizedString("alt_today") + " " + accessibilityText
        } else if selectable {
            todayIndicatorLabel.isHidden = true
            contentLabel.accessibilityLabel = accessibilityText
        } else {
            let altText = localizedString("alt_calendar_disabled_date") + accessibilityText
            todayIndicatorLabel.isHidden = true
            contentLabel.accessibilityLabel = altText
        }
    }

    func getAccessibilityDateTextWithDay(_ day: _CalendarHelper.Day, andIndexPath indexPath: IndexPath) -> String {
        let weekdayString = _CalendarDataSource.accessibilityDayStringWithRow(indexPath.row % 7)
        let monthString = _CalendarDataSource.monthStringWithMonthValue(day.month)

        var altText = "\(weekdayString) \(day.year)-\(monthString)-\(day.day)"

        let currentLocale = StockTradeJourneyFactory.locale.rawValue

        if currentLocale == "zh-Hans" || currentLocale == "zh-Hant" {
            altText = weekdayString + "\(day.year)年" + "\(monthString)" + "\(day.day)日"
        }

        return altText
    }

    func alignSecondarySelectionViewLeadingToCell() {
        if let leading = secondarySelectionToSelectionLeadingConstraint {
            removeConstraint(leading)
            secondarySelectionToSelectionLeadingConstraint = nil
        }

        if secondarySelectionLeadingConstraint == nil {
            let constraint = NSLayoutConstraint(item: secondarySelectionView as Any, attribute: .leading, relatedBy: .equal, toItem: contentView, attribute: .leading, multiplier: 1, constant: -2)
            addConstraint(constraint)
            secondarySelectionLeadingConstraint = constraint
        }
    }

    func alignSecondarySelectionViewLeadingToSelectionView() {
        if let leading = secondarySelectionLeadingConstraint {
            removeConstraint(leading)
            secondarySelectionLeadingConstraint = nil
        }

        if secondarySelectionToSelectionLeadingConstraint == nil {
            let constraint = NSLayoutConstraint(item: secondarySelectionView as Any, attribute: .leading, relatedBy: .equal, toItem: selectionView, attribute: .leading, multiplier: 1, constant: 0)
            addConstraint(constraint)
            secondarySelectionToSelectionLeadingConstraint = constraint
        }
    }

    func alignSecondarySelectionViewTrailingToCell() {
        if let trailing = secondarySelectionToSelectionTrailingConstraint {
            removeConstraint(trailing)
            secondarySelectionToSelectionTrailingConstraint = nil
        }

        if secondarySelectionTrailingConstraint == nil {
            let constraint = NSLayoutConstraint(item: secondarySelectionView as Any, attribute: .trailing, relatedBy: .equal, toItem: contentView, attribute: .trailing, multiplier: 1, constant: 2)
            addConstraint(constraint)
            secondarySelectionTrailingConstraint = constraint
        }
    }

    func alignSecondarySelectionViewTrailingToSelectionView() {
        if let trailing = secondarySelectionTrailingConstraint {
            removeConstraint(trailing)
            secondarySelectionTrailingConstraint = nil
        }

        if secondarySelectionToSelectionTrailingConstraint == nil {
            let constraint = NSLayoutConstraint(item: secondarySelectionView as Any, attribute: .trailing, relatedBy: .equal, toItem: selectionView, attribute: .trailing, multiplier: 1, constant: 0)
            addConstraint(constraint)
            secondarySelectionToSelectionTrailingConstraint = constraint
        }
    }
}

class _CalendarSelectionMaskView: UIView {
    weak var maskLayer: CAShapeLayer?
    var roundInfo: (corners: UIRectCorner, radius: CGFloat)? {
        didSet {
            self.setupMaskLayer()
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        setupMaskLayer()
    }

    fileprivate func setupMaskLayer() {
        if let roundInfo = self.roundInfo {
            let path = UIBezierPath(roundedRect: bounds, byRoundingCorners: roundInfo.corners, cornerRadii: CGSize(width: roundInfo.radius, height: roundInfo.radius))
            let mask = CAShapeLayer()
            mask.path = path.cgPath
            layer.mask = mask
        } else {
            layer.mask = nil
        }
    }
}
