//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit
// import wealthxcore

protocol _CalendarDataSourceDelegate: AnyObject {
    func calendarDataSource(_ dataSource: _CalendarDataSource, isSelectableAtDay day: _CalendarHelper.Day?) -> Bool
    func calendarDataSource(_ dataSource: _CalendarDataSource, didUpdateState: _CalendarDataSource.State?)
}

class _CalendarDataSource: NSObject, UICollectionViewDataSource {
    enum State {
        case single(date: _CalendarHelper.Day, indexPath: IndexPath)
        case multiple(startDate: _CalendarHelper.Day, endDate: _CalendarHelper.Day, startIndexPath: IndexPath, endIndexPath: IndexPath)
    }

    weak var delegate: _CalendarDataSourceDelegate?

    let today: _CalendarHelper.Day
    let months: [_CalendarHelper.Month]
    let multipleSelection: Bool
    let timeZone: TimeZone

    var state: State? {
        didSet {
            delegate?.calendarDataSource(self, didUpdateState: state)
        }
    }

    init?(startMonth: _CalendarHelper.Month, endMonth: _CalendarHelper.Month, today: _CalendarHelper.Day, multipleSelection: Bool, withTimeZone timeZone: TimeZone, preferredSelectedDate: _CalendarHelper.Day? = nil, preferredSelectedDateRange: _CalendarDayRange? = nil) {
        guard startMonth <= endMonth else { return nil }

        var months: [_CalendarHelper.Month] = []
        var currentMonth = startMonth
        while currentMonth <= endMonth {
            months.append(currentMonth)
            if let nextMonth = currentMonth.nextMonth() {
                currentMonth = nextMonth
            } else {
                break
            }
        }

        self.months = months
        self.today = today
        self.multipleSelection = multipleSelection
        self.timeZone = timeZone

        super.init()

        if !self.multipleSelection {
            var success = configDefaultStateIfPossibleWithDate(preferredSelectedDate, startMonth: startMonth, endMonth: endMonth)
            if !success {
                success = configDefaultStateIfPossibleWithDate(today, startMonth: startMonth, endMonth: endMonth)
            }
            if !success {
                if let lastDate = _CalendarHelper.Day(year: endMonth.year, month: endMonth.month, day: endMonth.numberOfDays, timeZone: timeZone) {
                    configDefaultStateIfPossibleWithDate(lastDate, startMonth: startMonth, endMonth: endMonth)
                }
            }
        } else {
            var success = configDefaultStateIfPossibleWithStartDate(preferredSelectedDateRange?.startDate, endDate: preferredSelectedDateRange?.endDate, startMonth: startMonth, endMonth: endMonth)
            if !success {
                success = configDefaultStateIfPossibleWithStartDate(today, endDate: today, startMonth: startMonth, endMonth: endMonth)
            }
            if !success {
                if let lastDate = _CalendarHelper.Day(year: endMonth.year, month: endMonth.month, day: endMonth.numberOfDays, timeZone: timeZone) {
                    configDefaultStateIfPossibleWithStartDate(lastDate, endDate: lastDate, startMonth: startMonth, endMonth: endMonth)
                }
            }
        }
    }

    @discardableResult
    func configDefaultStateIfPossibleWithDate(_ date: _CalendarHelper.Day?, startMonth: _CalendarHelper.Month, endMonth: _CalendarHelper.Month) -> Bool {
        if let preferredSelectionDate = date,
            let aMonth = preferredSelectionDate.toMonth(), aMonth >= startMonth, aMonth <= endMonth, preferredSelectionDate.day <= aMonth.numberOfDays,
            let indexPath = indexPathFromDay(preferredSelectionDate) {
            if multipleSelection {
                state = .multiple(startDate: preferredSelectionDate, endDate: preferredSelectionDate, startIndexPath: indexPath, endIndexPath: indexPath)
            } else {
                state = .single(date: preferredSelectionDate, indexPath: indexPath)
            }

            return true
        }

        return false
    }

    @discardableResult
    func configDefaultStateIfPossibleWithStartDate(_ startDate: _CalendarHelper.Day?, endDate: _CalendarHelper.Day?, startMonth: _CalendarHelper.Month, endMonth: _CalendarHelper.Month) -> Bool {
        if let start = startDate,
            let end = endDate,
            let aStartMonth = start.toMonth(), aStartMonth >= startMonth, aStartMonth <= endMonth, start.day <= aStartMonth.numberOfDays,
            let anEndMonth = end.toMonth(), anEndMonth >= startMonth, anEndMonth <= endMonth, end.day <= anEndMonth.numberOfDays,
            let startIndexPath = indexPathFromDay(start),
            let endIndexPath = indexPathFromDay(end) {
            if multipleSelection, start != end {
                state = .multiple(startDate: start, endDate: end, startIndexPath: startIndexPath, endIndexPath: endIndexPath)
            } else {
                state = .single(date: start, indexPath: startIndexPath)
            }

            return true
        }

        return false
    }
}

extension _CalendarDataSource {
    static func shortMonthStringWithMonthValue(_ month: Int) -> String {
        switch month {
        case 1: return localizedString("wx_calendar_month_jan")
        case 2: return localizedString("wx_calendar_month_feb")
        case 3: return localizedString("wx_calendar_month_mar")
        case 4: return localizedString("wx_calendar_month_apr")
        case 5: return localizedString("wx_calendar_month_may")
        case 6: return localizedString("wx_calendar_month_jun")
        case 7: return localizedString("wx_calendar_month_jul")
        case 8: return localizedString("wx_calendar_month_aug")
        case 9: return localizedString("wx_calendar_month_sept")
        case 10: return localizedString("wx_calendar_month_oct")
        case 11: return localizedString("wx_calendar_month_nov")
        case 12: return localizedString("wx_calendar_month_dec")
        default: return ""
        }
    }

    static func monthStringWithMonthValue(_ month: Int) -> String {
        switch month {
        case 1: return localizedString("calendar_month_jan_long")
        case 2: return localizedString("calendar_month_feb_long")
        case 3: return localizedString("calendar_month_mar_long")
        case 4: return localizedString("calendar_month_apr_long")
        case 5: return localizedString("calendar_month_may_long")
        case 6: return localizedString("calendar_month_jun_long")
        case 7: return localizedString("calendar_month_jul_long")
        case 8: return localizedString("calendar_month_aug_long")
        case 9: return localizedString("calendar_month_sept_long")
        case 10: return localizedString("calendar_month_oct_long")
        case 11: return localizedString("calendar_month_nov_long")
        case 12: return localizedString("calendar_month_dec_long")
        default: return ""
        }
    }

    static func accessibilityDayStringWithRow(_ row: Int) -> String {
        switch row {
        case 0: return localizedString("alt_sunday")
        case 1: return localizedString("alt_monday")
        case 2: return localizedString("alt_tuesday")
        case 3: return localizedString("alt_wednesday")
        case 4: return localizedString("alt_thursday")
        case 5: return localizedString("alt_friday")
        case 6: return localizedString("alt_saturday")
        default: return ""
        }
    }

    func dayFromIndexPath(_ indexPath: IndexPath) -> _CalendarHelper.Day? {
        guard indexPath.section < months.count else { return nil }

        let month = months[indexPath.section]
        let day = indexPath.row - month.weekdayOfFirstDay.indexWeekday + 1

        if day >= 1, day <= month.numberOfDays {
            return _CalendarHelper.Day(year: month.year, month: month.month, day: day, timeZone: timeZone)
        } else {
            return nil
        }
    }

    func indexPathFromDay(_ day: _CalendarHelper.Day) -> IndexPath? {
        guard let month = day.toMonth(),
            let section = months.firstIndex(of: month) else { return nil }
        let row = day.day - 1 + month.weekdayOfFirstDay.indexWeekday
        return IndexPath(row: row, section: section)
    }
}

extension _CalendarDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        months.count
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        let month = months[section]
        return month.numberOfItems()
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: _kCalendarCellReuseID, for: indexPath) as! _CalendarCollectionViewCell
        cell.configFontStyle()
        cell.todayIndicatorLabel.isHidden = true

        let calendarDay = dayFromIndexPath(indexPath)
        let isToday = calendarDay == today
        let selectable = delegate?.calendarDataSource(self, isSelectableAtDay: calendarDay) ?? (calendarDay != nil)

        if let calendarDay = calendarDay {
            let yearStr: String = "\(calendarDay.year)"
            let monthStr: String = (calendarDay.month > 9) ? "\(calendarDay.month)" : "0\(calendarDay.month)"
            let dayStr: String = (calendarDay.day > 9) ? "\(calendarDay.day)" : "0\(calendarDay.day)"

            cell.accessibilityIdentifier = "kDateYYYYMMDD" + yearStr + monthStr + dayStr
        }

        if let state = self.state {
            var round = UIRectCorner()

            switch state {
            case .single(let date, _):
                if calendarDay == date {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: true, selectable: selectable)
                    cell.contentLabel.accessibilityTraits = UIAccessibilityTraits(rawValue: UIAccessibilityTraits.button.rawValue | UIAccessibilityTraits.selected.rawValue)
                } else {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: false, selectable: selectable)
                    cell.contentLabel.accessibilityTraits = UIAccessibilityTraits.button
                }
            case .multiple(_, _, let startIndexPath, let endIndexPath):
                if indexPath == startIndexPath, startIndexPath == endIndexPath {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: true, selectable: selectable)
                    cell.selectionView.isHidden = false
                    cell.secondarySelectionView.isHidden = false
                    round.insert(.topLeft)
                    round.insert(.bottomLeft)
                    round.insert(.topRight)
                    round.insert(.bottomRight)

                    cell.alignSecondarySelectionViewLeadingToSelectionView()
                    cell.alignSecondarySelectionViewTrailingToSelectionView()
                } else if indexPath == startIndexPath {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: true, selectable: selectable)
                    cell.selectionView.isHidden = false
                    cell.secondarySelectionView.isHidden = false
                    round.insert(.topLeft)
                    round.insert(.bottomLeft)

                    cell.alignSecondarySelectionViewLeadingToSelectionView()
                    cell.alignSecondarySelectionViewTrailingToCell()
                } else if indexPath == endIndexPath {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: true, selectable: selectable)
                    cell.selectionView.isHidden = false
                    cell.secondarySelectionView.isHidden = false

                    round.insert(.topRight)
                    round.insert(.bottomRight)

                    cell.alignSecondarySelectionViewTrailingToSelectionView()
                    cell.alignSecondarySelectionViewLeadingToCell()
                } else if indexPath.compare(startIndexPath) == .orderedDescending, indexPath.compare(endIndexPath) == .orderedAscending {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: false, selectable: selectable)
                    cell.selectionView.isHidden = true
                    cell.secondarySelectionView.isHidden = false

                    cell.alignSecondarySelectionViewLeadingToCell()
                    cell.alignSecondarySelectionViewTrailingToCell()
                    cell.accessibilityTraits = UIAccessibilityTraits(rawValue: UIAccessibilityTraits.button.rawValue | UIAccessibilityTraits.selected.rawValue)
                } else {
                    cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, isSelected: false, selectable: selectable)
                    cell.selectionView.isHidden = true
                    cell.secondarySelectionView.isHidden = true

                    cell.alignSecondarySelectionViewLeadingToCell()
                    cell.alignSecondarySelectionViewTrailingToCell()
                }

                if indexPath >= startIndexPath, indexPath <= endIndexPath {
                    cell.contentLabel.accessibilityTraits = UIAccessibilityTraits(rawValue: UIAccessibilityTraits.button.rawValue | UIAccessibilityTraits.selected.rawValue)
                } else {
                    cell.contentLabel.accessibilityTraits = UIAccessibilityTraits(rawValue: UIAccessibilityTraits.notEnabled.rawValue | UIAccessibilityTraits.button.rawValue)
                }

                if indexPath.row % 7 == 0 {
                    round.insert(.topLeft)
                    round.insert(.bottomLeft)

                    cell.alignSecondarySelectionViewLeadingToSelectionView()
                }

                if indexPath.row % 7 == 6 {
                    round.insert(.topRight)
                    round.insert(.bottomRight)

                    cell.alignSecondarySelectionViewTrailingToSelectionView()
                }

                if round.isEmpty {
                    cell.secondarySelectionView.roundInfo = nil
                } else {
                    cell.secondarySelectionView.roundInfo = (round, cell.secondarySelectionView.bounds.width / 2)
                }
            }
        } else {
            cell.configCellWithDay(calendarDay, indexPath: indexPath, isToday: isToday, selectable: selectable)
        }

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        let headerView = collectionView.dequeueReusableSupplementaryView(ofKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: _kCalendarHeaderReuseID, for: indexPath) as! _CalendarHeaderView
        let month = months[indexPath.section]
        configHeaderView(headerView, withMonth: month)
        return headerView
    }
}

extension _CalendarDataSource {
    func configHeaderView(_ headerView: _CalendarHeaderView, withMonth month: _CalendarHelper.Month) {
        headerView.configFontStyle()

        let monthStr = _CalendarDataSource.shortMonthStringWithMonthValue(month.month)
        headerView.resetMonthLabel()
        switch month.weekdayOfFirstDay {
        case .sunday:
            headerView.sundayMonthLabel.text = monthStr
        case .monday:
            headerView.mondayMonthLabel.text = monthStr
        case .tuesday:
            headerView.tuesdayMonthLabel.text = monthStr
        case .wednesday:
            headerView.wednesdayMonthLabel.text = monthStr
        case .thursday:
            headerView.thursdayMonthLabel.text = monthStr
        case .friday:
            headerView.fridayMonthLabel.text = monthStr
        case .saturday:
            headerView.saturdayMonthLabel.text = monthStr
        }
    }

    func updateSelectionStateAtSelectedIndexPath(_ selectedIndexPath: IndexPath) {
        guard let selectedDate = dayFromIndexPath(selectedIndexPath) else { return }

        let selectable = delegate?.calendarDataSource(self, isSelectableAtDay: selectedDate) ?? true
        guard selectable else { return }

        if multipleSelection {
            if let state = self.state {
                switch state {
                case .multiple(let startDate, let endDate, let startIndexPath, let endIndexPath):
                    if startIndexPath == endIndexPath {
                        let newStartDate = min(selectedDate, startDate)
                        let newEndDate = max(selectedDate, endDate)
                        if let startIndexPath = indexPathFromDay(newStartDate),
                            let endIndexPath = indexPathFromDay(newEndDate) {
                            self.state = .multiple(startDate: newStartDate, endDate: newEndDate, startIndexPath: startIndexPath, endIndexPath: endIndexPath)
                        }
                    } else {
                        self.state = .multiple(startDate: selectedDate, endDate: selectedDate, startIndexPath: selectedIndexPath, endIndexPath: selectedIndexPath)
                    }
                case .single:
                    self.state = .multiple(startDate: selectedDate, endDate: selectedDate, startIndexPath: selectedIndexPath, endIndexPath: selectedIndexPath)
                }
            } else {
                state = .multiple(startDate: selectedDate, endDate: selectedDate, startIndexPath: selectedIndexPath, endIndexPath: selectedIndexPath)
            }
        } else {
            state = .single(date: selectedDate, indexPath: selectedIndexPath)
        }
    }
}

extension _CalendarHelper.Month {
    func numberOfItems() -> Int {
        numberOfWeeks * 7
    }
}
