//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import RxCocoa
#warning("demo only")
protocol _PriceInputViewModelOutputs: AnyObject {
    var price: Driver<Decimal?> { get }
    var proposedPrice: Driver<Decimal?> { get }
    var spreadInterval: Driver<Decimal> { get }
    var dayLowPrice: Driver<Decimal?> { get }
    var dayHighPrice: Driver<Decimal?> { get }
    var bidPrice: Driver<Decimal?> { get }
    var askPrice: Driver<Decimal?> { get }
    var currency: Driver<WXCurrencyCode> { get }
    var stockQuoteUpdateTime: Driver<String?> { get }
    var exchangeTimezone: Driver<String?> { get }
    var proposedPriceError: Driver<HSBCErrorCode> { get }
    var market: Driver<WXMarket> { get }
    var orderType: Driver<HSBCStockTradeOrderType> { get }
    var isDelayedQuote: Driver<Bool?> { get }
    // Volatility Control Mechanism (VCM) Cooling Period
    var isVCMCoolingPeriod: Driver<Bool> { get }
    var vcmCoolingPeriodStartTime: Driver<Date> { get }
    var vcmCoolingPeriodEndTime: Driver<Date> { get }
    var vcmLowerLimit: Driver<Decimal> { get }
    var vcmUpperLimit: Driver<Decimal> { get }
    var isMDSDown: Driver<Bool> { get }
    var decimalPlaces: Driver<Int> { get }

    // UI
    var priceTrailingZero: Driver<Bool> { get }
}
