//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation
import MobileCore
import MobileDesign
import RxCocoa
import RxSwift
import UIKit

#warning("demo only")
open class _PriceInputViewModel: NSObject, ViewModelType, _PriceInputViewModelInputs, _PriceInputViewModelOutputs, WXTrackable {
    internal var inputs: _PriceInputViewModelInputs { self }
    internal var outputs: _PriceInputViewModelOutputs { self }

    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    weak var view: PriceInputView?

    public let _orderInstruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    public let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    public let _price: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _proposedPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _spreadInterval: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _dayLowPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _dayHighPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _refreshDate: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _bidPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _askPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _isDelayedQuote: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public let _currency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)
    public let _stockQuoteUpdateTime: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    public let _exchangeTimezone: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    public let _proposedPriceError: BehaviorRelay<HSBCErrorCode> = BehaviorRelay(value: .noError)
    public let _limitRange: BehaviorRelay<ClosedRange<Decimal>?> = BehaviorRelay(value: nil)
    public let _validSpreadRange: BehaviorRelay<ClosedRange<Decimal>?> = BehaviorRelay(value: nil)
    public let _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)
    public let _decimalPlaces: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    // Volatility Control Mechanism (VCM) Cooling Period
    public let _isInVCMPeriod: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public let _vcmCoolingPeriodStartTime: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _vcmCoolingPeriodEndTime: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _vcmLowerLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _vcmUpperLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _isMDSDown: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    // UI
    internal let _priceTrailingZero: BehaviorRelay<Bool> = BehaviorRelay(value: true)
    public let _didClickBidButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public let _didClickAskButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public let _didClickDoneButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public let _didClickHelpButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    // Pre opening session
    public let _tradingPeriodType: BehaviorRelay<HSBCTradingPeriodType?> = BehaviorRelay(value: nil)
    public let _isPOSEligible: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public let _posReferencePrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _posBuyLimitRange: BehaviorRelay<ClosedRange<Decimal>?> = BehaviorRelay(value: nil)
    public let _posSellLimitRange: BehaviorRelay<ClosedRange<Decimal>?> = BehaviorRelay(value: nil)

    // Rx dispose bag
    public let bag = DisposeBag()

    override public init() {
        super.init()
    }

    init(view: PriceInputView) {
        super.init()
        setupView(view: view)
    }

    open func viewDidLoad() {
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
        _isMDSDown.flatMap(ignoreNil).distinctUntilChanged().asObservable().subscribe(onNext: { [weak self] isMDSDown in
            guard let self = self else { return }
            if isMDSDown {
                self.view?.disableBidButton()
                self.view?.disableAskButton()
            } else {
                self.view?.enableBidButton()
                self.view?.enableAskButton()
            }
        }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _proposedPrice,
            _proposedPriceError,
            _spreadInterval.distinctUntilChanged(),
            _limitRange.distinctUntilChanged(),
            _tradingPeriodType.distinctUntilChanged(),
            _validSpreadRange.flatMap(ignoreNil).distinctUntilChanged(),
            _orderType.flatMap(ignoreNil).distinctUntilChanged(),
            _isPOSEligible.distinctUntilChanged()
        ).asObservable().subscribe(onNext: { [weak self] proposedPrice, error, spreadInterval, limitRange, tradingPeriodType, spreadRange, orderType, isPOSEligible in
            guard let self = self else { return }

            // If price is nil, we disable te price decrease button
            if proposedPrice == nil || (proposedPrice?.isZero ?? false) {
                self.view?.disablePriceDecreaseButton()
                self.view?.hideInlineStatusView()
                self.view?.disableDoneButton()

                /// https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-7029
                /// to fix first prompt on `priceInputViewController` to show CAS header
                if tradingPeriodType == .cas {
                    self.view?.displayCASInlineMessage(errorCode: error, upperLimit: limitRange?.upperBound, lowerLimit: limitRange?.lowerBound, market: self._market.value)
                } else if tradingPeriodType == .pos, isPOSEligible == true, orderType != .limit {
                    // isPOSEligible can be nil, thus we explicitly check if its true
                    self.view?.displayCASInlineMessage(errorCode: error, upperLimit: limitRange?.upperBound, lowerLimit: limitRange?.lowerBound, market: self._market.value)
                } else if let proposedPrice = proposedPrice, proposedPrice.isZero {
                    self.view?.displayOutOfSpreadTableInlineMessage()
                }
            } else if let proposedPrice = proposedPrice {
                // If price equals to the minimum valid value, disable the decrease button
                // If price equals to the maxmium valid value, disable the increase button
                if let spreadInterval = spreadInterval {
                    if proposedPrice - spreadInterval < spreadRange.lowerBound {
                        self.view?.disablePriceDecreaseButton()
                    } else if proposedPrice + spreadInterval > spreadRange.upperBound {
                        self.view?.disablePriceIncreaseButton()
                    } else {
                        self.view?.enablePriceDecreaseButton()
                        self.view?.enablePriceIncreaseButton()
                    }
                }

                // 1. if spread table error -> disable done button + show inline message has icon
                // 2. If spread table no error -> check if there is cas error -> show error -> cas text ... no icon
                // 3. if .pos period, and price is out of range
                // 4. if no any error -> show 10% ->  has icon
                // 5. no, there should be nothing
                switch (error, tradingPeriodType) {
                case (.valueOutOfRange, _): // Handle spread incremental error
                    // If user input is out of the spread table range
                    if proposedPrice < spreadRange.lowerBound || proposedPrice > spreadRange.upperBound {
                        self.view?.displayOutOfSpreadTableInlineMessage()
                    } else if let market = self._market.value, let currency = self._currency.value, let spreadInterval = spreadInterval {
                        self.view?.displaySpreadIncrementalInlineMessage(spreadInterval: spreadInterval, market: market, currency: currency)
                    }
                    self.view?.disableDoneButton()
                case (.casPriceInRange, .cas), (.casPriceOutOfRange, .cas), (.casAuctionPeriod, .cas), (.noError, .cas): // Handle CAS related errors when if VCM is false or nil
                    // Only show CAS inline mesage if its not in VCM cool period
                    // > `The VCM is only applicable for board lot order input during the Continuous Trading Session (CTS), but not for any orders input during the Pre-opening Session (POS) and the CAS`
                    self.view?.displayCASInlineMessage(errorCode: error, upperLimit: limitRange?.upperBound, lowerLimit: limitRange?.lowerBound, market: self._market.value)
                    self.view?.enableDoneButton()
                case (.exceededProactivePriceAlertLimit, _):
                    self.view?.displayExceededProactivePriceAlertInlineMessage()
                    self.view?.enableDoneButton()
                case (let error, .pos) where isPOSEligible == true && error != .noError:
                    if orderType != .limit {
                        self.view?.displayCASInlineMessage(errorCode: error, upperLimit: limitRange?.upperBound, lowerLimit: limitRange?.lowerBound, market: self._market.value)
                    }
                    if error == .posProactivePriceAlertLimit {
                        self.view?.displayExceededPreOpeningSessionProactivePriceAlertInlineMessage()
                    }
                    self.view?.enableDoneButton()
                case (.posProactivePriceAlertLimit, .cts):
                    self.view?.displayExceededPreOpeningSessionProactivePriceAlertInlineMessage()
                    self.view?.enableDoneButton()
                case (_, .vcm): // Show the vcm inline message whenever its in the period
                    if let upperLimit = limitRange?.upperBound, let lowerLimit = limitRange?.lowerBound, [HSBCStockTradeOrderType.market, HSBCStockTradeOrderType.limit].contains(orderType) {
                        // Handle VCM related errors
                        self.view?.displayVCMInlineMessage(errorCode: error, upperLimit: upperLimit, lowerLimit: lowerLimit, market: self._market.value)
                        self.view?.enableDoneButton()
                    } else {
                        // FIXIT: Need to refactor using `case with where clause`
                        self.view?.hideInlineStatusView()
                        self.view?.enableDoneButton()
                    }
                default: // If none of the case above fit, then we enable the CTA
                    self.view?.hideInlineStatusView()
                    self.view?.enableDoneButton()
                }
            }
        }).disposed(by: bag)

        BehaviorRelay.combineLatest(_tradingPeriodType.flatMap(ignoreNil).distinctUntilChanged(), _isPOSEligible.flatMap(ignoreNil).distinctUntilChanged()).subscribe(onNext: { [weak self] tradingPeriodType, isPOSEligible in
            guard let self = self else { return }
            if tradingPeriodType == .pos, isPOSEligible {
                self.view?.displayPOSHeaderMessage()
            }
        }).disposed(by: bag)

        setupTealiumTaggings()
    }

    open func viewDidDisappear() {}

    open func viewDeinit() {
        stockQuotePresenter?.viewDeinit(view: self)
        if let price = _price.value, !price.isZero { // reset proposed quantity after panel is dismissed
            stockTradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: price))
        } else {
            stockTradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: nil))
        }
        stockTradePresenter?.viewDeinit(view: self)
    }

    func setupView(view: PriceInputView) {
        self.view = view
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    open func setupTealiumTaggings() {
        _orderInstruction.flatMap(ignoreNil).take(1).subscribe(onNext: { [weak self] instruction in
            guard let self = self else { return }
            if instruction == .buy {
                self.tracker.trackPage(EventInfo.buyPriceInputPageLoad(defaultParameters: self.tracker.defaultParameters))
            } else if instruction == .sell {
                self.tracker.trackPage(EventInfo.sellPricePage(defaultParameters: self.tracker.defaultParameters))
            }
        }).disposed(by: bag)

        _didClickAskButton.take(1).subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            if self._orderInstruction.value == .sell {
                self.tracker.trackEvent(EventInfo.sellPriceClickAsk(defaultParameters: self.tracker.defaultParameters))
            } else if self._orderInstruction.value == .buy {
                self.tracker.trackEvent(EventInfo.buyPriceInputAskClick(defaultParameters: self.tracker.defaultParameters))
            }
        }).disposed(by: bag)

        _didClickBidButton.take(1).subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            if self._orderInstruction.value == .sell {
                self.tracker.trackEvent(EventInfo.sellPriceClickBid(defaultParameters: self.tracker.defaultParameters))
            } else if self._orderInstruction.value == .buy {
                self.tracker.trackEvent(EventInfo.buyPriceInputBidClick(defaultParameters: self.tracker.defaultParameters))
            }
        }).disposed(by: bag)

        _didClickDoneButton.take(1).subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            if self._orderInstruction.value == .buy {
                self.tracker.trackEvent(EventInfo.sellPriceClickDone(defaultParameters: self.tracker.defaultParameters))
            }
        }).disposed(by: bag)
    }

    // MARK: inputs

    open func didUpdateVCMCoolingPeriodSection(isInPeriod: Bool?, limitRange: ClosedRange<Decimal>?, startTime: Date?, endTime: Date?) {
        // Volatility Control Mechanism (VCM) Cooling Period
        _isInVCMPeriod.accept(isInPeriod)
        _vcmUpperLimit.accept(limitRange?.upperBound)
        _vcmLowerLimit.accept(limitRange?.lowerBound)
        _vcmCoolingPeriodStartTime.accept(startTime)
        _vcmCoolingPeriodEndTime.accept(endTime)
    }

    open func didUpdateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        _currency.accept(quote.currency)
        if let market = quote.stockMarket {
            _market.accept(market)
            _exchangeTimezone.accept(market.timeZoneAbbreviation)
            _dayLowPrice.accept(quote.dayLow)
            _dayHighPrice.accept(quote.dayHigh)
            _bidPrice.accept(quote.bidPrice)
            _askPrice.accept(quote.askPrice)
            _isDelayedQuote.accept(quote.isDelay)
            if let updatedTime = quote.exchangeUpdatedTime {
                _stockQuoteUpdateTime.accept(updatedTime.stringWithDateFormat(localizedString("date_format_date_time"), timeZone: market.timeZone))
            }
        }
        if let productType = quote.productType {
            stockTradePresenter?.didEditSection(section: .productType(type: productType))
        }
    }

    open func didUpdatePrice(price: String) {
        if price.isEmpty {
            stockTradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: nil))
        } else if validatePrice(price: price) {
            let proposedPriceDecimal = Decimal(string: price)
            stockTradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: proposedPriceDecimal))
        }
    }

    func didKeyboardInput() {
        _priceTrailingZero.accept(false)
    }

    open func didClickBidButton() {
        _priceTrailingZero.accept(true)
        _didClickBidButton.accept(())

        _bidPrice.take(1).subscribe(onNext: { [weak self] bidPrice in
            guard let self = self else { return }
            if let price = bidPrice {
                self.stockTradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: price))
            }
        }).disposed(by: bag)
    }

    open func didClickAskButton() {
        _priceTrailingZero.accept(true)
        _didClickAskButton.accept(())

        _askPrice.take(1).subscribe(onNext: { [weak self] askPrice in
            guard let self = self else { return }
            if let price = askPrice {
                self.stockTradePresenter?.didEditSection(section: .proposedPrice(proposedPrice: price))
            }
        }).disposed(by: bag)
    }

    open func didClickHelpButton() {
        view?.displayPriceExplainedView()
        _didClickHelpButton.accept(())
    }

    open func didClickDoneButton() {
        if let price = _proposedPrice.value {
            stockTradePresenter?.didEditSection(section: .price(price: price))
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }
                self.view?.dismissView()
            }
            _didClickDoneButton.accept(())
        }
    }

    open func didClickCloseButton() {
        view?.dismissView()
    }

    open func didClickPriceIncreaseButton() {
        _priceTrailingZero.accept(true)
        stockTradePresenter?.attemptIncreaseProposedPrice()
    }

    open func didClickPriceDecreaseButton() {
        _priceTrailingZero.accept(true)
        stockTradePresenter?.attemptDecreaseProposedPrice()
    }

    open func didUpdatePreOpeningSessionSection(isEligible: Bool, referencePrice: Decimal?, buyLimitRange: ClosedRange<Decimal>?, sellLimitRange: ClosedRange<Decimal>?) {
        _isPOSEligible.accept(isEligible)
        _posReferencePrice.accept(referencePrice)
        _posBuyLimitRange.accept(buyLimitRange)
        _posSellLimitRange.accept(sellLimitRange)
    }

    open func didUpdateTradingPeriodType(type: HSBCTradingPeriodType) {
        _tradingPeriodType.accept(type)
    }

    func validatePrice(price: String) -> Bool {
        let notAllowedCharSet = NSCharacterSet(charactersIn: "0123456789.").inverted
        if let range = price.rangeOfCharacter(from: notAllowedCharSet), !range.isEmpty {
            return false
        } else {
            let components = price.components(separatedBy: ".")
            if components.count > 2 {
                return false
            } else {
                if components.count == 1 {
                    if components[0].count > 9 {
                        return false
                    }
                    return true
                } else {
                    if let market = _market.value {
                        // If presenter support `decimalPlaces`. eg. WX Singapore
                        if let decimalPlaces = _decimalPlaces.value {
                            return components[1].count <= decimalPlaces
                        } else { // WX Hong Kong
                            switch market {
                            case .hongKong:
                                return components[1].count <= 3 ? true : false
                            default:
                                return components[1].count <= 2 ? true : false
                            }
                        }
                    } else {
                        return components[1].count <= 2 ? true : false
                    }
                }
            }
        }
    }

    // MARK: outputs

    var isVCMCoolingPeriod: Driver<Bool> {
        _isInVCMPeriod.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var vcmCoolingPeriodStartTime: Driver<Date> {
        _vcmCoolingPeriodStartTime.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var vcmCoolingPeriodEndTime: Driver<Date> {
        _vcmCoolingPeriodEndTime.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var vcmLowerLimit: Driver<Decimal> {
        _vcmLowerLimit.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var vcmUpperLimit: Driver<Decimal> {
        _vcmUpperLimit.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var market: Driver<WXMarket> {
        _market.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var price: Driver<Decimal?> {
        _price.asDriver().distinctUntilChanged()
    }

    var proposedPrice: Driver<Decimal?> {
        _proposedPrice.asDriver() // remove distinctUntilChanged in here, since proposed price might not be updated on every input in some special characters handling cases
    }

    var dayLowPrice: Driver<Decimal?> {
        _dayLowPrice.asDriver().distinctUntilChanged()
    }

    var dayHighPrice: Driver<Decimal?> {
        _dayHighPrice.asDriver().distinctUntilChanged()
    }

    var bidPrice: Driver<Decimal?> {
        _bidPrice.asDriver().distinctUntilChanged()
    }

    var askPrice: Driver<Decimal?> {
        _askPrice.asDriver().distinctUntilChanged()
    }

    var currency: Driver<WXCurrencyCode> {
        _currency.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var stockQuoteUpdateTime: Driver<String?> {
        _stockQuoteUpdateTime.asDriver().distinctUntilChanged()
    }

    var exchangeTimezone: Driver<String?> {
        _exchangeTimezone.asDriver().distinctUntilChanged()
    }

    var proposedPriceError: Driver<HSBCErrorCode> {
        _proposedPriceError.asDriver()
    }

    var spreadInterval: Driver<Decimal> {
        _spreadInterval.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var priceTrailingZero: Driver<Bool> {
        _priceTrailingZero.asDriver().distinctUntilChanged()
    }

    var orderType: Driver<HSBCStockTradeOrderType> {
        _orderType.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var isDelayedQuote: Driver<Bool?> {
        _isDelayedQuote.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var isMDSDown: Driver<Bool> {
        _isMDSDown.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    var decimalPlaces: Driver<Int> {
        _decimalPlaces.asDriver().flatMap(ignoreNil).distinctUntilChanged()
    }

    public func didUpdatePriceSection(price: Decimal, currency: WXCurrencyCode) {
        _price.accept(price)
    }

    public func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _orderInstruction.accept(instruction)
    }

    public func didUpdateProposedPriceSection(proposedPrice: Decimal?, limitRange: ClosedRange<Decimal>?, spreadInterval: Decimal?, currency: WXCurrencyCode, validRange: ClosedRange<Decimal>, error: HSBCErrorCode) {
        _proposedPrice.accept(proposedPrice)
        _proposedPriceError.accept(error)
        _limitRange.accept(limitRange)
        _spreadInterval.accept(spreadInterval)
        _validSpreadRange.accept(validRange)
    }

    public func didUpdateSelectedOrderType(type: HSBCStockTradeOrderType) {
        _orderType.accept(type)
    }

    public func didUpdateIsMDSDown(isMDSDown: Bool) {
        _isMDSDown.accept(isMDSDown)
    }

    public func didUpdateDecimalPlaces(decimalPlaces: Int) {
        _decimalPlaces.accept(decimalPlaces)
    }
}

extension _PriceInputViewModel: StockTradeView {
    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        inputs.didUpdateInstructionSection(instruction: instruction)
    }

    public func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        inputs.didUpdateSelectedOrderType(type: type)
    }

    public func updatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        inputs.didUpdatePriceSection(price: price, currency: currency)
    }

    public func updateProposedPriceSection(proposedPrice: Decimal?, limitRange: ClosedRange<Decimal>?, currency: WXCurrencyCode, spreadTableInterval: Decimal?, validSpreadRange: ClosedRange<Decimal>, error: HSBCErrorCode) {
        inputs.didUpdateProposedPriceSection(proposedPrice: proposedPrice, limitRange: limitRange, spreadInterval: spreadTableInterval, currency: currency, validRange: validSpreadRange, error: error)
    }
}

extension _PriceInputViewModel: StockQuoteView {
    public func updateVCMCoolingPeriodSection(isInPeriod: Bool?, limitRange: ClosedRange<Decimal>?, startTime: Date?, endTime: Date?) {
        inputs.didUpdateVCMCoolingPeriodSection(isInPeriod: isInPeriod, limitRange: limitRange, startTime: startTime, endTime: endTime)
    }

    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        inputs.didUpdateStockQuoteSection(quote: quote, error: error)
    }

    public func updateIsMDSDownSection(isMDSDown: Bool, error: HSBCErrorCode) {
        inputs.didUpdateIsMDSDown(isMDSDown: isMDSDown)
    }

    public func updateDecimalPlacesSection(decimalPlaces: Int, error: HSBCErrorCode) {
        inputs.didUpdateDecimalPlaces(decimalPlaces: decimalPlaces)
    }

    public func updatePreOpeningSessionSection(isEligible: Bool, referencePrice: Decimal?, buyLimitRange: ClosedRange<Decimal>?, sellLimitRange: ClosedRange<Decimal>?) {
        inputs.didUpdatePreOpeningSessionSection(isEligible: isEligible, referencePrice: referencePrice, buyLimitRange: buyLimitRange, sellLimitRange: sellLimitRange)
    }

    public func updateTradingPeriodType(type: HSBCTradingPeriodType) {
        inputs.didUpdateTradingPeriodType(type: type)
    }
}
