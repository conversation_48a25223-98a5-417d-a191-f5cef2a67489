//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import UIKit

import MobileDesign
import RxCocoa
import RxSwift
import UIKit
// import wealthxcore
#warning("demo only")
class _PriceInputViewController: DismissableViewController {
    var viewModel: _PriceInputViewModel!

    // For accessibility only
    weak var accessibilityListener: AccessibilityDelegate?
    private weak var selectedAccessibilityView: UIView?

    struct Colors {
        static let blackColor = ThemeManager.shared.currentTheme.colors.textPrimary
        static let lightGrayed = ThemeManager.shared.currentTheme.colors.textSecondary
    }

    enum ViewType {
        case stockBuy
        case stockSell
    }

    // Rx dispose bag
    private let bag = DisposeBag()

    private let priceTitleLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.body
        label.text = localizedString("Price")
        label.isAccessibilityElement = true
        label.accessibilityTraits = .header
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        return label
    }()

    private let infoButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.clipsToBounds = true
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        button.setImage(
            ThemeManager.shared.currentTheme.systemIcons.help.withRenderingMode(.alwaysTemplate),
            for: .normal
        )
        button.accessibilityLabel = String(format: "%@", localizedString("wx_accessibility_order_price"))
        return button
    }()

    public let closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.clipsToBounds = true
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        button.setImage(
            ThemeManager.shared.currentTheme.systemIcons.close.withRenderingMode(.alwaysTemplate),
            for: .normal
        )
        button.isAccessibilityElement = true
        button.accessibilityLabel = localizedString("close")
        return button
    }()

    private let dayHighLowLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.body
        label.textColor = Colors.blackColor
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.numberOfLines = 0
        return label
    }()

    private let specialPeriodNameLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.body
        label.textColor = Colors.blackColor
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        return label
    }()

    private let casReminderLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.footnote
        label.textColor = Colors.blackColor
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.numberOfLines = 0
        return label
    }()

    private let refreshedTimeLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.caption
        label.textColor = Colors.lightGrayed
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.numberOfLines = 0
        return label
    }()

    private let currencyLabel: UILabel = {
        let label = UILabel()
        label.textColor = Colors.blackColor
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        return label
    }()

    private let priceTextField: UITextField = {
        let textField = UITextField()
        textField.keyboardType = .decimalPad
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.adjustsFontForContentSizeCategory = true
        textField.font = UIFont.preferredFont(forTextStyle: .body)
        return textField
    }()

    private var cursorPosition: Int?

    private lazy var inlineStatusView: InlineStatusView = {
        let messageView = InlineStatusView(status: InlineStatusView.Status.warning, text: AccessibleValue(""))
        messageView.isHidden = true
        messageView.translatesAutoresizingMaskIntoConstraints = false
        return messageView
    }()

    private let priceIncreaseButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.setImage(ThemeManager.shared.currentTheme.systemIcons.addCircle.withRenderingMode(.alwaysTemplate), for: .normal)
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        return button
    }()

    private let priceDecreaseButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.setImage(ThemeManager.shared.currentTheme.systemIcons.subtractCircle.withRenderingMode(.alwaysTemplate), for: .normal)
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        return button
    }()

    private var bidAndAskStackViewAndInputTextBaseViewConstraint: NSLayoutConstraint?

    private let bidPriceButton: SecondaryButton = {
        // https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-8503
        let button = SecondaryButton(title: String(format: localizedString("text_bid_price"), localizedString("default_item_value")))
        return button
    }()

    private let askPriceButton: SecondaryButton = {
        // https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-8503
        let button = SecondaryButton(title: String(format: localizedString("text_ask_price"), localizedString("default_item_value")))
        return button
    }()

    private let ctaButton: PrimaryButton = {
        let button = PrimaryButton(title: localizedString("btn_done"), state: .disabled)
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()

    private let baseView = UIView()

    private let stockInfoStackView: UIStackView = {
        let stockInfoStackView = UIStackView()
        stockInfoStackView.translatesAutoresizingMaskIntoConstraints = false
        stockInfoStackView.axis = .vertical
        stockInfoStackView.alignment = .center
        stockInfoStackView.distribution = .fill
        return stockInfoStackView
    }()

    private var keyboardAwareLayoutGuide = UIKeyboardAwareGuide()

    init() {
        super.init(nibName: nil, bundle: nil)
        viewModel = StockTradeJourneyFactory.getObjectFromSuffix(_PriceInputViewModel.self)
        viewModel.setupView(view: self)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        keyboardAwareLayoutGuide.owningViewController = self
        setupUI()
        setupAccessibilityIds()
        setupViewModel()
        viewModel.inputs.viewDidLoad()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            UIAccessibility.post(
                notification: .layoutChanged,
                argument: self.priceTitleLabel
            )
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if !UIAccessibility.isVoiceOverRunning {
            priceTextField.becomeFirstResponder()
        } else {
            closeButton.becomeFirstResponder()
            closeButton.updateFocusIfNeeded()
        }
        // change accessibility focus
        if let selectedAccessibilityView = self.selectedAccessibilityView {
            self.selectedAccessibilityView = nil
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                UIAccessibility.post(
                    notification: .layoutChanged,
                    argument: selectedAccessibilityView
                )
            }
        }
    }

    func setupUI() {
        view.backgroundColor = ThemeManager.shared.currentTheme.colors.dimmedBackground

        baseView.translatesAutoresizingMaskIntoConstraints = false
        baseView.layer.cornerRadius = 4.0
        baseView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.addSubview(baseView)

        NSLayoutConstraint.activate([
            baseView.leftAnchor.constraint(equalTo: view.leftAnchor),
            baseView.rightAnchor.constraint(equalTo: view.rightAnchor),
            baseView.bottomAnchor.constraint(equalTo: keyboardAwareLayoutGuide.topAnchor)
        ])

        baseView.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        let headerView = UIView()
        headerView.translatesAutoresizingMaskIntoConstraints = false
        baseView.addSubview(headerView)

        NSLayoutConstraint.activate([
            headerView.leftAnchor.constraint(equalTo: baseView.leftAnchor),
            headerView.rightAnchor.constraint(equalTo: baseView.rightAnchor),
            headerView.topAnchor.constraint(equalTo: baseView.topAnchor, constant: 16.0),
            headerView.heightAnchor.constraint(equalToConstant: 36.0)
        ])

        headerView.addSubview(priceTitleLabel)
        headerView.addSubview(infoButton)
        headerView.addSubview(closeButton)

        priceTitleLabel.setNeedsFocusUpdate()

        NSLayoutConstraint.activate([
            priceTitleLabel.leftAnchor.constraint(equalTo: headerView.leftAnchor, constant: 16.0),
            priceTitleLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            infoButton.leftAnchor.constraint(equalTo: priceTitleLabel.rightAnchor, constant: 10.0),
            closeButton.rightAnchor.constraint(equalTo: headerView.rightAnchor, constant: -16.0),
            infoButton.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            closeButton.centerYAnchor.constraint(equalTo: headerView.centerYAnchor)
        ])

        baseView.addSubview(stockInfoStackView)
        let spaceViewForStockInfoStockView = UIView()
        spaceViewForStockInfoStockView.translatesAutoresizingMaskIntoConstraints = false

        stockInfoStackView.addArrangedSubview(dayHighLowLabel)
        stockInfoStackView.addArrangedSubview(spaceViewForStockInfoStockView)
        stockInfoStackView.addArrangedSubview(refreshedTimeLabel)

        NSLayoutConstraint.activate([
            stockInfoStackView.leftAnchor.constraint(equalTo: baseView.leftAnchor),
            stockInfoStackView.rightAnchor.constraint(equalTo: baseView.rightAnchor),
            stockInfoStackView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 16.0),
            spaceViewForStockInfoStockView.heightAnchor.constraint(equalToConstant: 4.0)
        ])

        let inputTextBaseView = UIView()
        inputTextBaseView.translatesAutoresizingMaskIntoConstraints = false
        baseView.addSubview(inputTextBaseView)

        NSLayoutConstraint.activate([
            inputTextBaseView.leftAnchor.constraint(equalTo: baseView.leftAnchor),
            inputTextBaseView.rightAnchor.constraint(equalTo: baseView.rightAnchor),
            inputTextBaseView.topAnchor.constraint(equalTo: stockInfoStackView.bottomAnchor, constant: 8.0)
        ])

        let priceLabel = UILabel()
        priceLabel.font = ThemeManager.shared.currentTheme.fonts.body
        priceLabel.textColor = Colors.blackColor
        priceLabel.translatesAutoresizingMaskIntoConstraints = false
        priceLabel.adjustsFontForContentSizeCategory = true
        priceLabel.text = localizedString("enter_a_price")

        let separaterView = UIView()
        separaterView.backgroundColor = ThemeManager.shared.currentTheme.colors.textPrimary
        separaterView.translatesAutoresizingMaskIntoConstraints = false

        inputTextBaseView.addSubview(priceLabel)
        inputTextBaseView.addSubview(priceTextField)
        inputTextBaseView.addSubview(currencyLabel)
        inputTextBaseView.addSubview(separaterView)
        inputTextBaseView.addSubview(casReminderLabel)
        inputTextBaseView.addSubview(priceIncreaseButton)
        inputTextBaseView.addSubview(priceDecreaseButton)
        inputTextBaseView.addSubview(inlineStatusView)

        currencyLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)

        NSLayoutConstraint.activate([
            priceLabel.leftAnchor.constraint(equalTo: inputTextBaseView.leftAnchor, constant: 16.0),
            priceLabel.rightAnchor.constraint(equalTo: inputTextBaseView.rightAnchor, constant: -16.0),
            priceLabel.topAnchor.constraint(equalTo: inputTextBaseView.topAnchor, constant: 16.0),
            priceTextField.topAnchor.constraint(equalTo: priceLabel.bottomAnchor, constant: 5.0),
            priceTextField.leftAnchor.constraint(equalTo: inputTextBaseView.leftAnchor, constant: 16.0),
            priceTextField.rightAnchor.constraint(equalTo: currencyLabel.leftAnchor, constant: -10.0),
            priceTextField.heightAnchor.constraint(greaterThanOrEqualToConstant: 30.0),
            currencyLabel.topAnchor.constraint(equalTo: priceLabel.bottomAnchor, constant: 5.0),
            currencyLabel.rightAnchor.constraint(equalTo: priceDecreaseButton.leftAnchor, constant: -24.0),
            separaterView.topAnchor.constraint(equalTo: priceTextField.bottomAnchor),
            separaterView.heightAnchor.constraint(equalToConstant: 1.0),
            separaterView.leftAnchor.constraint(equalTo: inputTextBaseView.leftAnchor, constant: 16.0),
            separaterView.rightAnchor.constraint(equalTo: currencyLabel.rightAnchor),
            casReminderLabel.leadingAnchor.constraint(equalTo: separaterView.leadingAnchor),
            casReminderLabel.topAnchor.constraint(equalTo: separaterView.bottomAnchor, constant: 5),
            casReminderLabel.rightAnchor.constraint(equalTo: inputTextBaseView.rightAnchor, constant: -16.0),
            priceDecreaseButton.rightAnchor.constraint(equalTo: priceIncreaseButton.leftAnchor, constant: -24.0),
            priceIncreaseButton.rightAnchor.constraint(equalTo: inputTextBaseView.rightAnchor, constant: -16.0),
            priceDecreaseButton.bottomAnchor.constraint(equalTo: priceIncreaseButton.bottomAnchor),
            priceIncreaseButton.centerYAnchor.constraint(equalTo: currencyLabel.centerYAnchor),

            // In line message view
            inlineStatusView.leftAnchor.constraint(equalTo: inputTextBaseView.leftAnchor, constant: 16.0),
            inlineStatusView.rightAnchor.constraint(equalTo: inputTextBaseView.rightAnchor, constant: -16.0),
            inlineStatusView.topAnchor.constraint(equalTo: casReminderLabel.bottomAnchor, constant: -10),
            inlineStatusView.bottomAnchor.constraint(equalTo: inputTextBaseView.bottomAnchor, constant: 10)
        ])

        let bidAndAskStackView = UIStackView()
        bidAndAskStackView.translatesAutoresizingMaskIntoConstraints = false
        bidAndAskStackView.axis = .horizontal
        bidAndAskStackView.distribution = .fill
        let spacingView = UIView()
        spacingView.translatesAutoresizingMaskIntoConstraints = false

        baseView.addSubview(bidAndAskStackView)
        bidAndAskStackView.addArrangedSubview(bidPriceButton)
        bidAndAskStackView.addArrangedSubview(spacingView)
        bidAndAskStackView.addArrangedSubview(askPriceButton)

        view.addSubview(ctaButton)

        bidAndAskStackViewAndInputTextBaseViewConstraint = bidAndAskStackView.topAnchor.constraint(equalTo: inputTextBaseView.bottomAnchor, constant: 20.0)
        bidAndAskStackViewAndInputTextBaseViewConstraint?.isActive = true
        NSLayoutConstraint.activate([
            spacingView.widthAnchor.constraint(equalToConstant: 30.0),
            bidPriceButton.widthAnchor.constraint(equalTo: askPriceButton.widthAnchor),
            bidAndAskStackView.leftAnchor.constraint(equalTo: baseView.leftAnchor, constant: 16.0),
            bidAndAskStackView.rightAnchor.constraint(equalTo: baseView.rightAnchor, constant: -16.0),
            ctaButton.topAnchor.constraint(equalTo: bidAndAskStackView.bottomAnchor, constant: 10),
            ctaButton.bottomAnchor.constraint(equalTo: baseView.bottomAnchor, constant: -16.0),
            ctaButton.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 16.0),
            ctaButton.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -16.0),
            ctaButton.heightAnchor.constraint(greaterThanOrEqualToConstant: 48.0)
        ])
    }

    func handleCASInlineMessage(_ error: HSBCErrorCode, upperLimit: Decimal?, lowerLimit: Decimal?, market: WXMarket?) {
        let showAuctionPeriod: ((Bool) -> Void) = { [weak self] isAuctionPeriod in
            guard let self = self else { return }
            if self.stockInfoStackView.contains(self.dayHighLowLabel) {
                self.dayHighLowLabel.removeFromSuperview()
            }
            if self.stockInfoStackView.contains(self.specialPeriodNameLabel) {
                self.specialPeriodNameLabel.removeFromSuperview()
            }

            self.stockInfoStackView.insertArrangedSubview(isAuctionPeriod ? self.specialPeriodNameLabel : self.dayHighLowLabel, at: 0)

            if isAuctionPeriod {
                self.casReminderLabel.isHidden = false
                self.casReminderLabel.text = String(format: localizedString("text_price_input_in_cas_range"), PriceStringFormattingUtil.formattedPriceString(price: lowerLimit, market: market, shouldDiplayCurrencyCode: false) ?? "N/A", PriceStringFormattingUtil.formattedPriceString(price: upperLimit, market: market, shouldDiplayCurrencyCode: false) ?? "N/A")
                // "Enter a price between \(lowerLimit) - \(upperLimit)"
            } else {
                self.casReminderLabel.isHidden = true
                self.casReminderLabel.text = ""
            }
        }

        switch error {
        case .casPriceOutOfRange:
            bidAndAskStackViewAndInputTextBaseViewConstraint?.constant = 20
            specialPeriodNameLabel.text = localizedString("text_sub_title_price_model")
            showAuctionPeriod(true)
        default:
            setInlineStatusView(isHidden: true)
            inlineStatusView.text = AccessibleValue("")
            specialPeriodNameLabel.text = localizedString("text_sub_title_price_model") // "Auction Period"
            showAuctionPeriod(true)
        }
    }

    func handleVCMInlineMessage(_ error: HSBCErrorCode, upperLimit: Decimal, lowerLimit: Decimal, market: WXMarket?) {
        if let lowerLimitString = PriceStringFormattingUtil.formattedPriceString(price: lowerLimit, market: market),
            let upperLimiteString = PriceStringFormattingUtil.formattedPriceString(price: upperLimit, market: market) {
            casReminderLabel.isHidden = false
            casReminderLabel.text = String(format: localizedString("text_price_input_in_cas_range"), "\(lowerLimitString)", "\(upperLimiteString)")
        } else {
            setInlineStatusView(isHidden: true)
            casReminderLabel.text = ""
        }
    }

    private func setupAccessibilityIds() {
        bidPriceButton.accessibilityIdentifier = "button_bid_price"
        askPriceButton.accessibilityIdentifier = "button_ask_price"
        ctaButton.accessibilityIdentifier = "button_price_done"
        priceTextField.accessibilityIdentifier = "edittext_price"
        priceTextField.accessibilityLabel = localizedString("enter_a_price")
        currencyLabel.accessibilityIdentifier = "text_currency"
        refreshedTimeLabel.accessibilityIdentifier = "text_date"
        dayHighLowLabel.accessibilityIdentifier = "text_price_day_low_day_high"
        infoButton.accessibilityIdentifier = "image_price_help"
        closeButton.accessibilityIdentifier = "button_close"
        inlineStatusView.accessibilityIdentifier = "text_price_warning"
        specialPeriodNameLabel.accessibilityIdentifier = "text_sub_title_price_model"
        casReminderLabel.accessibilityIdentifier = "text_out_of_cas_range"

        // Minus button
        priceDecreaseButton.accessibilityIdentifier = "image_minus_quantity"
        priceDecreaseButton.accessibilityLabel = localizedString("alt_decrease_price")

        // Add button
        priceIncreaseButton.accessibilityIdentifier = "image_add_quantity"
        priceIncreaseButton.accessibilityLabel = localizedString("alt_increase_price")
    }

    func setupViewModel() {
        priceTextField.delegate = self

        infoButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickHelpButton()
            // For accessibility only
            self.selectedAccessibilityView = self.infoButton
        }.disposed(by: bag)

        // Bind price increase button
        priceIncreaseButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickPriceIncreaseButton()
            self.priceTextField.selectedTextRange = self.priceTextField.textRange(from: self.priceTextField.endOfDocument, to: self.priceTextField.endOfDocument)
        }.disposed(by: bag)

        // Bind price decrease button
        priceDecreaseButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickPriceDecreaseButton()
            self.priceTextField.selectedTextRange = self.priceTextField.textRange(from: self.priceTextField.endOfDocument, to: self.priceTextField.endOfDocument)
        }.disposed(by: bag)

        viewModel.outputs.market.asObservable().filter { $0 == .singapore }.subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            self.priceIncreaseButton.isHidden = true
            self.priceDecreaseButton.isHidden = true
            NSLayoutConstraint.activate([
                self.currencyLabel.rightAnchor.constraint(equalTo: self.baseView.rightAnchor, constant: -16.0)
            ])
        }).disposed(by: bag)

        // Update price inputed voiceover
        viewModel._proposedPrice.flatMap(ignoreNil).distinctUntilChanged().scan(Decimal.zero) { oldPrice, newPrice in

            var isIncreasePrice: Bool
            if newPrice - oldPrice >= 0 {
                isIncreasePrice = true
            } else {
                isIncreasePrice = false
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                UIAccessibility.post(notification: UIAccessibility.Notification.announcement, argument: String(format: localizedString(isIncreasePrice ? "alt_price_increased_to" : "alt_price_decreased_to"), "\(newPrice)"))
            }
            return newPrice
        }.asObservable().subscribe(onNext: { proposedPrice in

        }).disposed(by: bag)

        // Driver the price text field text
        Driver.combineLatest(
            viewModel.outputs.market,
            viewModel.outputs.proposedPrice,
            viewModel.outputs.currency.flatMap(ignoreNil),
            viewModel.outputs.decimalPlaces
        ).asObservable().subscribe(onNext: { [weak self] market, price, currency, decimalPlaces in
            guard let self = self else { return }
            if let price = price, !price.isZero {
                let shouldTrailingZero = self.viewModel._priceTrailingZero.value
                let price = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency, market: market, shouldDiplayCurrencyCode: false, shouldDisplayTrailingZero: shouldTrailingZero, shouldGroupBySeparator: false)
                self.priceTextField.text = price
                self.setCursorPosition(self.priceTextField)
            } else {
                self.priceTextField.text = nil
            }
            self.priceTextField.sendActions(for: .valueChanged)
        }).disposed(by: bag)

        Driver.combineLatest(
            viewModel.outputs.price,
            viewModel.outputs.currency.flatMap(ignoreNil),
            viewModel.outputs.decimalPlaces
        ).asObservable().subscribe(onNext: { [weak self] price, currency, decimalPlaces in
            guard let self = self else { return }
            if let price = price, !price.isZero {
                self.priceTextField.text = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency, shouldDiplayCurrencyCode: false, shouldDisplayTrailingZero: true, shouldGroupBySeparator: false)
            } else {
                self.priceTextField.text = nil
            }
        }).disposed(by: bag)

        // MARK: - Volatility Control Mechanism (VCM) Cooling Period

        // Update the `specialPeriodNameLabel` and `dayHighLowLabel` if `isVCMCoolingPeriod` is true
        Driver.combineLatest(viewModel.outputs.isVCMCoolingPeriod, viewModel.outputs.vcmCoolingPeriodStartTime, viewModel.outputs.vcmCoolingPeriodEndTime, viewModel.outputs.orderType).asObservable().subscribe(onNext: { [weak self] isVCMCoolingPeriod, startTime, endTime, orderType in
            guard let self = self else { return }
            if isVCMCoolingPeriod, [HSBCStockTradeOrderType.market, HSBCStockTradeOrderType.limit].contains(orderType) {
                let dateFormat = "HH:mm:ss"

                self.specialPeriodNameLabel.isHidden = false
                self.specialPeriodNameLabel.text = localizedString("title_vcm") // "Cooling off period"

                self.dayHighLowLabel.isHidden = false
                self.dayHighLowLabel.text = String(format: localizedString("text_vcm_start_end"), startTime.stringWithDateFormat(dateFormat), endTime.stringWithDateFormat(dateFormat))

                if self.stockInfoStackView.contains(self.dayHighLowLabel) {
                    self.dayHighLowLabel.removeFromSuperview()
                }

                if self.stockInfoStackView.contains(self.specialPeriodNameLabel) {
                    self.specialPeriodNameLabel.removeFromSuperview()
                }

                self.stockInfoStackView.insertArrangedSubview(self.dayHighLowLabel, at: 0)
                self.stockInfoStackView.insertArrangedSubview(self.specialPeriodNameLabel, at: 0)
            }
        }).disposed(by: bag)

        Driver.combineLatest(
            viewModel.outputs.dayLowPrice, /// accept nil as Inline Warrants won't return day high / low, https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-6895
            viewModel.outputs.dayHighPrice,
            viewModel.outputs.currency.flatMap(ignoreNil),
            viewModel.outputs.market,
            viewModel.outputs.decimalPlaces
        ).asObservable().subscribe(onNext: { [weak self] dayLow, dayHigh, currency, market, decimalPlaces in
            guard let self = self else { return }
            if let dayLow = PriceStringFormattingUtil.formattedPriceString(price: dayLow, decimalPlaces: decimalPlaces, currency: currency, shouldDiplayCurrencyCode: false), let dayHigh = PriceStringFormattingUtil.formattedPriceString(price: dayHigh, decimalPlaces: decimalPlaces, currency: currency, shouldDiplayCurrencyCode: false) {
                let isChinaMarket = [WXMarket.china, WXMarket.shenZhen, WXMarket.shangHai].contains(market)

                self.dayHighLowLabel.text = isChinaMarket ? String(format: localizedString("text_price_low_high"), "\(dayLow)", "\(dayHigh)") : String(format: localizedString("text_price_low_high"), "\(dayLow)", "\(dayHigh)")
//                                    self.dayHighLowLabel.text =  "Day Low : \(dayLow) / Day high : \(dayHigh)"
            }
        }).disposed(by: bag)

        Driver.combineLatest(
            viewModel.outputs.stockQuoteUpdateTime.flatMap(ignoreNil),
            viewModel.outputs.exchangeTimezone.flatMap(ignoreNil),
            viewModel.outputs.isDelayedQuote
        ).asObservable().subscribe(onNext: { [weak self] updateTime, timezone, isDelayedQuote in
            guard let self = self,
                let isDelayedQuote = isDelayedQuote else { return }
            self.refreshedTimeLabel.text = isDelayedQuote ? String(format: localizedString("delayed_quote_at"), "\(updateTime)", "\(timezone)") : String(format: localizedString("real_time_quote_at"), "\(updateTime)", "\(timezone)")

        }).disposed(by: bag)

        // MARK: - Currency label

        viewModel.outputs.currency.map { LocalizationUtil.localizedCurrencyName(code: $0) }.drive(currencyLabel.rx.text).disposed(by: bag)

        // MARK: - Bid price button

        // Bid price button title
        Driver.combineLatest(
            viewModel.outputs.bidPrice.flatMap(ignoreNil),
            viewModel.outputs.currency.flatMap(ignoreNil),
            viewModel.outputs.decimalPlaces
        ).asObservable().subscribe(onNext: { [weak self] price, currency, decimalPlaces in
            guard let self = self else { return }
            if let price = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency, shouldDiplayCurrencyCode: false) {
                self.bidPriceButton.title = String(format: localizedString("text_bid_price"), "\(price)")
                // "Bid: \(price)"
            }
        }).disposed(by: bag)

        /// https://jira-digital.systems.uk.hsbc/jira/browse/WXMA-6888 - AC6
        viewModel.outputs.isMDSDown.filter { $0 == true }.asObservable().subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            self.bidPriceButton.title = String(format: localizedString("text_bid_price"), "N/A")
            self.askPriceButton.title = String(format: localizedString("text_ask_price"), "N/A")
        }).disposed(by: bag)

        // Bid Price button button pressed
        bidPriceButton.buttonPressedCompletion = { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickBidButton()
            self.priceTextField.selectedTextRange = self.priceTextField.textRange(from: self.priceTextField.endOfDocument, to: self.priceTextField.endOfDocument)
        }

        // MARK: - Ask price button

        // Ask price button title
        Driver.combineLatest(
            viewModel.outputs.askPrice.flatMap(ignoreNil),
            viewModel.outputs.currency.flatMap(ignoreNil),
            viewModel.outputs.decimalPlaces
        ).asObservable().subscribe(onNext: { [weak self] price, currency, decimalPlaces in
            guard let self = self else { return }
            if let price = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency, shouldDiplayCurrencyCode: false) {
                self.askPriceButton.title = String(format: localizedString("text_ask_price"), "\(price)")
                // "Ask: \(price)"
            }
        }).disposed(by: bag)

        // Ask Price button button pressed
        askPriceButton.buttonPressedCompletion = { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickAskButton()
            self.priceTextField.selectedTextRange = self.priceTextField.textRange(from: self.priceTextField.endOfDocument, to: self.priceTextField.endOfDocument)
        }

        ctaButton.buttonPressedCompletion = { [weak self] in
            guard let self = self else { return }
            self.viewModel._priceTrailingZero.accept(true)
            if let price = self.priceTextField.text {
                self.viewModel.inputs.didUpdatePrice(price: price) // update proposed price once when CTA button clicked
            }

            self.viewModel.inputs.didClickDoneButton()
        }

        closeButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickCloseButton()
            // For accessibility only
            self.accessibilityListener?.shouldSelectAccessibilityView()
        }.disposed(by: bag)

        // STM-7706 STMA—buy-that price input related bid ask price not selectable and grayout-ios&aos(this defect for ios)
        viewModel
            .outputs
            .askPrice
            .asObservable().subscribe(onNext: { [weak self] askPrice in
                guard let self = self else { return }
                if let _ = askPrice {
                    self.askPriceButton.state = .enabled
                } else {
                    self.askPriceButton.state = .disabled
                }
            }).disposed(by: bag)

        viewModel
            .outputs
            .bidPrice
            .asObservable().subscribe(onNext: { [weak self] bidPrice in
                guard let self = self else { return }
                if let _ = bidPrice {
                    self.bidPriceButton.state = .enabled
                } else {
                    self.bidPriceButton.state = .disabled
                }
            }).disposed(by: bag)
    }

    override func handleDragOrTapToDismiss() {
        viewModel.inputs.didClickCloseButton()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // Assign the keyboard reference every time the view controlelr is back to foreground
        if keyboardAwareLayoutGuide.owningViewController != self {
            keyboardAwareLayoutGuide.owningViewController = self
        }
    }

    deinit {
        keyboardAwareLayoutGuide.owningViewController = nil
        viewModel.inputs.viewDeinit()
    }
}

extension _PriceInputViewController: PriceInputView {
    func displayPOSHeaderMessage() {
        if stockInfoStackView.contains(dayHighLowLabel) {
            dayHighLowLabel.removeFromSuperview()
        }

        if stockInfoStackView.contains(specialPeriodNameLabel) {
            specialPeriodNameLabel.removeFromSuperview()
        }

        specialPeriodNameLabel.text = localizedString("text_sub_title_price_model") // Auction period
        dayHighLowLabel.text = ""

        stockInfoStackView.insertArrangedSubview(dayHighLowLabel, at: 0)
        stockInfoStackView.insertArrangedSubview(specialPeriodNameLabel, at: 0)
    }

    func displaySpreadIncrementalInlineMessage(spreadInterval: Decimal, market: WXMarket, currency: WXCurrencyCode) {
        if let price = PriceStringFormattingUtil.formattedPriceString(price: spreadInterval, currency: currency, market: market, shouldDiplayCurrencyCode: true, shouldDisplayTrailingZero: true) {
            setInlineStatusView(isHidden: false)
            inlineStatusView.text = AccessibleValue(String(format: localizedString("text_price_input_violate_spread_table"), "\(price)"))
        }
    }

    func displayOutOfSpreadTableInlineMessage() {
        setInlineStatusView(isHidden: false)
        inlineStatusView.text = AccessibleValue(localizedString("urs0292"))
    }

    func displayCASInlineMessage(errorCode: HSBCErrorCode, upperLimit: Decimal?, lowerLimit: Decimal?, market: WXMarket?) {
        handleCASInlineMessage(errorCode, upperLimit: upperLimit, lowerLimit: lowerLimit, market: market)
    }

    func displayVCMInlineMessage(errorCode: HSBCErrorCode, upperLimit: Decimal, lowerLimit: Decimal, market: WXMarket?) {
        handleVCMInlineMessage(errorCode, upperLimit: upperLimit, lowerLimit: lowerLimit, market: market)
    }

    func displayExceededProactivePriceAlertInlineMessage() {
        inlineStatusView.text = AccessibleValue(localizedString("text_price_ten_percent_higher_than_previous_value"))
        setInlineStatusView(isHidden: false)
    }

    func displayExceededPreOpeningSessionProactivePriceAlertInlineMessage() {
        inlineStatusView.text = AccessibleValue(localizedString("text_price_fifteen_percent_higher_than_previous_value"))
        setInlineStatusView(isHidden: false)
    }

    func hideInlineStatusView() {
        setInlineStatusView(isHidden: true)
        casReminderLabel.isHidden = true
        inlineStatusView.text = AccessibleValue("")
    }

    func setInlineStatusView(isHidden: Bool) {
        inlineStatusView.isHidden = isHidden
        if isHidden {
            bidAndAskStackViewAndInputTextBaseViewConstraint?.constant = 40
        } else {
            bidAndAskStackViewAndInputTextBaseViewConstraint?.constant = 20
        }
    }

    func disablePriceIncreaseButton() {
        priceIncreaseButton.isEnabled = false
        priceIncreaseButton.tintColor = ThemeManager.shared.currentTheme.colors.textSecondary
    }

    func enablePriceIncreaseButton() {
        priceIncreaseButton.isEnabled = true
        priceIncreaseButton.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
    }

    func disablePriceDecreaseButton() {
        priceDecreaseButton.isEnabled = false
        priceDecreaseButton.tintColor = ThemeManager.shared.currentTheme.colors.textSecondary
    }

    func enablePriceDecreaseButton() {
        priceDecreaseButton.isEnabled = true
        priceDecreaseButton.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
    }

    func dismissView() {
        dismiss(animated: true, completion: nil)
    }

    func enableDoneButton() {
        ctaButton.state = .enabled
    }

    func disableDoneButton() {
        ctaButton.state = .disabled
    }

    func enableBidButton() {
        bidPriceButton.isUserInteractionEnabled = true
    }

    func disableBidButton() {
        bidPriceButton.isUserInteractionEnabled = false
    }

    func enableAskButton() {
        askPriceButton.isUserInteractionEnabled = true
    }

    func disableAskButton() {
        askPriceButton.isUserInteractionEnabled = false
    }

    func displayPriceExplainedView() {
        // Dismiss the keyboard before entering the another screen
        view.endEditing(true)

//        let helpViewController = ToolTipScreenViewController(with: localizedString("price_explained_title"), content: localizedString("price_explained_content"), tracker: tracker)
//        guard let viewType = viewType else { return }
//        switch viewType {
//        case .stockBuy:
//            helpViewController.pageEventInfo = EventInfo.buyPriceHelpPageLoad(defaultParameters: tracker.defaultParameters)
//        case .stockSell:
//            helpViewController.pageEventInfo = EventInfo.sellWhatIsPricePage(defaultParameters: tracker.defaultParameters)
//        }
        let priceInputLearnMoreScreen = StockTradeJourneyFactory.getObjectFromSuffix(PriceInputLearnMoreScreen.self)
        priceInputLearnMoreScreen.setListener(self)
        present(priceInputLearnMoreScreen.embedInNavigationController(), animated: true, completion: nil)
    }

    func displayLearnMoreView() {}
}

extension _PriceInputViewController: UITextFieldDelegate {
    func getCursorPosition(_ textField: UITextField, isAppend: Bool = true) -> Int? {
        if let selectedRange = textField.selectedTextRange {
            let cursorPosition = textField.offset(from: textField.beginningOfDocument, to: selectedRange.start)
            return cursorPosition + (isAppend ? 1 : -1)
        }
        return nil
    }

    func setCursorPosition(_ textField: UITextField) {
        if let currentValue: Int = cursorPosition {
            if let previousPosition = textField.position(from: textField.beginningOfDocument, offset: currentValue) {
                textField.selectedTextRange = textField.textRange(from: previousPosition, to: previousPosition)
            }
        }
    }

    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if let text = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) {
            viewModel.inputs.didKeyboardInput()
            cursorPosition = getCursorPosition(textField, isAppend: string.count > 0)
            return processPriceInputChange(text: text, input: string)
        }
        return false
    }

    private func processPriceInputChange(text: String, input: String) -> Bool {
        // decimalPad will display "," instead of "." in France region , so we need convert logic. defect STMA-7405
        let textStr = text.replacingOccurrences(of: ",", with: ".")
        var inputStr = input
        if input == "," {
            inputStr = "."
        }
        switch inputStr {
        case ".":
            if textStr.count == 1 /* start from . */ || textStr.filter({ char -> Bool in
                char.description.contains(".")
            }).count > 1 {
                return false
            } else if cursorPosition == textStr.count { // append "." at tail.
                return true
            } else {
                viewModel.inputs.didUpdatePrice(price: textStr)
            }
        case "0":
            let decimalPlaces: Int = viewModel._decimalPlaces.value ?? 2 /// temp fix for SG
            if textStr.count == 1 /* start from 0 */ {
                viewModel.inputs.didUpdatePrice(price: textStr)
                return true
            } else if !textStr.contains(".") {
                viewModel.inputs.didUpdatePrice(price: textStr)
            } else if textStr.contains("."), let range = textStr.range(of: "."), textStr[range.upperBound...].count <= decimalPlaces {
                return true
            } else {
                return false
            }
        case "": // delete case
            if let lastCharacter = textStr.last, [".", "0"].contains(lastCharacter), NSDecimalNumber(string: textStr).decimalValue == 0.0 {
                return true
            } else {
                viewModel.inputs.didUpdatePrice(price: textStr)
                return false
            }
        default:
            viewModel.inputs.didUpdatePrice(price: textStr)
        }
        return false
    }
}

extension _PriceInputViewController: WXInfoScreenViewControllerListener {
    func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true)
    }

    func showModule(_ module: WXInfoScreenViewController) {
        /* empty */
    }
}
