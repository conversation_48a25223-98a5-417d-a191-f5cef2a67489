//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit
#warning("demo only")
protocol _PriceInputViewModelInputs: AnyObject {
    func didKeyboardInput()
    func didClickBidButton()
    func didClickAskButton()
    func didClickHelpButton()

    func didClickDoneButton()
    func didClickCloseButton()
    func didUpdatePrice(price: String)

    func viewDidLoad()
    func viewDidDisappear()
    func viewDeinit()

    func didUpdateSelectedOrderType(type: HSBCStockTradeOrderType)
    func didUpdateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode)
    func didUpdateVCMCoolingPeriodSection(isInPeriod: Bool?, limitRange: ClosedRange<Decimal>?, startTime: Date?, endTime: Date?)
    func didUpdateProposedPriceSection(proposedPrice: Decimal?, limitRange: ClosedRange<Decimal>?, spreadInterval: Decimal?, currency: WX<PERSON>urrencyCode, validRange: ClosedRange<Decimal>, error: HSBCErrorCode)
    func didUpdateIsMDSDown(isMDSDown: Bool)
    func didUpdateDecimalPlaces(decimalPlaces: Int)

    func didClickPriceIncreaseButton()
    func didClickPriceDecreaseButton()

    func didUpdatePreOpeningSessionSection(isEligible: Bool, referencePrice: Decimal?, buyLimitRange: ClosedRange<Decimal>?, sellLimitRange: ClosedRange<Decimal>?)

    func didUpdateTradingPeriodType(type: HSBCTradingPeriodType)

    func didUpdatePriceSection(price: Decimal, currency: WXCurrencyCode)
    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction)
}
