/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class QuoteLimitBannerView: WXBannerInfo, WXTrackable {
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    public let _quoteDetail: BehaviorRelay<(Int?, WXMarket?, Bool?, Bool?, Decimal?, Decimal?)?> = BehaviorRelay(value: nil)
    public let _requestType: BehaviorRelay<PresenterRequestType?> = BehaviorRelay(value: nil)
    public let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    public let _leftButtonDidClick: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    public enum QuoteBannerCount {
        case ZERO_COUNT
        case NEGATIVE_COUNT
        case LESS_THAN_THRESHOLD
    }

    open var QUOTE_COUNT_THRESHOLD: Int { 200 }

    public struct QuoteLimitBannerViewData: Equatable {
        public var quoteBannerCount: QuoteBannerCount?
        public var message: String!
        public var count: Int?
        public init(
            _ quoteBannerCount: QuoteBannerCount?,
            _ message: String,
            _ count: Int?
        ) {
            self.quoteBannerCount = quoteBannerCount
            self.message = message
            self.count = count
        }

        public static func == (lhs: QuoteLimitBannerViewData, rhs: QuoteLimitBannerViewData) -> Bool {
            lhs.count == rhs.count && lhs.message == rhs.message && lhs.quoteBannerCount == rhs.quoteBannerCount
        }
    }

    open var viewData: Driver<QuoteLimitBannerViewData?> {
        _quoteDetail
            .flatMap(ignoreNil)
            .filter {
                [weak self] arg -> Bool in
                guard let self = self,
                    let count = arg.0,
                    let isDelayQuote = arg.2,
                    let isShowQuoteLimitBanner = arg.3 else { return false }
                return (count <= self.QUOTE_COUNT_THRESHOLD && isDelayQuote == false && isShowQuoteLimitBanner == true)
            }
            .asObservable()
            .map { remainingQuoteCount, market, isDelayQuote, isShowQuoteLimitBanner, bidPrice, askPrice -> QuoteLimitBannerViewData? in
                guard let market = market else { return nil }
                switch (remainingQuoteCount, market) {
                // Only need to add handling for `.hongKong` and `.america`, `.china` market has unlimited quote count
                case (let count, .hongKong), (let count, .america):
                    if count == nil || count == 0, bidPrice == nil, askPrice == nil {
                        // BMP quote
                        return QuoteLimitBannerViewData(.ZERO_COUNT, localizedString("wx_quote_count_with_bmp"), count)
                    } else if let count = count, count < 0 {
                        return QuoteLimitBannerViewData(.NEGATIVE_COUNT, localizedString("quote_count_with_charge"), count)
                    } else if let count = count {
                        return QuoteLimitBannerViewData(.LESS_THAN_THRESHOLD, String(format: localizedString("quote_count_remaining"), "\(count)"), count)
                    }
                    return nil
                default:
                    return nil
                }
            }
            .asDriver(onErrorJustReturn: nil)
            .flatMap(ignoreNil)
    }

    override open func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()
        bindViewData()
    }

    open func bindViewData() {
        // Show quote limit banner
        _leftButtonDidClick
            .subscribe(onNext: { [weak self] _ in
                guard let self = self else { return }
                self._quoteDetail.accept(nil)
            }).disposed(by: bag)

        viewData
            .asObservable()
            .flatMap(ignoreNil)
            .subscribe(onNext: { [weak self] quoteLimitBannerViewData in
                guard let self = self,
                    let message = quoteLimitBannerViewData.message else { return }

                self.viewModel.updateViewData(
                    BannerView.ViewModel(
                        message: message,
                        leftButtonAccessibleValue: AccessibleValue<String>(localizedString("banner_button_dismiss"), accessibilityText: "dismiss-quote-banner"),
                        leftButtonAction: { [weak self] in
                            guard let self = self else { return }
                            self.leftButtonDidClick()
                        }
                    )
                )
                self.viewModel?.inputs.updateModuleVisibility(visible: true)

            }).disposed(by: bag)
    }

    override open func setupTealiumTaggings() {
        super.setupTealiumTaggings()

        viewData
            .asObservable()
            .flatMap(ignoreNil)
            .subscribe(onNext: { [weak self] quoteLimitBannerViewData in
                guard let self = self,
                    let count = quoteLimitBannerViewData.count else { return }
                var eventContent = ""
                if count < 0 {
                    eventContent = "you will be charge for negative quotes"
                } else if count == 0 {
                    eventContent = "no live quote"
                } else if count <= self.QUOTE_COUNT_THRESHOLD {
                    eventContent = "remaining quote: \(count)"
                }
                if !eventContent.isEmpty {
                    self.tracker.trackEvent(EventInfo.buyPageQuoteMessageBanner(eventContent, defaultParameters: self.tracker.defaultParameters))
                }
            }).disposed(by: bag)
    }

    open func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        _quoteDetail.accept((quote.quoteRemaining, quote.stockMarket, quote.isDelay, quote.isShowQuoteLimitBanner, quote.bidPrice, quote.askPrice))
    }

    open func didUpdateInstruction(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }

    open func didUpdateRequestType(_ type: PresenterRequestType) {
        _requestType.accept(type)
    }

    open func leftButtonDidClick() {
        viewModel?.inputs.updateModuleVisibility(visible: false)
        _leftButtonDidClick.accept(())
    }
}

extension QuoteLimitBannerView: StockQuoteView {
    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateStockQuoteSection(quote: quote)
    }

    public func showLoadingIndicator(_ type: PresenterRequestType) {
        didUpdateRequestType(type)
    }

    public func hideLoadingIndicator(_ type: PresenterRequestType) {
        didUpdateRequestType(type)
    }
}

extension QuoteLimitBannerView: StockTradeView {
    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstruction(instruction: instruction)
    }
}
