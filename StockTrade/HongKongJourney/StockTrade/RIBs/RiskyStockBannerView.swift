/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class RiskyStockBannerView: WXBannerInfo, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    public let _isRiskyStock: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public let _rightButtonDidClick: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)

    override open func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        bannerView?.accessibilityIdentifier = "banner_risky_stock"
    }

    override open func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()
        _isRiskyStock.distinctUntilChanged().asObservable().subscribe(onNext: { [weak self] isRiskyStock in
            guard let self = self else { return }
            if let isRiskyStock = isRiskyStock, isRiskyStock {
                self.viewModel?.inputs.updateViewData(
                    BannerView.ViewModel(
                        message: localizedString("banner_risky_stock_warning"),
                        leftButtonAccessibleValue: AccessibleValue(localizedString("banner_button_learn_more"), accessibilityText: "banner_learnmore_button"),
                        leftButtonAction: { [weak self] in
                            guard let self = self else { return }
                            let helpViewController = StockTradeJourneyFactory.getObjectFromSuffix(RiskyStockBannerLearnMoreScreen.self)
                            helpViewController.setListener(self)
                            self.viewControllable?.present(helpViewController.embedInNavigationController(), animated: true, completion: nil)
                        },
                        rightButtonAccessibleValue: AccessibleValue(localizedString("banner_button_dismiss"), accessibilityText: "banner_dismiss_button"),
                        rightButtonAction: { [weak self] in
                            guard let self = self else { return }
                            self.viewModel?.inputs.updateModuleVisibility(visible: false)
                            self.rightButtonDidClick()
                        }
                    ))
                self.viewModel?.inputs.updateModuleVisibility(visible: true)
            } else {
                self.viewModel?.inputs.updateModuleVisibility(visible: false)
            }
        }).disposed(by: bag)
    }

    public func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        _isRiskyStock.accept(quote.riskStock)
    }

    public func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }

    public func rightButtonDidClick() {
        _rightButtonDidClick.accept(())
    }
}

extension RiskyStockBannerView: StockQuoteView {
    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateStockQuoteSection(quote: quote)
    }
}

extension RiskyStockBannerView: WXInfoScreenViewControllerListener {
    public func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    public func showModule(_ module: WXInfoScreenViewController) {
        /* empty */
    }
}

extension RiskyStockBannerView: StockTradeView {
    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }
}
