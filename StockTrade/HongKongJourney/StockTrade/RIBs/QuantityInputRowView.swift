//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class QuantityInputRowView: WXRowItemWithTitleSubTitleAndAddMinusButtons, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    var inlineMessageView: QuantityInputInlineMessageView!
    var views: [WXBaseView] = []

    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    private let _stockQuantityErrorCode: BehaviorRelay<HSBCErrorCode> = BehaviorRelay(value: .noError)
    private let _stockQuantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    private let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    private let _executedQuantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    private let _outstandingQuantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)

    // For accessibility only
    private weak var selectedAccessibilityView: UIView?

    override open func setupViews() {
        super.setupViews()

        inlineMessageView = QuantityInputInlineMessageView()
        inlineMessageView.setListener(self)
        stackView.addArrangedSubview(inlineMessageView)

        views = [inlineMessageView]
    }

    override open func initializeVariables() {
        super.initializeVariables()
        title = localizedString("quantity")

        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
        views.forEach { $0.viewDidLoad() }
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
        views.forEach { $0.viewDeinit() }
    }

    override open func viewDidAppear() {
        // For accessibility only
        if let selectedAccessibilityView = self.selectedAccessibilityView {
            self.selectedAccessibilityView = nil
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                UIAccessibility.post(
                    notification: .layoutChanged,
                    argument: selectedAccessibilityView
                )
            }
        }
    }

    override open func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityElements = [listItem, minusButton, addButton, executedQuantityLabel, outstandingQuantityLabel]
        isAccessibilityElement = false
        listItem.isAccessibilityElement = true
        listItem.accessibilityTraits = .button
        viewModel.outputs.value
            .asObservable()
            .subscribe(onNext: { [weak self] value in
                guard let self = self else { return }
                self.listItem.accessibilityLabel = String(format: "%@ %@", self.title, value)
            }).disposed(by: bag)
    }

    override open func bindViewModel() {
        super.bindViewModel()

        viewModel?.outputs.didClickView.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self, let controler = self.viewControllable else { return }
            let quantityInputView = _QuantityInputViewController()
            quantityInputView.modalPresentationStyle = .overCurrentContext
            controler.present(quantityInputView, animated: true, completion: nil)
            quantityInputView.accessibilityViewIsModal = true
            // change to setAccessibilityListener
            self.selectedAccessibilityView = self
            quantityInputView.accessibilityListener = self
            quantityInputView.accessibilityViewIsModal = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                UIAccessibility.post(
                    notification: .screenChanged,
                    argument: quantityInputView.closeButton
                )
            }

        }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _stockQuantity.distinctUntilChanged(),
            _stockQuantityErrorCode.distinctUntilChanged()
        ).asObservable().subscribe(onNext: { [weak self] quantity, error in
            guard let self = self else { return }

            switch error {
            case .almostReachUpperLimit:
                self.viewModel.inputs.updateAddInteractivity(interactabe: false)
            default:
                self.viewModel.inputs.updateAddInteractivity(interactabe: true)
            }

            // Disable quantity descrease button if the quantity is 0
            if let quantity = quantity, quantity > 0 {
                self.viewModel.inputs.updateMinusInteractivity(interactabe: true)
                self.viewModel.updateValue(value: self.getQuantitySetting(quantity: quantity))
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                    UIAccessibility.post(notification: UIAccessibility.Notification.announcement, argument: String(format: localizedString("alt_updated_to"), "\(self.getQuantitySetting(quantity: quantity))"))
                }
            } else {
                self.viewModel.inputs.updateMinusInteractivity(interactabe: false)
                self.viewModel.updateValue(value: localizedString("enter_quantity"))
            }
        }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _executedQuantity.flatMap(ignoreNil),
            _outstandingQuantity.flatMap(ignoreNil)
        ).asObservable().subscribe(onNext: { [weak self] executed, outstanding in
            guard let self = self else { return }
            self.viewModel.updateExecutedAndOutstandingQuantity(executed: executed, outstanding: outstanding)
        }).disposed(by: bag)

        viewModel?.outputs.didClickAdd.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.stockTradePresenter?.attemptIncreaseQuantity()

        }).disposed(by: bag)

        viewModel?.outputs.didClickMinus.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.stockTradePresenter?.attemptDecreaseQuantity()
        }).disposed(by: bag)
    }

    private func getQuantitySetting(quantity: Int64) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        // formatter.minimumIntegerDigits = 1
        formatter.usesGroupingSeparator = true
        formatter.groupingSeparator = ","
        formatter.decimalSeparator = "."
        let string = String(format: localizedString("number_of_shares"), formatter.string(from: NSNumber(value: quantity)) ?? "\(0)")
        return string
    }

    func didUpdateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        _stockQuantityErrorCode.accept(error)
        _stockQuantity.accept(quantity)
    }

    func didUpdateExecutedQuantitySection(executedQuantity: Int64, error: HSBCErrorCode) {
        _executedQuantity.accept(executedQuantity)
    }

    func didUpdateOutstandingQuantitySection(outstandingQuantity: Int64, error: HSBCErrorCode) {
        _outstandingQuantity.accept(outstandingQuantity)
    }

    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }

    func didUpdateBuyOddLotPrinciplesSection(allowOddLot: [HSBCStockTradeOrderType]?, canMoreThanOneLot: [HSBCStockTradeOrderType]?) {
        if _instruction.value == .buy || _instruction.value == .modifyBuy {
            if let allowOddLot = allowOddLot {
                stockTradePresenter?.didEditSection(section: .allowOddLot(allowOddLot: allowOddLot))
            }

            if let canMoreThanOneLot = canMoreThanOneLot {
                stockTradePresenter?.didEditSection(section: .canMoreThanOneLot(canMoreThanOneLot: canMoreThanOneLot))
            }
        }
    }

    func didUpdateSellOddLotPrinciplesSection(allowOddLot: [HSBCStockTradeOrderType]?, canMoreThanOneLot: [HSBCStockTradeOrderType]?) {
        if _instruction.value == .sell || _instruction.value == .modifySell {
            if let allowOddLot = allowOddLot {
                stockTradePresenter?.didEditSection(section: .allowOddLot(allowOddLot: allowOddLot))
            }

            if let canMoreThanOneLot = canMoreThanOneLot {
                stockTradePresenter?.didEditSection(section: .canMoreThanOneLot(canMoreThanOneLot: canMoreThanOneLot))
            }
        }
    }
}

extension QuantityInputRowView: StockTradeView {
    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }

    public func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        didUpdateQuantitySection(quantity: quantity, error: error)
    }

    public func updateExecutedQuantitySection(executedQuantity: Int64, error: HSBCErrorCode) {
        didUpdateExecutedQuantitySection(executedQuantity: executedQuantity, error: error)
    }

    public func updateOutstandingQuantitySection(outstandingQuantity: Int64, error: HSBCErrorCode) {
        didUpdateOutstandingQuantitySection(outstandingQuantity: outstandingQuantity, error: error)
    }
}

extension QuantityInputRowView: StockQuoteView {
    public func updateBuyOddLotPrinciplesSection(allowOddLot: [HSBCStockTradeOrderType]?, canMoreThanOneLot: [HSBCStockTradeOrderType]?, error: HSBCErrorCode) {
        didUpdateBuyOddLotPrinciplesSection(allowOddLot: allowOddLot, canMoreThanOneLot: canMoreThanOneLot)
    }

    public func updateSellOddLotPrinciplesSection(allowOddLot: [HSBCStockTradeOrderType]?, canMoreThanOneLot: [HSBCStockTradeOrderType]?, error: HSBCErrorCode) {
        didUpdateSellOddLotPrinciplesSection(allowOddLot: allowOddLot, canMoreThanOneLot: canMoreThanOneLot)
    }
}

extension QuantityInputRowView: WXInlinedWarningMessageViewDelegate {
    public func showInlinedWarningMessageViewModule(_ module: WXInlinedWarningMessageView) {
        module.isHidden = false
    }

    public func hideInlinedWarningMessageViewModule(_ module: WXInlinedWarningMessageView) {
        module.isHidden = true
    }
}

extension QuantityInputRowView: AccessibilityDelegate {
    public func didSelectAccessibilityView(_ module: UIView) {
        selectedAccessibilityView = module
    }

    public func shouldSelectAccessibilityView() {
        if let selectedAccessibilityView = self.selectedAccessibilityView {
            self.selectedAccessibilityView = nil
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                UIAccessibility.post(
                    notification: .screenChanged,
                    argument: selectedAccessibilityView
                )
            }
        }
    }
}
