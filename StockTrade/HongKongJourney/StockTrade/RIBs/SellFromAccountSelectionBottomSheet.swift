/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift

class SellFromAccountSelectionBottomSheet: WXListItemTableViewCellSingleSelectionBottomSheet {
    weak var stockTradePresenter: StockTradePresenterImplementation!
    weak var accountSettlementPresenter: AccountSettlementPresenterImplementation!

    let _selectedAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)
    let _investmentAccounts: BehaviorRelay<[[WXInvestmentAccount: Int64]]?> = BehaviorRelay(value: nil)

    // For accessibility only
    weak var accessibilityListener: AccessibilityDelegate?

    override func initializeVariables() {
        super.initializeVariables()
        headerTitle = localizedString("select_security_account")
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
        accountSettlementPresenter = AccountSettlementPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
        accountSettlementPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
        accountSettlementPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        closeButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            // For accessibility only
            self.accessibilityListener?.shouldSelectAccessibilityView()
        }.disposed(by: bag)

        BehaviorRelay.combineLatest(
            _investmentAccounts.flatMap(ignoreNil).distinctUntilChanged(),
            _selectedAccount.distinctUntilChanged()
        ).subscribe(onNext: { [weak self] investmentAccounts, selectedAccount in
            guard let self = self else { return }
            self.viewModel?.inputs.updateDatasource(
                data: investmentAccounts.compactMap { self.createInvestmentAccountData($0, selectedAccount: selectedAccount) }
            )
        }).disposed(by: bag)
        if let viewModel = self.viewModel {
            Driver.combineLatest(
                viewModel.outputs.row,
                _investmentAccounts.asDriver().flatMap(ignoreNil).distinctUntilChanged()
            ).asObservable().subscribe(onNext: { [weak self] selectedRow, investmentAccounts in
                guard let self = self, let selectedAccount = investmentAccounts[selectedRow].keys.first else { return }
                self.didSelectAccount(account: selectedAccount)
                self.stockTradePresenter.didEditSection(section: .investmentAccount(account: selectedAccount))
                self.viewModel?.didClickCloseButton()
                #warning("FBE 1.0")
                self.accountSettlementPresenter?.didEditSection(section: .selectedInvestmentAccount(account: selectedAccount))
                self.viewModel?.didClickCloseButton()
            }).disposed(by: bag)
        }
    }

    private func createInvestmentAccountData(_ hash: [WXInvestmentAccount: Int64], selectedAccount: WXInvestmentAccount?) -> SingleSelectionData? {
        if let key = hash.keys.first,
            let tradableQuantity = hash[key],
            let decimalQuantity = Decimal(string: "\(tradableQuantity)"),
            let formattedQuantity = PriceStringFormattingUtil.formattedPriceString(price: decimalQuantity, shouldDiplayCurrencyCode: false, shouldDisplayTrailingZero: false, shouldGroupBySeparator: true) {
            let title = LocalizationUtil.localizedAccountTypeName(type: key.type, currency: key.currency)
            let additionalDetails = String(format: localizedString("number_of_shares"), "\(formattedQuantity)")
            let detail = key.number
            var isSelected = false
            if let selected = selectedAccount {
                isSelected = selected == key
            }
            return SingleSelectionData(selected: isSelected, title: title, detail: detail, additionalDetails: additionalDetails)
        }
        return nil
    }

    // User interaction
    func didSelectAccount(account: WXInvestmentAccount) {
        stockTradePresenter?.didEditSection(section: .investmentAccount(account: account))
    }

    func didUpdateSelectedAccountSection(account: WXInvestmentAccount) {
        _selectedAccount.accept(account)
    }

    func didUpdateInvestmentAccountsSection(accounts: [[WXInvestmentAccount: Int64]]) {
        _investmentAccounts.accept(accounts)
    }

    func didUpdateErrorCodeWithWarnings(errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {}
}

extension SellFromAccountSelectionBottomSheet: StockTradeView {
    func updateAvailableInvestmentAccountsSection(accounts: [[WXInvestmentAccount: Int64]], error: HSBCErrorCode) {
        didUpdateInvestmentAccountsSection(accounts: accounts)
    }

    func updateInvestmentAccountSection(account: WXInvestmentAccount, tradableQuantity: Int64, error: HSBCErrorCode) {
        didUpdateSelectedAccountSection(account: account)
    }

    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        didUpdateErrorCodeWithWarnings(errorCode: errorCode, warnings: warnings)
    }
}

extension SellFromAccountSelectionBottomSheet: AccountSettlementView {
    /** empty implementation */
}
