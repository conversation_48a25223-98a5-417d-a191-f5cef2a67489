/** @RIB */
//
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class QuoteInfoHeaderView: WXStockQuoteHeaderView, WXTrackable {
    // MARK: -  quote header view

    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    public weak var accountSettlementPresenter: AccountSettlementPresenterImplementation!
    public var _quote: BehaviorRelay<StockQuoteDetail?> = BehaviorRelay(value: nil)
    public var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    public var _stockMarket: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    public var _stockCode: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    public var _stockName: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    public var _currency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)
    public var _stockPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _changeAmount: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _changePercent: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _exchangeUpdatedTime: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public var _isDelay: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public var _lotSize: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    public var _isAuctionPeriod: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public var _casUpperLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _casLowerLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _referencePrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _buyLimitRange: BehaviorRelay<ClosedRange<Decimal>?> = BehaviorRelay(value: nil)
    public var _sellLimitRange: BehaviorRelay<ClosedRange<Decimal>?> = BehaviorRelay(value: nil)
    public var _tradingPeriodType: BehaviorRelay<HSBCTradingPeriodType?> = BehaviorRelay(value: nil)
    public var _isInVCMPeriod: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public var _vcmPriceUpperLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _vcmPriceLowerLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _spreadCode: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    public var _askPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _bidPrice: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public var _isMDSDown: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public var _exchangeCode: BehaviorRelay<HSBCStockExchange?> = BehaviorRelay(value: nil)

    override open func initializeVariables() {
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
        accountSettlementPresenter = AccountSettlementPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
        accountSettlementPresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
        accountSettlementPresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateName(localizedString("default_item_value"))
        viewModel?.inputs.updateCurrency(localizedString("default_item_value"))
        viewModel?.inputs.updateCode(localizedString("default_item_value"))
        viewModel?.inputs.updateQuoteTime(NSAttributedString(string: localizedString("default_item_value")))
        viewModel?.inputs.updateCurrentPrice(localizedString("default_item_value"))
        viewModel?.inputs.updatePriceDiff(localizedString("default_item_value"))
        viewModel?.inputs.updateTrend(.noChange)
        viewModel?.outputs.didClickRefresh.asObservable().debounce(.milliseconds(800), scheduler: MainScheduler.instance).subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.stockQuotePresenter?.attemptGetStockQuote(view: self)

        }).disposed(by: bag)

        /** FBE 1.0 logic reomve from FBE 2.0 */
        _lotSize.flatMap(ignoreNil)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] lotSize in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter.didEditSection(section: .lotSize(size: lotSize))
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _stockMarket.flatMap(ignoreNil).distinctUntilChanged(),
                _stockCode.flatMap(ignoreNil).distinctUntilChanged()
            ).subscribe(onNext: { [weak self] market, code in
                guard let self = self else { return }
                self.viewModel?.inputs.updateCode(self.stockCode(market, code))
                #warning("FBE 1.0")
                self.stockTradePresenter.didEditSection(section: .stockCode(string: code))
            }).disposed(by: bag)

        _stockName
            .flatMap(ignoreNil)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] stockName in
                guard let self = self else { return }
                self.viewModel?.inputs.updateName(stockName)
                #warning("FBE 1.0")
                self.stockTradePresenter.didEditSection(section: .stockName(string: stockName))
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _isAuctionPeriod.flatMap(ignoreNil).distinctUntilChanged(),
                _casUpperLimit.distinctUntilChanged(),
                _casLowerLimit.distinctUntilChanged()
            ).subscribe(onNext: { [weak self] isAuctionPeriod, casUpperLimit, casLowerLimit in
                guard let self = self else { return }
                #warning("FBE 1.0")
                if let upper = casUpperLimit {
                    self.stockTradePresenter?.didEditSection(section: .casUpperLimit(casUpperLimit: upper))
                }

                if let lower = casLowerLimit {
                    self.stockTradePresenter?.didEditSection(section: .casLowerLimit(casLowerLimit: lower))
                }
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _isInVCMPeriod.flatMap(ignoreNil).distinctUntilChanged(),
                _vcmPriceUpperLimit.distinctUntilChanged(),
                _vcmPriceLowerLimit.distinctUntilChanged()
            ).subscribe(onNext: { [weak self] isInVCMPeriod, vcmPriceUpperLimit, vcmPriceLowerLimit in
                guard let self = self else { return }
                #warning("FBE 1.0")
                if isInVCMPeriod, let upper = vcmPriceUpperLimit, let lower = vcmPriceLowerLimit {
                    self.stockTradePresenter?.didEditSection(section: .vcmUpperLimit(price: upper))
                    self.stockTradePresenter?.didEditSection(section: .vcmLowerLimit(price: lower))
                }
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _stockMarket.flatMap(ignoreNil).distinctUntilChanged(),
                _currency.flatMap(ignoreNil).distinctUntilChanged()
            ).take(1)
            .subscribe(onNext: { [weak self] stockMarket, currency in
                guard let self = self else { return }
                self.viewModel?.inputs.updateCurrency(LocalizationUtil.localizedCurrencyName(code: currency))
                #warning("FBE 1.0")
                self.setAccountCurrency(stockMarket: stockMarket, currency: currency)
            }).disposed(by: bag)

        _spreadCode
            .flatMap(ignoreNil)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] spreadCode in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .spreadCode(code: spreadCode))
            }).disposed(by: bag)

        _askPrice
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] askPrice in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .currentAskPrice(price: askPrice))
            }).disposed(by: bag)

        _bidPrice
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] bidPrice in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .currentBidPrice(price: bidPrice))
            }).disposed(by: bag)

        _stockPrice
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] stockPrice in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .currentPrice(price: stockPrice))
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _stockMarket.flatMap(ignoreNil).distinctUntilChanged(),
                _currency.flatMap(ignoreNil).distinctUntilChanged(),
                _stockPrice.flatMap(ignoreNil).distinctUntilChanged()
            ).subscribe(onNext: { [weak self] market, currency, price in
                guard let self = self else { return }
                let decimalPlaces = self.getDecimalPlaces(market)
                if let formattedPrice = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency, shouldDiplayCurrencyCode: false) {
                    self.viewModel?.inputs.updateCurrentPrice(formattedPrice)
                }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .lastTradedPrice(price: price))
            }).disposed(by: bag)

        _isMDSDown
            .flatMap(ignoreNil)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] _isMDSDown in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .isMDSDown(isMDSDown: _isMDSDown))
            }).disposed(by: bag)

        _exchangeCode
            .flatMap(ignoreNil)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] exchangeCode in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .exchangeCode(exchangeCode: exchangeCode))
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _stockMarket.flatMap(ignoreNil).distinctUntilChanged(),
                _changeAmount.flatMap(ignoreNil).distinctUntilChanged(),
                _changePercent.flatMap(ignoreNil).distinctUntilChanged()
            ).subscribe(onNext: { [weak self] market, nominal, percentage in
                guard let self = self else { return }
                self.viewModel?.inputs.updatePriceDiff(self.percentiage(market: market, nominal: nominal, percentage: percentage))
                self.viewModel?.inputs.updateTrend(self.trend(nominal))
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _instruction.flatMap(ignoreNil).distinctUntilChanged(),
                _referencePrice.flatMap(ignoreNil).distinctUntilChanged(),
                _buyLimitRange.flatMap(ignoreNil).distinctUntilChanged(),
                _sellLimitRange.flatMap(ignoreNil).distinctUntilChanged()
            ).subscribe(onNext: { [weak self] instruction, referencePrice, buyLimitRange, sellLimitRange in
                guard let self = self else { return }
                #warning("FBE 1.0")
                switch instruction {
                case .buy, .modifyBuy:
                    self.stockTradePresenter?.didEditSection(section: .preOpeningSession(referencePrice: referencePrice, limitRange: buyLimitRange))
                case .sell, .modifySell:
                    self.stockTradePresenter?.didEditSection(section: .preOpeningSession(referencePrice: referencePrice, limitRange: sellLimitRange))
                default:
                    break
                }
            }).disposed(by: bag)

        _tradingPeriodType
            .flatMap(ignoreNil)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] type in
                guard let self = self else { return }
                #warning("FBE 1.0")
                self.stockTradePresenter?.didEditSection(section: .tradingPeriodType(type: type))
            }).disposed(by: bag)

        BehaviorRelay
            .combineLatest(
                _stockMarket.flatMap(ignoreNil).distinctUntilChanged(),
                _exchangeUpdatedTime.flatMap(ignoreNil).distinctUntilChanged(),
                _isDelay.flatMap(ignoreNil).distinctUntilChanged()
            ).subscribe(onNext: { [weak self] market, date, isDelayed in
                guard let self = self else { return }
                let dateString = date.stringWithDateFormat(localizedString("date_format_quote"), timeZone: market.timeZone)

                let delayedString = String(format: localizedString("delayed_quote_at"), dateString, market.timeZoneAbbreviation)
                let realtimeString = String(format: localizedString("real_time_quote_at"), dateString, market.timeZoneAbbreviation)

                let attributedString = NSMutableAttributedString(string: "\(!isDelayed ? realtimeString : delayedString)")

                let paragraphStyle = NSMutableParagraphStyle()
                paragraphStyle.lineSpacing = 5
                attributedString.addAttribute(NSAttributedString.Key.paragraphStyle, value: paragraphStyle, range: NSMakeRange(0, attributedString.length))
                self.viewModel?.inputs.updateQuoteTime(attributedString)
            }).disposed(by: bag)
    }

    override open func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        Driver.combineLatest(
            viewModel.outputs.trend,
            viewModel.outputs.currentPrice
        ).asObservable().subscribe(onNext: { [weak self] trend, price in
            guard let self = self else { return }
            switch trend {
            case .noChange:
                break
            case .upTrend:
                self.stockTrendIndicatorImageView.accessibilityLabel = String(format: localizedString("alt_increased_price"), "")
                self.stockCurrentPriceLabel.accessibilityLabel = String(format: localizedString("alt_increased_price"), "\(price)")
            case .downTrend:
                self.stockTrendIndicatorImageView.accessibilityLabel = String(format: localizedString("alt_decreased_price"), "")
                self.stockCurrentPriceLabel.accessibilityLabel = String(format: localizedString("alt_decreased_price"), "\(price)")
            }
        }).disposed(by: bag)
        stockTrendIndicatorImageView.accessibilityIdentifier = "image_trend_indicator"
    }

    open func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        _stockMarket.accept(quote.stockMarket)
        _stockCode.accept(quote.stockCode)
        _stockName.accept(quote.stockName)
        _currency.accept(quote.currency)
        _stockPrice.accept(quote.stockPrice)
        _changeAmount.accept(quote.changeAmount)
        _changePercent.accept(quote.changePercent)
        _exchangeUpdatedTime.accept(quote.exchangeUpdatedTime)
        _isDelay.accept(quote.isDelay)
        _lotSize.accept(quote.lotSize)
        _isAuctionPeriod.accept(quote.isAuctionPeriod)
        _casUpperLimit.accept(quote.casUpperLimit)
        _casLowerLimit.accept(quote.casLowerLimit)
        _isInVCMPeriod.accept(quote.isInVCMPeriod)
        _vcmPriceUpperLimit.accept(quote.vcmPriceUpperLimit)
        _vcmPriceLowerLimit.accept(quote.vcmPriceLowerLimit)
        _spreadCode.accept(quote.spreadCode)
        _askPrice.accept(quote.askPrice)
        _bidPrice.accept(quote.bidPrice)
        _exchangeCode.accept(quote.exchangeCode)
    }

    open func didUpdateIsMDSDown(isMDSDown: Bool) {
        _isMDSDown.accept(isMDSDown)
    }

    open func didUpdatePreOpeningSessionSection(referencePrice: Decimal?, buyLimitRange: ClosedRange<Decimal>?, sellLimitRange: ClosedRange<Decimal>?) {
        _referencePrice.accept(referencePrice)
        _buyLimitRange.accept(buyLimitRange)
        _sellLimitRange.accept(sellLimitRange)
    }

    open func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }

    private func percentiage(market: WXMarket, nominal: Decimal, percentage: Decimal) -> String {
        let n = Double(truncating: nominal as NSNumber)
        let p = Double(truncating: percentage as NSNumber)
        switch (market, trend(nominal)) {
        case (.china, .downTrend), (.shangHai, .downTrend), (.shenZhen, .downTrend), (.hongKong, .downTrend):
            return String(format: "%.3f (%.2f%%)", n, p)
        case (.america, .downTrend):
            return String(format: "%.2f (%.2f%%)", n, p)
        case (.hongKong, .upTrend), (.china, .upTrend), (.shangHai, .upTrend), (.shenZhen, .upTrend), (.america, .upTrend):
            return String(format: "+%.3f (+%.2f%%)", n, p)
        case (.hongKong, .noChange), (.china, .noChange), (.shangHai, .noChange), (.shenZhen, .noChange):
            return String(format: "0.000 (0.00%%)", n, p)
        case (.america, .noChange):
            return String(format: "0.00 (0.00%%)", n, p)
        default:
            return String(format: "%@ (%@)", localizedString("default_item_value"), localizedString("default_item_value"))
        }
    }

    private func getDecimalPlaces(_ market: WXMarket) -> Int {
        switch market {
        case .hongKong, .china, .shenZhen, .shangHai:
            return 3
        case .america:
            return 2
        default:
            return 3
        }
    }

    private func trend(_ nominal: Decimal) -> StockTrend {
        if nominal == 0.0 {
            return .noChange
        } else if Decimal(0).isLess(than: nominal) {
            return .upTrend
        } else {
            return .downTrend
        }
    }

    private func stockCode(_ market: WXMarket, _ code: String) -> String {
        "\(code) (\(LocalizationUtil.localizedMarketShortName(market: market)))"
    }

    open func didSetShowLoadingIndicator(_ type: PresenterRequestType) {
        if [.PRESENTER_REQUEST_GET_QUOTE_DETAILS, .PRESENTER_REQUEST_GET_QUOTE].contains(type) {
            viewModel?.inputs.updateLoadingIndicatorVisibility(true)
        }
    }

    open func didSetHideLoadingIndicator(_ type: PresenterRequestType) {
        if [.PRESENTER_REQUEST_GET_QUOTE_DETAILS, .PRESENTER_REQUEST_GET_QUOTE].contains(type) {
            viewModel?.inputs.updateLoadingIndicatorVisibility(false)
        }
    }

    open func setAccountCurrency(stockMarket: WXMarket?, currency: WXCurrencyCode) {
        stockTradePresenter?.didEditSection(section: .currency(currency: currency))

        var settlementCurrency: WXCurrencyCode = currency
        if stockMarket == .hongKong, currency == .unitedStatesDollar {
            settlementCurrency = .hongKongDollar
        }
        accountSettlementPresenter?.didEditSection(section: .currency(currency: settlementCurrency))
    }

    func didUpdateTradingPeriodType(type: HSBCTradingPeriodType) {
        _tradingPeriodType.accept(type)
    }
}

extension QuoteInfoHeaderView: StockTradeView {}

extension QuoteInfoHeaderView: StockQuoteView {
    open func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateStockQuoteSection(quote: quote)
    }

    open func updateIsMDSDownSection(isMDSDown: Bool, error: HSBCErrorCode) {
        didUpdateIsMDSDown(isMDSDown: isMDSDown)
    }

    open func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        didSetHideLoadingIndicator(type)
    }

    open func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }

    open func updatePreOpeningSessionSection(isEligible: Bool, referencePrice: Decimal?, buyLimitRange: ClosedRange<Decimal>?, sellLimitRange: ClosedRange<Decimal>?) {
        didUpdatePreOpeningSessionSection(referencePrice: referencePrice, buyLimitRange: buyLimitRange, sellLimitRange: sellLimitRange)
    }

    open func updateTradingPeriodType(type: HSBCTradingPeriodType) {
        didUpdateTradingPeriodType(type: type)
    }

    open func showLoadingIndicator(_ type: PresenterRequestType) {
        didSetShowLoadingIndicator(type)
    }

    open func hideLoadingIndicator(_ type: PresenterRequestType) {
        didSetHideLoadingIndicator(type)
    }
}

extension QuoteInfoHeaderView: AccountSettlementView {
    /** empty implementation */
}
