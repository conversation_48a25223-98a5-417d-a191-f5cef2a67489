/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit

open class SellFromAccountRowView: WXAccountInformationRowItem, WXTrackable, WXViewControllable {
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    public weak var accountSettlementPresenter: AccountSettlementPresenterImplementation!

    public weak var viewControllable: UIViewController?
    public var views: [WXBaseView] = []

    public var sellFromAccountLoadingIndicator: SellFromAccountLoadingIndicator!

    public let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    public let _investmentAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)
    public let _investmentAccountTradableQuantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    public let _investmentAccounts: BehaviorRelay<[WXInvestmentAccount]?> = BehaviorRelay(value: nil)

    // For accessibility only
    private weak var selectedAccessibilityView: UIView?

    override open func initializeVariables() {
        super.initializeVariables()
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
        accountSettlementPresenter = AccountSettlementPresenterImplementation.configure(view: self, routerImplementation: nil)
        sellFromAccountLoadingIndicator = StockTradeJourneyFactory.getObjectFromSuffix(SellFromAccountLoadingIndicator.self)
        sellFromAccountLoadingIndicator.setListener(self)
        views = [
            sellFromAccountLoadingIndicator
        ]
    }

    override open func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "sell_from_row"
        titleLabel.accessibilityIdentifier = "title"
        chevronImageView.accessibilityIdentifier = "chevronImage"
        isAccessibilityElement = true
        accessibilityTraits = .button
        Driver.combineLatest(
            viewModel.outputs.accountName,
            viewModel.outputs.accountNumber,
            viewModel.outputs.accountShares
        )
        .asObservable()
        .subscribe(onNext: { [weak self] accountName, accountNumber, accountShares in
            guard let self = self else { return }
            self.accessibilityValue = String(format: "%@ %@ %@", accountName, accountNumber ?? "", accountShares ?? "")
        }).disposed(by: bag)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockTradePresenter?.viewDidLoad(view: self)
        accountSettlementPresenter?.viewDidLoad(view: self)
        for view in views {
            view.viewDidLoad()
        }
    }

    override open func viewDidAppear() {
        for view in views {
            view.viewDidAppear()
        }
    }

    override open func viewWillDisappear() {
        for view in views {
            view.viewWillDisappear()
        }
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockTradePresenter?.viewDeinit(view: self)
        accountSettlementPresenter?.viewDeinit(view: self)
        for view in views {
            view.viewDeinit()
        }
    }

    override open func setupViews() {
        super.setupViews()
        addSubview(sellFromAccountLoadingIndicator)
        NSLayoutConstraint.activate([
            sellFromAccountLoadingIndicator.centerXAnchor.constraint(equalTo: centerXAnchor),
            sellFromAccountLoadingIndicator.centerYAnchor.constraint(equalTo: centerYAnchor)
        ])
    }

    override open func bindViewModel() {
        super.bindViewModel()
        viewModel.inputs.updateTitle(title: localizedString("sell_from"))
        viewModel.inputs.updateAccountName(accountName: localizedString("sell_from_account_placeholder"))
        viewModel.inputs.updateChevronVisibility(chevronVisible: false)
        viewModel.inputs.updateModuleVisibility(visible: false)

        _instruction
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] instruction in
                guard let self = self, [.modifySell, .sell].contains(instruction) else { return }
                self.viewModel.inputs.updateModuleVisibility(visible: true)
            }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _investmentAccount
                .flatMap(ignoreNil)
                .distinctUntilChanged(),
            _investmentAccountTradableQuantity
                .flatMap(ignoreNil)
                .distinctUntilChanged(),
            _instruction.distinctUntilChanged()
        )
        .subscribe(onNext: { [weak self] investmentAccount, tradableQuantity, instruction in
            guard let self = self, [.modifySell, .sell].contains(instruction) else { return }
            if let decimalQuantity = Decimal(string: "\(tradableQuantity)"),
                let formattedQuantity = PriceStringFormattingUtil.formattedPriceString(price: decimalQuantity, shouldDiplayCurrencyCode: false, shouldDisplayTrailingZero: false, shouldGroupBySeparator: true) {
                self.viewModel.inputs.updateAccountName(accountName: LocalizationUtil.localizedAccountTypeName(type: investmentAccount.type, currency: investmentAccount.currency))
                self.viewModel.inputs.updateAccountNumber(accountNumber: investmentAccount.number)
                self.viewModel.inputs.updateAccountShares(accountShares: String(format: localizedString("number_of_shares"), "\(formattedQuantity)"))
            }

        }).disposed(by: bag)

        viewModel.outputs.didClick.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self, let viewControllable = self.viewControllable else { return }
            let sellFromBottomSheet = StockTradeJourneyFactory.getObjectFromSuffix(SellFromAccountSelectionBottomSheet.self)
            viewControllable.presentPanModal(sellFromBottomSheet)
            // change to setAccessibilityListener
            self.selectedAccessibilityView = self
            sellFromBottomSheet.accessibilityListener = self
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                UIAccessibility.post(
                    notification: .screenChanged,
                    argument: sellFromBottomSheet.closeButton
                )
            }
        }).disposed(by: bag)

        _instruction.flatMap(ignoreNil).distinctUntilChanged().take(1)
            .asObservable().subscribe(onNext: { [weak self] instruction in
                guard let self = self else { return }
                if [.modifySell, .sell].contains(instruction) {
                    self.viewModel.inputs.updateModuleVisibility(visible: true)
                } else {
                    self.viewModel.inputs.updateModuleVisibility(visible: false)
                }
            }).disposed(by: bag)

        bindOverridableData()
    }

    open func bindOverridableData() {
        BehaviorRelay.combineLatest(
            _investmentAccounts
                .flatMap(ignoreNil)
                .distinctUntilChanged(),
            _instruction
                .distinctUntilChanged(),
            _investmentAccount
                .distinctUntilChanged()
        )
        .subscribe(onNext: { [weak self] accounts, instruction, investmentAccount in
            guard let self = self, [.modifySell, .sell].contains(instruction) else { return }
            let count = accounts.count
            switch count {
            case 1:
                let account = accounts[0]
                self.didEditSection(account)
                // STM-4092 AC3
                self.viewModel.inputs.updateModuleInteractivity(interactabe: false)
                self.viewModel.inputs.updateChevronVisibility(chevronVisible: false)
            default:
                let isEditable = instruction != .modifySell
                if isEditable, investmentAccount == nil {
                    self.viewModel.inputs.updateAccountName(accountName: localizedString("sell_from_account_placeholder"))
                }
                self.viewModel.inputs.updateChevronVisibility(chevronVisible: isEditable)
                self.viewModel.inputs.updateModuleInteractivity(interactabe: isEditable)
            }
        }).disposed(by: bag)
    }

    public func didEditSection(_ account: WXInvestmentAccount) {
        accountSettlementPresenter?.didEditSection(section: .selectedInvestmentAccount(account: account))
        stockTradePresenter?.didEditSection(section: .investmentAccount(account: account))
    }

    open func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }

    open func didUpdateInvestmentAccount(account: WXInvestmentAccount, tradableQuantity: Int64) {
        _investmentAccount.accept(account)
        _investmentAccountTradableQuantity.accept(tradableQuantity)
    }

    open func didUpdateAvailableInvestmentAccountSection(accounts: [WXInvestmentAccount]) {
        _investmentAccounts.accept(accounts)
    }
}

extension SellFromAccountRowView: StockTradeView {
    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }

    public func updateInvestmentAccountSection(account: WXInvestmentAccount, tradableQuantity: Int64, error: HSBCErrorCode) {
        didUpdateInvestmentAccount(account: account, tradableQuantity: tradableQuantity)
    }

    public func updateAvailableInvestmentAccountsSection(accounts: [[WXInvestmentAccount: Int64]], error: HSBCErrorCode) {
        didUpdateAvailableInvestmentAccountSection(accounts: accounts.compactMap { $0.keys.first })
    }
}

extension SellFromAccountRowView: WXViewWithLoadingIndicatorListener {
    public func showModule(_ module: WXViewWithLoadingIndicator) {
        module.isHidden = false
    }

    public func hideModule(_ module: WXViewWithLoadingIndicator) {
        module.isHidden = true
    }

    public func childViewsInteractivity(_ bool: Bool) {
        isUserInteractionEnabled = bool
    }
}

extension SellFromAccountRowView: AccountSettlementView {
    /** empty */
}

extension SellFromAccountRowView: AccessibilityDelegate {
    public func didSelectAccessibilityView(_ module: UIView) {
        selectedAccessibilityView = module
    }

    public func shouldSelectAccessibilityView() {
        if let selectedAccessibilityView = self.selectedAccessibilityView {
            self.selectedAccessibilityView = nil
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                UIAccessibility.post(
                    notification: .screenChanged,
                    argument: selectedAccessibilityView
                )
            }
        }
    }
}
