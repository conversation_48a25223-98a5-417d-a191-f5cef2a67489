//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class QuantityWarningOddLotLongerTimeToProcessBannerView: WXBannerInfo, WXTrackable, WXViewControllable {
    weak var viewControllable: UIViewController?
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!

    public let _stockLotSize: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    public let _stockQuantityErrorCode: BehaviorRelay<HSBCErrorCode> = BehaviorRelay(value: .noError)
    public let _hasToShowQuantityWarning: BehaviorRelay<Bool> = BehaviorRelay(value: true)
    public let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)

    override open func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        bannerView?.accessibilityIdentifier = "banner_quantity_warning"
    }

    override open func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()
        // Show quote limit banner if error is oddLotLongerTimeToProcess
        BehaviorRelay.combineLatest(
            _stockLotSize.flatMap(ignoreNil).distinctUntilChanged(),
            _stockQuantityErrorCode,
            _hasToShowQuantityWarning.filter { $0 == true },
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).asObservable()
            .subscribe(onNext: { [weak self] lotSize, errorCode, _, market in
                guard let self = self else { return }

                // US stock does not have the odd lot concept
                guard market != .america else { return }

                var bannerMessage = ""
                switch errorCode {
                case .noError:
                    self.viewModel?.inputs.updateModuleVisibility(visible: false)
                case .oddLotNotAllow,
                     .oddLotSizeNotMatch,
                     .oddLotChangeToMarketOrder:
                    self.viewModel.updateModuleVisibility(visible: false)

                case .oddLotLongerTimeToProcess:
                    bannerMessage = String(format: localizedString("banner_quantity_info_odd_lot_market_order_type_chosen"), String(lotSize)) // "Your order might take longer to process as it's outside the normal lot size of \(lotSize)"
                    self.viewModel?.inputs.updateViewData(
                        BannerView.ViewModel(
                            message: bannerMessage,
                            leftButtonAccessibleValue: AccessibleValue(localizedString("banner_button_learn_more"), accessibilityText: "learn-more-quantity-banner"),
                            leftButtonAction: { [weak self] in
                                guard let self = self else { return }
                                let helpViewController = StockTradeJourneyFactory.getObjectFromSuffix(QuantityBannerLearnMoreScreen.self)
                                helpViewController.setListener(self)
                                self.viewControllable?.present(helpViewController.embedInNavigationController(), animated: true, completion: nil)
                            },
                            rightButtonAccessibleValue: AccessibleValue(localizedString("banner_button_dismiss"), accessibilityText: "dismiss-quantity-banner"),
                            rightButtonAction: { [weak self] in
                                guard let self = self else { return }
                                self.viewModel?.inputs.updateModuleVisibility(visible: false)
                            }
                        ))
                    self.viewModel.updateModuleVisibility(visible: true)
                default:
                    break
                }
            }).disposed(by: bag)
    }

    override open func setupTealiumTaggings() {
        BehaviorRelay.combineLatest(
            _stockLotSize.flatMap(ignoreNil).distinctUntilChanged(),
            _stockQuantityErrorCode.distinctUntilChanged(),
            _hasToShowQuantityWarning.filter { $0 == true },
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).asObservable()
            .debounce(.microseconds(500), scheduler: MainScheduler.instance)
            .subscribe(onNext: { [weak self] lotSize, errorCode, _, market in
                guard let self = self else { return }
                switch errorCode {
                case .oddLotLongerTimeToProcess:
                    self.tracker.trackEvent(EventInfo.buyQuantityError(defaultParameters: self.tracker.defaultParameters))
                default:
                    return
                }
            }).disposed(by: bag)

        viewModel.moduleVisibility.asObservable().asObservable().debounce(.microseconds(500), scheduler: MainScheduler.instance).subscribe(onNext: { [weak self] visibility in
            guard let self = self, visibility == false else { return }
            self.tracker.trackEvent(EventInfo.buyPageDismissBanner(defaultParameters: self.tracker.defaultParameters))
        }).disposed(by: bag)
    }

    func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        _stockLotSize.accept(quote.lotSize)
        _market.accept(quote.stockMarket)
    }

    func didUpdateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        _stockQuantityErrorCode.accept(error)
    }
}

extension QuantityWarningOddLotLongerTimeToProcessBannerView: StockQuoteView {
    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateStockQuoteSection(quote: quote)
    }
}

extension QuantityWarningOddLotLongerTimeToProcessBannerView: StockTradeView {
    public func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        didUpdateQuantitySection(quantity: quantity, error: error)
    }
}

extension QuantityWarningOddLotLongerTimeToProcessBannerView: WXInfoScreenViewControllerListener {
    public func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    public func showModule(_ module: WXInfoScreenViewController) {
        /* empty */
    }
}
