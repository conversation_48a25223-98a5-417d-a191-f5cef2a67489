/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

open class VCMPeriodBannerView: WXBannerInfo, WXTrackable {
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    public var instruction: HSBCStockTradeOrderInstruction?

    // MARK: - Volatility Control Mechanism (VCM) Cooling Period

    public let _isInVCMPeriod: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public let _vcmPeriodStartTime: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _vcmPeriodEndTime: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    public let _vcmPeriodLowerLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _vcmPeriodUpperLimit: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    public let _stockCurrency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)
    public let _stockMarket: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    public let _stockOrderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)
    public let _decimalPlaces: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    public let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    public let _rightButtonDidClick: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    override open func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override open func setupTealiumTaggings() {
        super.setupTealiumTaggings()
        tracker.trackEvent(EventInfo.buyPageCoolingOffBanner(defaultParameters: tracker.defaultParameters))
    }

    override open func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)
    }

    override open func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)
    }

    override open func bindViewModel() {
        super.bindViewModel()

        updateViewModelData()
    }

    open func updateViewModelData() {
        BehaviorRelay.combineLatest(
            _isInVCMPeriod.flatMap(ignoreNil).distinctUntilChanged(),
            _vcmPeriodStartTime.flatMap(ignoreNil).distinctUntilChanged(),
            _vcmPeriodEndTime.flatMap(ignoreNil).distinctUntilChanged(),
            _vcmPeriodLowerLimit.flatMap(ignoreNil).distinctUntilChanged(),
            _vcmPeriodUpperLimit.flatMap(ignoreNil).distinctUntilChanged(),
            _stockMarket.flatMap(ignoreNil),
            _stockCurrency.flatMap(ignoreNil),
            _stockOrderType.flatMap(ignoreNil)
        ).asObservable().subscribe(onNext: { [weak self] isInVCMPeriod, startTime, endTime, lowerLimit, upperLimit, market, currency, orderType in
            guard let self = self else { return }
            if isInVCMPeriod, [HSBCStockTradeOrderType.market, HSBCStockTradeOrderType.limit].contains(orderType) {
                guard let lowerLimitString = PriceStringFormattingUtil.formattedPriceString(price: lowerLimit, market: market), let upperLimiteString = PriceStringFormattingUtil.formattedPriceString(price: upperLimit, market: market) else { return }
                let dateFormat = "HH:mm:ss"
                self.viewModel?.inputs.updateViewData(
                    BannerView.ViewModel(message: "\(localizedString("title_vcm")) \n\(String(format: localizedString("text_vcm_start_end"), startTime.stringWithDateFormat(dateFormat, timeZone: market.timeZone), endTime.stringWithDateFormat(dateFormat, timeZone: market.timeZone))) \n\(localizedString("price_limit")) (\(LocalizationUtil.localizedCurrencyName(code: currency))): \(lowerLimitString) - \(upperLimiteString)", rightButtonAccessibleValue: AccessibleValue(localizedString("button_cta_close"), accessibilityText: localizedString("button_cta_close"), accessibilityValue: "button_cta_close"), rightButtonAction: { [weak self] in
                        guard let self = self else { return }
                        self.viewModel?.inputs.updateModuleVisibility(visible: false)
                        self.rightButtonDidClick()
                    }))
                self.viewModel?.inputs.updateModuleVisibility(visible: true)
            } else {
                self.viewModel?.inputs.updateModuleVisibility(visible: false)
            }
        }).disposed(by: bag)
    }

    public func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        _stockMarket.accept(quote.stockMarket)
        _stockCurrency.accept(quote.currency)
        _isInVCMPeriod.accept(quote.isInVCMPeriod)
        _vcmPeriodStartTime.accept(quote.vcmStartTime)
        _vcmPeriodEndTime.accept(quote.vcmEndTime)
        _vcmPeriodLowerLimit.accept(quote.vcmPriceLowerLimit)
        _vcmPeriodUpperLimit.accept(quote.vcmPriceUpperLimit)
    }

    public func didUpdateSelectedOrderTypeSection(type: HSBCStockTradeOrderType) {
        _stockOrderType.accept(type)
    }

    open func didUpdateInstruction(instruction: HSBCStockTradeOrderInstruction) {
        self.instruction = instruction
        _instruction.accept(instruction)
    }

    public func didUpdateDecimalPlaces(value: Int?) {
        _decimalPlaces.accept(value)
    }

    public func rightButtonDidClick() {
        _rightButtonDidClick.accept(())
    }
}

extension VCMPeriodBannerView: StockQuoteView {
    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateStockQuoteSection(quote: quote)
    }

    public func updateDecimalPlacesSection(decimalPlaces: Int, error: HSBCErrorCode) {
        didUpdateDecimalPlaces(value: decimalPlaces)
    }
}

extension VCMPeriodBannerView: StockTradeView {
    public func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateSelectedOrderTypeSection(type: type)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstruction(instruction: instruction)
    }
}
