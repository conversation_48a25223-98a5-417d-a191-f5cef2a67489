//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import Foundation

public protocol _QuantityInputView: AnyObject {
    func dismissView()
    func enableDoneButton()
    func disableDoneButton()
    func disableQuantityDecreseButton()
    func enableQuantityDecreaseButton()
    func disableQuantityIncreaseButton()
    func enableQuantityIncreaseButton()
    func displayQuantityExplainedView()
    func showInlineWarningMessage(message: String)
    func hideInlineWarningMessage()
}
