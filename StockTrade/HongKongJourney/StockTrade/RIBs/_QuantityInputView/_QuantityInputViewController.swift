/** @View */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift
import UIKit

public class _QuantityInputViewController: DismissableViewController {
    var viewModel: _QuantityInputViewModel!

    // For accessibility only
    weak var accessibilityListener: AccessibilityDelegate?
    private weak var selectedAccessibilityView: UIView?

    var _largePadding: CGFloat = 41
    var _smallPadding: CGFloat = 16
    var scale: CGFloat = 1

    var largePadding: CGFloat {
        let val = -1 * _largePadding * scale
        return val
    }

    var smallPadding: CGFloat {
        let val = -1 * _smallPadding * scale
        return val
    }

    private lazy var quantityTitleLabel: UILabel = {
        let label = UILabel()
        label.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        label.font = ThemeManager.shared.currentTheme.fonts.body
        label.text = localizedString("quantity")
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.isAccessibilityElement = true
        label.accessibilityTraits = .header
        return label
    }()

    public lazy var infoButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.clipsToBounds = true
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        button.setImage(
            ThemeManager.shared.currentTheme.systemIcons.help.withRenderingMode(.alwaysTemplate),
            for: .normal
        )
        button.accessibilityLabel = String(format: "%@", localizedString("wx_accessibility_order_quantity"))
        return button
    }()

    public lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.clipsToBounds = true
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        button.setImage(
            ThemeManager.shared.currentTheme.systemIcons.close.withRenderingMode(.alwaysTemplate),
            for: .normal
        )
        button.accessibilityLabel = localizedString("close")
        return button
    }()

    private lazy var quantityTextField: UITextField = {
        let textField = NonEditableTextField()
        textField.isAccessibilityElement = true
        textField.font = ThemeManager.shared.currentTheme.fonts.title1Emphasized
        textField.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        textField.adjustsFontForContentSizeCategory = true
        textField.keyboardType = .numberPad
        textField.translatesAutoresizingMaskIntoConstraints = false
        return textField
    }()

    lazy var quantityLabel: UILabel = {
        let label = UILabel()
        label.font = ThemeManager.shared.currentTheme.fonts.body
        label.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.numberOfLines = 0
        label.text = localizedString("order_input_enter_quantity") // "Enter number of shares"
        return label
    }()

    private lazy var quantityUpButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.setImage(ThemeManager.shared.currentTheme.systemIcons.addCircle.withRenderingMode(.alwaysTemplate), for: .normal)
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        return button
    }()

    private lazy var quantityDownButton: UIButton = {
        let button = UIButton(type: .custom)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.adjustsImageSizeForAccessibilityContentSizeCategory = true
        button.setImage(ThemeManager.shared.currentTheme.systemIcons.subtractCircle.withRenderingMode(.alwaysTemplate), for: .normal)
        button.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
        return button
    }()

    private lazy var inlineStatusView: InlineStatusView = {
        let messageView = InlineStatusView(status: .warning, text: AccessibleValue(" "))
        messageView.translatesAutoresizingMaskIntoConstraints = false
        messageView.isHidden = true
        return messageView
    }()

    private lazy var quantityInputInfoLabel: UILabel = {
        let label = UILabel()
        label.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
        label.font = ThemeManager.shared.currentTheme.fonts.footnoteEmphasized
        label.translatesAutoresizingMaskIntoConstraints = false
        label.adjustsFontForContentSizeCategory = true
        label.numberOfLines = 0
        return label
    }()

    private lazy var doneButton: PrimaryButton = {
        let button = PrimaryButton(title: localizedString("btn_done"))
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()

    lazy var headerView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    lazy var inputTextBaseView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    lazy var separaterView: UIView = {
        let view = UIView()
        view.backgroundColor = ThemeManager.shared.currentTheme.colors.textPrimary
        view.translatesAutoresizingMaskIntoConstraints = false
        view.heightAnchor.constraint(equalToConstant: 1).isActive = true
        return view
    }()

    private lazy var baseView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.layer.cornerRadius = 4.0
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return view
    }()

    private var keyboardAwareLayoutGuide = UIKeyboardAwareGuide()

    // Rx dispose bag
    private let bag = DisposeBag()

    private var market: WXMarket!

    var tracker: WXTracking!

    internal var instruction: HSBCStockTradeOrderInstruction!

    var constraintsShowingInlinesMessage: [NSLayoutConstraint]!
    var constraintsHiddenInlinesMessage: [NSLayoutConstraint]!
    var activeConstraints: [NSLayoutConstraint]!

    //    private var ctaButtonClickHandler: (() -> Void)?

    public init() {
        tracker = StockTradeJourneyFactory.trackManager
        super.init(nibName: nil, bundle: nil)

        setupViewModel()
    }

    internal func setupViewModel() {
        viewModel = StockTradeJourneyFactory.getObjectFromSuffix(_QuantityInputViewModel.self)
        viewModel.setupView(view: self)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override public func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // Assign the keyboard reference every time the view controlelr is back to foreground
        if keyboardAwareLayoutGuide.owningViewController != self {
            keyboardAwareLayoutGuide.owningViewController = self
        }
        // change accessibility focus
        if let selectedAccessibilityView = self.selectedAccessibilityView {
            self.selectedAccessibilityView = nil
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                UIAccessibility.post(
                    notification: .layoutChanged,
                    argument: selectedAccessibilityView
                )
            }
        }
    }

    deinit {
        viewModel.inputs.viewDeinit()
    }

    override public func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        keyboardAwareLayoutGuide.owningViewController = self
        setupUI()
        setupAccessibilityIDs()
        configureViewModel()
        setupAccessibilityLabel()
    }

    func setupAccessibilityLabel() {
        quantityDownButton.accessibilityLabel = localizedString("alt_decrease")
        quantityUpButton.accessibilityLabel = localizedString("alt_increase")
    }

    override public func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        quantityTextField.becomeFirstResponder()
    }

    func setupUI() {
        view.backgroundColor = ThemeManager.shared.currentTheme.colors.dimmedBackground

        view.addSubview(baseView)

        baseView.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted

        baseView.addSubview(headerView)

        headerView.addSubview(quantityTitleLabel)
        headerView.addSubview(infoButton)
        headerView.addSubview(closeButton)

        baseView.addSubview(inputTextBaseView)

        inputTextBaseView.addSubview(quantityLabel)
        inputTextBaseView.addSubview(quantityTextField)
        inputTextBaseView.addSubview(quantityDownButton)
        inputTextBaseView.addSubview(quantityUpButton)
        inputTextBaseView.addSubview(separaterView)
        baseView.addSubview(inlineStatusView)
        baseView.addSubview(quantityInputInfoLabel)
        baseView.addSubview(doneButton)
        NSLayoutConstraint.activate([
            baseView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            baseView.trailingAnchor.constraint(equalTo: view.trailingAnchor),

            headerView.leadingAnchor.constraint(equalTo: baseView.leadingAnchor),
            headerView.trailingAnchor.constraint(equalTo: baseView.trailingAnchor),
            baseView.topAnchor.constraint(equalTo: headerView.topAnchor, constant: -16.0),

            quantityTitleLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 16.0),
            quantityTitleLabel.topAnchor.constraint(equalTo: headerView.topAnchor),
            quantityTitleLabel.trailingAnchor.constraint(equalTo: infoButton.leadingAnchor, constant: -4),
            quantityTitleLabel.bottomAnchor.constraint(equalTo: headerView.bottomAnchor),
            infoButton.topAnchor.constraint(equalTo: headerView.topAnchor),
            infoButton.trailingAnchor.constraint(lessThanOrEqualTo: closeButton.leadingAnchor, constant: -4),
            infoButton.heightAnchor.constraint(equalTo: infoButton.heightAnchor),
            closeButton.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -16.0),
            closeButton.topAnchor.constraint(equalTo: headerView.topAnchor),
            closeButton.heightAnchor.constraint(equalTo: closeButton.heightAnchor),

            inputTextBaseView.leadingAnchor.constraint(equalTo: baseView.leadingAnchor),
            inputTextBaseView.trailingAnchor.constraint(equalTo: baseView.trailingAnchor),
            inputTextBaseView.topAnchor.constraint(equalTo: quantityLabel.topAnchor, constant: -16),

            quantityLabel.leadingAnchor.constraint(equalTo: inputTextBaseView.leadingAnchor, constant: 16.0),
            quantityLabel.trailingAnchor.constraint(equalTo: inputTextBaseView.trailingAnchor, constant: -16.0),
            quantityLabel.topAnchor.constraint(equalTo: inputTextBaseView.topAnchor, constant: 16.0),
            quantityLabel.bottomAnchor.constraint(equalTo: quantityTextField.topAnchor, constant: -8),

            quantityDownButton.trailingAnchor.constraint(equalTo: quantityUpButton.leadingAnchor, constant: -24.0),
            quantityDownButton.widthAnchor.constraint(equalTo: quantityDownButton.heightAnchor),
            quantityDownButton.bottomAnchor.constraint(equalTo: quantityUpButton.bottomAnchor),
            quantityUpButton.trailingAnchor.constraint(equalTo: inputTextBaseView.trailingAnchor, constant: -16.0),
            quantityUpButton.widthAnchor.constraint(equalTo: quantityUpButton.heightAnchor),
            quantityUpButton.bottomAnchor.constraint(equalTo: separaterView.topAnchor, constant: -4.0),

            quantityTextField.bottomAnchor.constraint(equalTo: separaterView.topAnchor, constant: -4.0),
            quantityTextField.leadingAnchor.constraint(equalTo: inputTextBaseView.leadingAnchor, constant: 16.0),
            quantityTextField.trailingAnchor.constraint(equalTo: separaterView.trailingAnchor),

            separaterView.trailingAnchor.constraint(equalTo: quantityDownButton.leadingAnchor, constant: -24),
            separaterView.heightAnchor.constraint(equalToConstant: 1.0),
            separaterView.leadingAnchor.constraint(equalTo: inputTextBaseView.leadingAnchor, constant: 16.0),

            // In line message view

            doneButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16.0),
            doneButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16.0)
        ])

        switch traitCollection.preferredContentSizeCategory {
        case .accessibilityExtraExtraExtraLarge,
             .accessibilityExtraExtraLarge,
             .accessibilityExtraLarge,
             .accessibilityLarge,
             .accessibilityMedium:
            scale = 0.1
        case .extraExtraExtraLarge,
             .extraExtraLarge,
             .extraLarge:
            scale = 0.8
        default:
            scale = 1
        }

        constraintsShowingInlinesMessage = [
            baseView.bottomAnchor.constraint(equalTo: keyboardAwareLayoutGuide.topAnchor),
            headerView.bottomAnchor.constraint(equalTo: inputTextBaseView.topAnchor, constant: largePadding),
            separaterView.bottomAnchor.constraint(equalTo: inputTextBaseView.bottomAnchor),
            inputTextBaseView.bottomAnchor.constraint(equalTo: inlineStatusView.topAnchor, constant: smallPadding),
            inlineStatusView.leadingAnchor.constraint(equalTo: inputTextBaseView.leadingAnchor, constant: 16.0),
            inlineStatusView.trailingAnchor.constraint(equalTo: inputTextBaseView.trailingAnchor, constant: -16.0),
            inlineStatusView.bottomAnchor.constraint(equalTo: doneButton.topAnchor, constant: largePadding),
            doneButton.bottomAnchor.constraint(equalTo: baseView.safeAreaLayoutGuide.bottomAnchor, constant: smallPadding),
            keyboardAwareLayoutGuide.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ]

        constraintsHiddenInlinesMessage = [
            baseView.bottomAnchor.constraint(equalTo: keyboardAwareLayoutGuide.topAnchor),
            headerView.bottomAnchor.constraint(equalTo: inputTextBaseView.topAnchor, constant: largePadding),
            separaterView.bottomAnchor.constraint(equalTo: inputTextBaseView.bottomAnchor),
            inputTextBaseView.bottomAnchor.constraint(equalTo: quantityInputInfoLabel.topAnchor, constant: smallPadding),
            quantityInputInfoLabel.leadingAnchor.constraint(equalTo: doneButton.leadingAnchor),
            quantityInputInfoLabel.trailingAnchor.constraint(equalTo: doneButton.trailingAnchor),
            quantityInputInfoLabel.bottomAnchor.constraint(equalTo: doneButton.topAnchor, constant: largePadding),
            doneButton.bottomAnchor.constraint(equalTo: baseView.safeAreaLayoutGuide.bottomAnchor, constant: smallPadding),
            keyboardAwareLayoutGuide.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ]

        NSLayoutConstraint.activate(constraintsHiddenInlinesMessage)
        activeConstraints = constraintsHiddenInlinesMessage
    }

    func setupAccessibilityIDs() {
        infoButton.accessibilityIdentifier = "image_quantity_help"
        closeButton.accessibilityIdentifier = "image_quantity_input_close"
        quantityTitleLabel.accessibilityIdentifier = "text_quantity"
        quantityInputInfoLabel.accessibilityIdentifier = "text_quantity_message"
        quantityTextField.accessibilityIdentifier = "edittext_quantity"
        quantityDownButton.accessibilityIdentifier = "image_minus_quantity"
        quantityUpButton.accessibilityIdentifier = "image_add_quantity"
        doneButton.accessibilityIdentifier = "button_quantity_done"
    }

    func configureViewModel() {
        quantityTextField.delegate = self

        // Bind close button action
        closeButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickCloseButton()
            self.viewModel.inputs.didEnterQuantity(quantity: "")
            // For accessibility only
            self.accessibilityListener?.shouldSelectAccessibilityView()
        }.disposed(by: bag)

        // Bind info button
        infoButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickHelpButton()
            // For accessibility only
            self.selectedAccessibilityView = self.infoButton
        }.disposed(by: bag)

        // Bind increase quantity button
        quantityUpButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickIncreaseQuantityButton()
        }.disposed(by: bag)

        // Bind decrease quantity button
        quantityDownButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickDecreaseQuantityButton()
        }.disposed(by: bag)

        // set CTA button action
        doneButton.buttonPressedCompletion = { [weak self] in
            guard let self = self else { return }
            self.viewModel.inputs.didClickDoneButton()
            //            self.ctaButtonClickHandler?()
        }

        // Update quantity inputed
        viewModel.outputs.proposedQuantity.distinctUntilChanged().asObservable().subscribe(onNext: { [weak self] quantity in
            guard let self = self else { return }
            if let quantity = quantity, quantity > 0 {
                self.quantityTextField.text = "\(quantity)"
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                    UIAccessibility.post(notification: UIAccessibility.Notification.announcement, argument: String(format: localizedString("alt_updated_to"), "\(quantity)"))
                }
            } else {
                self.quantityTextField.text = nil
            }
        }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            viewModel._proposedQuantity.distinctUntilChanged(),
            viewModel._proposedQuantity.distinctUntilChanged().scan(0) { $1 }
        ).asObservable().subscribe(onNext: { [weak self] proposedQuantity, previousProposedQuantity in
            guard let _ = self else { return }
            if (proposedQuantity == nil || proposedQuantity == 0) &&
                (previousProposedQuantity != nil && previousProposedQuantity != 0) {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.75) {
                    UIAccessibility.post(notification: UIAccessibility.Notification.announcement, argument: String(format: localizedString("alt_updated_to"), "\(0)"))
                }
            }
        }).disposed(by: bag)

        viewModel.outputs.lotSize.asObservable().subscribe(onNext: { [weak self] lotSize in
            guard let self = self else { return }
            if let lotSize = lotSize, lotSize > 1 {
                self.quantityInputInfoLabel.text = String(format: localizedString("quantity_warning_inline_message"), "\(lotSize)") // "Enter quantity in lot size of \(lotSize)"
            } else {
                self.quantityInputInfoLabel.text = localizedString("enter_quantity") // "Enter quantity"
            }
        }).disposed(by: bag)

        viewModel.inputs.viewDidLoad()
    }

    override public func handleDragOrTapToDismiss() {
        viewModel.inputs.didClickCloseButton()
        viewModel.inputs.didEnterQuantity(quantity: "")
    }
}

extension _QuantityInputViewController: _QuantityInputView {
    public func disableQuantityDecreseButton() {
        quantityDownButton.isEnabled = false
        quantityDownButton.tintColor = ThemeManager.shared.currentTheme.colors.textSecondary
    }

    public func enableQuantityDecreaseButton() {
        quantityDownButton.isEnabled = true
        quantityDownButton.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
    }

    public func disableQuantityIncreaseButton() {
        quantityUpButton.isEnabled = false
        quantityUpButton.tintColor = ThemeManager.shared.currentTheme.colors.textSecondary
    }

    public func enableQuantityIncreaseButton() {
        quantityUpButton.isEnabled = true
        quantityUpButton.tintColor = ThemeManager.shared.currentTheme.colors.textPrimary
    }

    public func dismissView() {
        // Remove keyboard reference upon leaving screen
        //        keyboardAwareLayoutGuide.owningViewController = nil

        view.endEditing(true)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            guard let self = self else { return }
            self.dismiss(animated: true, completion: nil)
        }
    }

    public func enableDoneButton() {
        doneButton.state = .enabled
    }

    public func disableDoneButton() {
        doneButton.state = .disabled
    }

    public func displayQuantityExplainedView() {
        let quantityInputLearnMoreScreen = StockTradeJourneyFactory.getObjectFromSuffix(QuantityInputLearnMoreScreen.self)
        quantityInputLearnMoreScreen.setListener(self)
        present(quantityInputLearnMoreScreen.embedInNavigationController(), animated: true, completion: nil)
    }

    public func showInlineWarningMessage(message: String) {
        inlineStatusView.text = AccessibleValue(message)
        inlineStatusView.isHidden = false
        quantityInputInfoLabel.isHidden = true

        if activeConstraints != constraintsShowingInlinesMessage {
            activeConstraints = constraintsShowingInlinesMessage
            NSLayoutConstraint.activate(constraintsShowingInlinesMessage)
            NSLayoutConstraint.deactivate(constraintsHiddenInlinesMessage)
        }
    }

    public func hideInlineWarningMessage() {
        inlineStatusView.text = AccessibleValue("")
        inlineStatusView.isHidden = true
        quantityInputInfoLabel.isHidden = false
        if activeConstraints != constraintsHiddenInlinesMessage {
            activeConstraints = constraintsHiddenInlinesMessage
            NSLayoutConstraint.deactivate(constraintsShowingInlinesMessage)
            NSLayoutConstraint.activate(constraintsHiddenInlinesMessage)
        }
    }
}

extension _QuantityInputViewController: UITextFieldDelegate {
    public func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if let text = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) {
            viewModel.inputs.didEnterQuantity(quantity: text)
        }
        return false
    }
}

extension _QuantityInputViewController: WXInfoScreenViewControllerListener {
    public func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    public func showModule(_ module: WXInfoScreenViewController) {
        /* empty */
    }
}
