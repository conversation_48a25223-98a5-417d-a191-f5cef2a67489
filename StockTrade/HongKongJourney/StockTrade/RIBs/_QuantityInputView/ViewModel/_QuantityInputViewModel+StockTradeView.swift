//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation

extension _QuantityInputViewModel: StockTradeView {
    public func updateProposedQuantitySection(proposedQuantity: Int64?, error: HSBCErrorCode) {
        inputs.didUpdateProposedQuantitySection(quantity: proposedQuantity, error: error)
    }

    public func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        inputs.didUpdateQuantitySection(quantity: quantity)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        inputs.didUpdateInstructionSection(instruction: instruction)
    }
}
