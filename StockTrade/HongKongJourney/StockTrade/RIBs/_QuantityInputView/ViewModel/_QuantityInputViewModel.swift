//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import MobileCore
import RxCocoa
import RxSwift
import UIKit
// import wealthxcore

open class _QuantityInputViewModel: NSObject, ViewModelType, _QuantityInputViewModelInputs, _QuantityInputViewModelOutputs, WXTrackable {
    public weak var stockTradePresenter: StockTradePresenterImplementation!
    public weak var stockQuotePresenter: StockQuotePresenterImplementation!

    public weak var view: _QuantityInputView?

    internal var inputs: _QuantityInputViewModelInputs { self }
    internal var outputs: _QuantityInputViewModelOutputs { self }

    // Behaviour relays
    public let _lotSize: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    public let _proposedQuantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    public let _quantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    public let _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil) // tbc
    public let _quantityError: BehaviorRelay<HSBCErrorCode?> = BehaviorRelay(value: nil)
    public let _tradingPeriodType: BehaviorRelay<HSBCTradingPeriodType?> = BehaviorRelay(value: nil)
    public let _errorCode: BehaviorRelay<HSBCErrorCode?> = BehaviorRelay(value: nil)
    public let _errorWarnings: BehaviorRelay<[HSBCWarning]?> = BehaviorRelay(value: nil)

    // Rx dispose bag
    public let bag = DisposeBag()
    public let _orderInstruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    public let _didClickDoneButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public let _didClickHelpButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    override public init() {
        super.init()
    }

    init(view: _QuantityInputView) {
        super.init()
        setupView(view: view)
    }

    deinit {
        logStatic("QuantityInputViewModel: deinit", level: .debug, namespace: .stockTrade)
    }

    func setupView(view: _QuantityInputView) {
        self.view = view
        stockTradePresenter = StockTradePresenterImplementation.configure(view: self, routerImplementation: nil)
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    // MARK: inputs

    public func viewDidLoad() {
        stockQuotePresenter?.viewDidLoad(view: self)
        stockTradePresenter?.viewDidLoad(view: self)

        // Disable quantity increase button, if the next increment will exceed upper limit
        _quantityError.asObservable().flatMap(ignoreNil).distinctUntilChanged().subscribe(onNext: { [weak self] error in
            guard let self = self else { return }
            switch error {
            case .almostReachUpperLimit:
                self.view?.disableQuantityIncreaseButton()
            default:
                self.view?.enableQuantityIncreaseButton()
            }
        }).disposed(by: bag)

        // Update state of minus button according to quantity
        _proposedQuantity.asObservable().subscribe(onNext: { [weak self] quantity in
            guard let self = self else { return }
            if let quantity = quantity, quantity > 0 {
                self.view?.enableQuantityDecreaseButton()
                self.view?.enableDoneButton()
            } else {
                self.view?.disableQuantityDecreseButton()
                self.view?.disableDoneButton()
            }
        }).disposed(by: bag)

        setupInlineMessage()

        setupTealiumTaggings()
    }

    open func viewDidDisappear() {}

    public func viewDeinit() {
        stockQuotePresenter?.viewDeinit(view: self)
        stockTradePresenter?.viewDeinit(view: self)

        if let quantity = _quantity.value { // reset proposed quantity after panel is dismissed
            stockTradePresenter?.didEditSection(section: .proposedQuantity(quantity))
        }
    }

    open func setupInlineMessage() {
        BehaviorRelay.combineLatest(
            _quantityError
                .asObservable()
                .flatMap(ignoreNil)
                .filter { [
                    .quantityEqualExecutedQuantity,
                    .quantityLessThanExecutedQuantity,
                    .noError
                ].contains($0)
                },
            _lotSize
                .asObservable()
                .flatMap(ignoreNil)
        )
        .subscribe(onNext: { [weak self] error, lotSize in
            guard let self = self else { return }
            switch error {
            case .quantityEqualExecutedQuantity, .quantityLessThanExecutedQuantity:
                self.view?.disableDoneButton()
                let msg = error == .quantityEqualExecutedQuantity ? localizedString("warning_quantity_equal_executed_quantity") : localizedString("warning_quantity_less_than_executed_quantity")
                self.view?.showInlineWarningMessage(message: msg)
            default:
                self.view?.hideInlineWarningMessage()
            }
        }).disposed(by: bag)
    }

    open func setupTealiumTaggings() {
        _orderInstruction.flatMap(ignoreNil).take(1).subscribe(onNext: { [weak self] instruction in
            guard let self = self else { return }
            if instruction == .buy {
            } else if instruction == .sell {
                self.tracker.trackPage(EventInfo.sellQuantityPage(defaultParameters: self.tracker.defaultParameters))
            }
        }).disposed(by: bag)

        _didClickDoneButton.take(1).debounce(.milliseconds(300), scheduler: MainScheduler.instance).subscribe(onNext: { [weak self] _ in
            guard let self = self else { return }
            if self._orderInstruction.value == .sell {
                self.tracker.trackEvent(EventInfo.sellQuantityClickDone(defaultParameters: self.tracker.defaultParameters))
            }
        }).disposed(by: bag)
    }

    open func didClickCloseButton() {
        view?.dismissView()
    }

    open func didClickHelpButton() {
        _didClickHelpButton.accept(())
        view?.displayQuantityExplainedView()
    }

    open func didClickDoneButton() {
        if let quantity = _proposedQuantity.value {
            _didClickDoneButton.accept(())
            stockTradePresenter?.didEditSection(section: .quantity(quantity: quantity))
            view?.dismissView()
        }
    }

    open func didClickIncreaseQuantityButton() {
        stockTradePresenter?.attemptIncreaseProposedQuantity()
    }

    open func didClickDecreaseQuantityButton() {
        stockTradePresenter?.attemptDecreaseProposedQuantity()
    }

    open func didEnterQuantity(quantity: String) {
        if quantity.isEmpty {
            stockTradePresenter?.didEditSection(section: .proposedQuantity(nil))
        } else if validateQuantity(quantity: quantity), let proposedQuantity = Int64(quantity) {
            stockTradePresenter?.didEditSection(section: .proposedQuantity(proposedQuantity))
        }
    }

    open func didUpdateStockQuoteSection(quote: StockQuoteDetail) {
        if let lotSize = quote.lotSize {
            _lotSize.accept(lotSize)
        }
    }

    open func didUpdateProposedQuantitySection(quantity: Int64?, error: HSBCErrorCode) {
        _proposedQuantity.accept(quantity)
        _quantityError.accept(error)
    }

    open func didUpdateQuantitySection(quantity: Int64) {
        _quantity.accept(quantity)
    }

    open func didUpdateTradingPeriodType(type: HSBCTradingPeriodType) {
        _tradingPeriodType.accept(type)
    }

    open func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _orderInstruction.accept(instruction)
    }

    private func validateQuantity(quantity: String) -> Bool {
        let notAllowedCharSet = NSCharacterSet(charactersIn: "0123456789").inverted
        if let range = quantity.rangeOfCharacter(from: notAllowedCharSet), !range.isEmpty {
            return false
        }
        return quantity.count <= 13
    }

    open func didShowErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        _errorCode.accept(errorCode)
        _errorWarnings.accept(warnings)
    }

    // MARK: outputs

    public var lotSize: Driver<Int?> {
        _lotSize.asDriver().distinctUntilChanged()
    }

    public var proposedQuantity: Driver<Int64?> {
        _proposedQuantity.asDriver().distinctUntilChanged()
    }

    public var quantityError: Driver<HSBCErrorCode> {
        _quantityError.asDriver().flatMap(ignoreNil)
    }

    public var errorCode: Driver<HSBCErrorCode> {
        _errorCode.asDriver().flatMap(ignoreNil)
    }

    public var errorWarnings: Driver<[HSBCWarning]?> {
        _errorWarnings.asDriver().flatMap(ignoreNil)
    }
}
