//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
extension _QuantityInputViewModel: StockQuoteView {
    public func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        inputs.didUpdateStockQuoteSection(quote: quote)
    }

    public func updateAvailableGoodUntilDatesSection(dates: [Date], error: HSBCErrorCode) {}

    public func updateAvailableOrderTypes(types: [HSBCStockTradeOrderType], error: HSBCErrorCode) {}

    public func updateTradingPeriodType(type: HSBCTradingPeriodType) {
        inputs.didUpdateTradingPeriodType(type: type)
    }

    public func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        inputs.didShowErrorMessage(type, errorCode: errorCode, warnings: warnings)
    }
}
