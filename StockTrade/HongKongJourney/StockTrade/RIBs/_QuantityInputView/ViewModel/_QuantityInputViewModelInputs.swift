//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

// import wealthxcore

protocol _QuantityInputViewModelInputs {
    func viewDidLoad()
    func viewDidDisappear()
    func viewDeinit()

    func didClickCloseButton()
    func didClickHelpButton()
    func didClickDoneButton()
    func didClickIncreaseQuantityButton()
    func didClickDecreaseQuantityButton()
    func didEnterQuantity(quantity: String)

    // Presenter callbacks
    func didUpdateStockQuoteSection(quote: StockQuoteDetail)
    func didUpdateProposedQuantitySection(quantity: Int64?, error: HSBCErrorCode)
    func didUpdateQuantitySection(quantity: Int64)
    func didUpdateTradingPeriodType(type: HSBCTradingPeriodType)
    func didShowErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?)
    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction)
}
