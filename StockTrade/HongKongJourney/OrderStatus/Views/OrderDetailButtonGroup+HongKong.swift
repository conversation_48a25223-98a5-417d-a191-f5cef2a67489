/** @RIB */
//
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class OrderDetailButtonGroupHongKong: WXButtonStackView, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    weak var stockOrderPresenter: StockOrderPresenterImplementation!

    var _orderRecord: BehaviorRelay<StockOrderDetail?> = BehaviorRelay(value: nil)

    private lazy var cancelButton: ButtonWithImage = {
        let button = ButtonWithImage(type: .custom)
        let image = ThemeManager.shared.currentTheme.systemIcons.clear.withRenderingMode(.alwaysOriginal)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setImage(image.tintWithColor(ThemeManager.shared.currentTheme.colors.textPrimary), for: .normal)
        button.setTitle(localizedString("order_status_detail_cancel"), for: .normal)
        button.setTitleColor(ThemeManager.shared.currentTheme.colors.textPrimary, for: .normal)
        button.titleLabel?.font = ThemeManager.shared.currentTheme.fonts.caption
        button.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        button.accessibilityIdentifier = "button_cancel"
        return button
    }()

    private lazy var modifyButton: ButtonWithImage = {
        let button = ButtonWithImage(type: .custom)
        let image = ThemeManager.shared.currentTheme.systemIcons.edit.withRenderingMode(.alwaysOriginal)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setImage(image.tintWithColor(ThemeManager.shared.currentTheme.colors.textPrimary), for: .normal)
        button.setTitle(localizedString("order_status_detail_modify"), for: .normal)
        button.setTitleColor(ThemeManager.shared.currentTheme.colors.textPrimary, for: .normal)
        button.titleLabel?.font = ThemeManager.shared.currentTheme.fonts.caption
        button.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        button.accessibilityIdentifier = "button_modify"
        return button
    }()

    var CTA: Driver<[ButtonWithImage]> {
        _orderRecord.asDriver().flatMap(ignoreNil).map { ($0.allowCancel, $0.allowModify) }.map { [weak self] in
            guard let self = self else { return [] }
            var buttons: [ButtonWithImage] = []

            if let allowModify = $0.1, allowModify {
                buttons.append(self.modifyButton)
            }

            if let allowCancel = $0.0, allowCancel {
                buttons.append(self.cancelButton)
            }

            return buttons
        }.distinctUntilChanged()
    }

    override func initializeVariables() {
        super.initializeVariables()
        if let viewControllable = self.viewControllable {
            stockOrderPresenter = StockOrderPresenterImplementation.configure(view: self, routerImplementation: StockOrderRouterImplementation(viewController: viewControllable))
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockOrderPresenter?.viewDidLoad(view: self)
        viewModel?.inputs.updateModuleVisibility(visible: false)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockOrderPresenter?.viewDeinit(view: self)
    }

    override func setupViews() {
        super.setupViews()
    }

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
    }

    override func bindViewModel() {
        super.bindViewModel()
        CTA.asObservable().subscribe(onNext: { [weak self] buttons in
            guard let self = self else { return }
            self.viewModel?.inputs.updateButton(buttons: buttons)
            if buttons.isEmpty {
                self.viewModel?.inputs.updateModuleVisibility(visible: false)
            } else {
                self.viewModel?.inputs.updateModuleVisibility(visible: true)
            }
        }).disposed(by: bag)

        modifyButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.didClickModifyButton()
        }.disposed(by: bag)

        cancelButton.rx.tap.bind { [weak self] in
            guard let self = self else { return }
            self.didClickCancelButton()
        }.disposed(by: bag)
    }

    func updateOrderDetail(orderRecord: StockOrderDetail) {
        _orderRecord.accept(orderRecord)
    }

    func didClickModifyButton() {
        stockOrderPresenter?.navigateToModifyOrder(view: self)
    }

    func didClickCancelButton() {
        stockOrderPresenter?.navigateToCancelOrder(view: self)
    }
}

extension OrderDetailButtonGroupHongKong: StockOrderView {
    // order detail
    func updateSelectedOrderDetailSection(record: StockOrderDetail) {
        updateOrderDetail(orderRecord: record)
    }
}
