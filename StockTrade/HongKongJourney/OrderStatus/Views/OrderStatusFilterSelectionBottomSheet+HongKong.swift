/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class OrderStatusFilterSelectionBottomSheetHongKong: WXMultipleSelectionWithSwitchBottomSheet, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    weak var stockOrderPresenter: StockOrderPresenterImplementation!

    var _availableOrderStatuses: BehaviorRelay<[HSBCOrderStatus]?> = BehaviorRelay(value: nil)
    var _availableInProgressOrderStatus: BehaviorRelay<[HSBCOrderStatus]?> = BehaviorRelay(value: nil)
    var _selectedOrderStatuses: BehaviorRelay<[HSBCOrderStatus]?> = BehaviorRelay(value: nil)
    var _filteredStatus: BehaviorRelay<[HSBCOrderStatus]?> = BehaviorRelay(value: [.pendingResume, .unknown])
    var _hiddenStatus: BehaviorRelay<[HSBCOrderStatus]?> = BehaviorRelay(value: [.pendingResume])

    override func initializeVariables() {
        super.initializeVariables()
        headerTitle = localizedString("order_filter_status_item")
        switchTitle = localizedString("order_filter_switch_desc")
        confirmButtonTitle = localizedString("confirm")
        stockOrderPresenter = StockOrderPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockOrderPresenter?.viewDidLoad(view: self)
        tracker.trackPage(EventInfo.orderStatusStatusPageLoad(defaultParameters: tracker.defaultParameters))
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockOrderPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        BehaviorRelay.combineLatest(
            _availableOrderStatuses.flatMap(ignoreNil).distinctUntilChanged(),
            _availableInProgressOrderStatus.flatMap(ignoreNil).distinctUntilChanged(),
            _selectedOrderStatuses.flatMap(ignoreNil).distinctUntilChanged(),
            _filteredStatus.flatMap(ignoreNil).distinctUntilChanged()
        ).subscribe(onNext: { [weak self] available, availableInProgressOrderStatus, selected, filteredStatus in
            guard let self = self else { return }
            // Do not show `PDRM` @ bottom sheet
            let filtered = available.filter { filteredStatus.firstIndex(of: $0) == nil }
            self.viewModel?.inputs.updateSelectedIndexesWhenSwitchOn(indexes: availableInProgressOrderStatus.compactMap { filtered.firstIndex(of: $0) })
            self.viewModel?.inputs.updateDatasource(data: filtered.compactMap { selected.contains($0) ? RowData(title: $0.bindToWXOrderStatus().title, isSelected: true) : RowData(title: $0.bindToWXOrderStatus().title, isSelected: false) })
        }).disposed(by: bag)

        viewModel?.outputs.confirmButtonClicked.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            Observable.combineLatest(
                self.viewModel!.outputs.options.asObservable(),
                self._availableOrderStatuses.flatMap(ignoreNil).asObservable(),
                self._availableInProgressOrderStatus.flatMap(ignoreNil).distinctUntilChanged().asObservable(),
                self._hiddenStatus.flatMap(ignoreNil).asObservable()
            ).take(1).subscribe(onNext: { [weak self] options, available, availableInProgressOrderStatus, hiddenStatus in
                guard let self = self else { return }

                var selectedOrderStatus: [HSBCOrderStatus] = []
                options.filter { $0.isSelected }.forEach { option in
                    guard let index = options.firstIndex(of: option) else { return }
                    selectedOrderStatus.append(available[index])
                }

                let selectedSet = Set(selectedOrderStatus)
                let inProgressOrderStatusList = Set(availableInProgressOrderStatus)
                if inProgressOrderStatusList.isSubset(of: selectedSet), selectedSet.isSubset(of: inProgressOrderStatusList) {
                    // Pending resume order should be shown ONLY IF pendingAmendment, .pendingCapture, .pendingDealing, .pendingCancellation are selected and no others are selected
                    selectedOrderStatus.append(.pendingResume)
                } else if options.count == options.filter({ $0.isSelected }).count {
                    // If everything selected, need to show pending resume.
                    selectedOrderStatus.append(.pendingResume)
                }

                self.stockOrderPresenter?.didEditSection(section: .orderStatus(orderStatus: selectedOrderStatus))

                // Tealium tagging
                let array: [String] = selectedOrderStatus.map { (status) -> String in
                    status.rawValue
                }
                self.tracker.trackEvent(EventInfo.orderStatusStatusConfirmClick(array.joined(separator: "|"), defaultParameters: self.tracker.defaultParameters))
                self.dismiss(animated: true, completion: nil)
            }).disposed(by: self.bag)
        }).disposed(by: bag)
    }

    func didUpdateAvailableOrderStatuses(availableOrderStatus: [HSBCOrderStatus], availableInProgressOrderStatus: [HSBCOrderStatus]) {
        _availableOrderStatuses.accept(availableOrderStatus)
        _availableInProgressOrderStatus.accept(availableInProgressOrderStatus)
    }

    func didUpdateSelectedOrderStatuses(statuses: [HSBCOrderStatus]) {
        _selectedOrderStatuses.accept(statuses)
    }
}

extension OrderStatusFilterSelectionBottomSheetHongKong: StockOrderView {
    public func updateOrderStatusFilterSection(statuses: [HSBCOrderStatus], inProgressStatuses: [HSBCOrderStatus], selected: [HSBCOrderStatus]) {
        didUpdateAvailableOrderStatuses(availableOrderStatus: statuses, availableInProgressOrderStatus: inProgressStatuses)
        didUpdateSelectedOrderStatuses(statuses: selected)
    }
}
