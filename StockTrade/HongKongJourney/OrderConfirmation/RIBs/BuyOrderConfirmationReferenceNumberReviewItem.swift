/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationReferenceNumberReviewItem: WXReviewItem, StockBuyView {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultTitle = localizedString("order_reference_number")
        defaultValue = localizedString("default_item_value")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func setupAccessibilityIndentifiers() {
        // Reference number
        reviewItem.titleLabel.accessibilityIdentifier = "textview_reference_number_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_reference_number_value"
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateModuleVisibility(visible: true)
    }

    func updateReferenceNumberSection(number: String, error: HSBCErrorCode) {
        viewModel?.inputs.updateValue(value: number)
    }
}
