/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationEstimatedTotalReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_estimated_total"
        // Trade amount
        reviewItem.titleLabel.accessibilityIdentifier = "textview_trade_amount_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_trade_amount_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_confirmation_estimated_total")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }
}

extension BuyOrderConfirmationEstimatedTotalReviewItem: StockBuyView {
    func updateCaculatedEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode, error: HSBCErrorCode) {
        if let estimatedTotalValue = total, let value = PriceStringFormattingUtil.formattedPriceString(price: estimatedTotalValue.value, decimalPlaces: estimatedTotalValue.decimalPlaces, currency: currency, isEstimatedTotal: true) {
            viewModel.inputs.updateValue(value: value)
        }
    }
}
