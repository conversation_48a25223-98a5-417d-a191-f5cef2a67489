/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift

class BuyOrderConfirmationNavigationBar: WXNavigationBarWithImagebaseLeftRightButton {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!
    var _duplicatedRequestIndicator: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    var _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    var _investmentAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)

    let _orderReferenceNumber: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _stockCode: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _stockName: BehaviorRelay<String?> = BehaviorRelay(value: nil)

    let _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)
    let _price: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    let _quantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    let _tradeAmount: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    let _tradeAmountCurrency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        leftButtonImage = ThemeManager.shared.currentTheme.systemIcons.close
        leftButtonImageContentDescription = "button_toolbar_close"
        titleLabelContentDescription = "text_toolbar_title"
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        removeFromSuperview()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        viewModel?.inputs.updateTitle(localizedString("stock_confirmation_title"))

        viewModel?.outputs.didClickLeftButton.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            if let instruction = self._instruction.value,
                let market = self._market.value,
                let investmentAccount = self._investmentAccount.value,
                let viewControllable = self.viewControllable {
                self.removeFromSuperview()
                if [.cancelBuy, .modifyBuy].contains(instruction) {
                    let config = StockTradeJourneyFactory.generateOrderStatusConfig(market, priorInvestmentAccount: investmentAccount)
                    StockTradeJourneyFactory.transitJourney(viewController: viewControllable, intent: .navigateToOrderStatusJourney, config: config, animated: true)
                } else {
                    // finish by journey
                    StockTradeJourneyFactory.finishBuyJourney()
                }
            }
        }).disposed(by: bag)
    }

    override func setupTealiumTaggings() {
        super.setupTealiumTaggings()

        let referenceNumberAndStockCodeAndStockName = BehaviorRelay.combineLatest(_orderReferenceNumber.distinctUntilChanged().flatMap(ignoreNil), _stockCode.distinctUntilChanged().flatMap(ignoreNil), _stockName.distinctUntilChanged().flatMap(ignoreNil)) { ($0, $1, $2) }

        let marketAndOrderTypeAndTradeAmount = BehaviorRelay.combineLatest(_market.distinctUntilChanged().flatMap(ignoreNil), _orderType.distinctUntilChanged().flatMap(ignoreNil), _tradeAmount.distinctUntilChanged()) { ($0, $1, $2) }

        BehaviorRelay.combineLatest(
            _instruction.distinctUntilChanged().flatMap(ignoreNil),
            referenceNumberAndStockCodeAndStockName,
            marketAndOrderTypeAndTradeAmount,
            _quantity.distinctUntilChanged().flatMap(ignoreNil),
            _price.distinctUntilChanged().flatMap(ignoreNil),
            _tradeAmountCurrency.distinctUntilChanged().flatMap(ignoreNil)
        ).take(1)
            .subscribe(onNext: { [weak self] instruction, referenceNumberAndStockCodeAndStockName, marketAndOrderTypeAndTradeAmount, quantity, price, tradeAmountCurrency in
                //  modify & cancel screen 's view page load tagging is independent
                guard let self = self, [.cancelBuy, .modifyBuy].contains(instruction) else { return }
                let referenceNumber = "\(referenceNumberAndStockCodeAndStockName.0)"
                let stockName = "\(referenceNumberAndStockCodeAndStockName.2)"
                let orderType = "\(marketAndOrderTypeAndTradeAmount.1)"
                self.tracker.trackPage(EventInfo.orderStatusBuyConfirmationPage(referenceNumber: referenceNumber, stockName: stockName, price: "\(String(describing: price))", quantity: "\(String(describing: quantity))", orderType: orderType, orderInstruction: instruction.rawValue, defaultParameters: self.tracker.defaultParameters))
            })
            .disposed(by: bag)

        BehaviorRelay.combineLatest(
            _duplicatedRequestIndicator.flatMap(ignoreNil).distinctUntilChanged(),
            _instruction.distinctUntilChanged().flatMap(ignoreNil),
            referenceNumberAndStockCodeAndStockName,
            marketAndOrderTypeAndTradeAmount,
            _quantity.distinctUntilChanged().flatMap(ignoreNil),
            _price.distinctUntilChanged().flatMap(ignoreNil),
            _tradeAmountCurrency.distinctUntilChanged().flatMap(ignoreNil)
        ).take(1)
            .subscribe(onNext: { [weak self] duplicatedRequestIndicator, instruction, referenceNumberAndStockCodeAndStockName, marketAndOrderTypeAndTradeAmount, quantity, price, tradeAmountCurrency in
                // split tagging logic because _duplicatedRequestIndicator is nil when modify & cancel screen
                guard let self = self, instruction == .buy else { return }
                let referenceNumber = "\(referenceNumberAndStockCodeAndStockName.0)"
                let stockCode = "\(referenceNumberAndStockCodeAndStockName.1)"
                let market = "\(marketAndOrderTypeAndTradeAmount.0)"
                let orderType = "\(marketAndOrderTypeAndTradeAmount.1)"
                let currency = tradeAmountCurrency.rawValue
                let duplicatedRequest = duplicatedRequestIndicator ?"duplicate order" : "non duplicate order"

                self.tracker.track(
                    "HKBuy-32",
                    [
                        .applicationDecision: referenceNumber,
                        .formField1: "stock code: \(stockCode)",
                        .formField2: "market: \(market)",
                        .formField3: "order type: \(orderType)",
                        .formField4: "quantity: \(quantity)",
                        .formField5: "estimated total: \(marketAndOrderTypeAndTradeAmount.2 == nil ? "" : "\(marketAndOrderTypeAndTradeAmount.2!)")",
                        .amount: "price: \(price)",
                        .currencyCode: currency,
                        .eventContent: duplicatedRequest,
                        .pageSecurityLevel: "30"
                    ]
                )
                if duplicatedRequestIndicator {
                    self.tracker.appDynamicTrackEventWithoutArguments("Duplicate order submission - HTTP 200 - Confirmed duplicate order - Confirmation", String(describing: type(of: self)))
                }
            })
            .disposed(by: bag)
    }

    public func didUpdateDuplicatedRequestIndicatorSection(duplicatedRequestIndicator: Bool, warnings: [HSBCWarning]?) {
        _duplicatedRequestIndicator.accept(duplicatedRequestIndicator)
    }

    public func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        _instruction.accept(instruction)
    }

    public func didUpdateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        _market.accept(market)
    }

    func didUpdateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        _investmentAccount.accept(account)
    }

    func didUpdateReferenceNumberSection(number: String, error: HSBCErrorCode) {
        _orderReferenceNumber.accept(number)
    }

    func didUpdateStockCodeSection(code: String, error: HSBCErrorCode) {
        _stockCode.accept(code)
    }

    func didUpdateStockNameSection(name: String, error: HSBCErrorCode) {
        _stockName.accept(name)
    }

    func didUpdatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        _price.accept(price)
    }

    func didUpdateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        _quantity.accept(quantity)
    }

    func didUpdateCaculatedEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode, error: HSBCErrorCode) {
        _tradeAmount.accept(total?.value)
        _tradeAmountCurrency.accept(currency)
    }

    func didUpdateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        _orderType.accept(type)
    }
}

extension BuyOrderConfirmationNavigationBar: StockBuyView {
    public func updateDuplicatedRequestIndicatorSection(duplicatedRequestIndicator: Bool, warnings: [HSBCWarning]?) {
        didUpdateDuplicatedRequestIndicatorSection(duplicatedRequestIndicator: duplicatedRequestIndicator, warnings: warnings)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction, error: error)
    }

    public func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        didUpdateStockMarketSection(market: market, error: error)
    }

    func updateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        didUpdateInvestmentAccountSection(account: account, error: error)
    }

    func updateReferenceNumberSection(number: String, error: HSBCErrorCode) {
        didUpdateReferenceNumberSection(number: number, error: error)
    }

    func updateStockCodeSection(code: String, error: HSBCErrorCode) {
        didUpdateStockCodeSection(code: code, error: error)
    }

    func updateStockNameSection(name: String, error: HSBCErrorCode) {
        didUpdateStockNameSection(name: name, error: error)
    }

    func updatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdatePriceSection(price: price, currency: currency, error: error)
    }

    func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        didUpdateQuantitySection(quantity: quantity, error: error)
    }

    func updateCaculatedEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdateCaculatedEstimatedTotalSection(total: total, currency: currency, error: error)
    }

    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateSelectedOrderTypeSection(type: type, error: error)
    }
}
