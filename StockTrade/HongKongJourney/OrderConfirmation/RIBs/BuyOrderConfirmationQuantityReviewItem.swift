/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationQuantityReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_quantity"
        // Quantity
        reviewItem.titleLabel.accessibilityIdentifier = "textview_quantity_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_quantity_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("quantity")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }
}

extension BuyOrderConfirmationQuantityReviewItem: StockBuyView {
    func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        viewModel.inputs.updateValue(value: String(format: localizedString("number_of_shares"), "\(quantity)"))
    }
}
