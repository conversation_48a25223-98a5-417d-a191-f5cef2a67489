/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationSecondaryButton: WXSecondaryButton, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _instructionType: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    var _investmentAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)

    override func initializeVariables() {
        super.initializeVariables()
        title = localizedString("stock_review_check_order_status")
        accessibilityIdentifier = "button_check_order_status"
        if let vc = viewControllable {
            stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: StockBuyRouterImplementation(viewController: vc, tracker: tracker))
        }
    }

    override func setupViews() {
        super.setupViews()
        NSLayoutConstraint.activate([
            secondaryButton.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            secondaryButton.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -16),
            secondaryButton.topAnchor.constraint(equalTo: topAnchor, constant: 16),
            secondaryButton.bottomAnchor.constraint(equalTo: bottomAnchor),
            secondaryButton.widthAnchor.constraint(equalTo: widthAnchor, multiplier: 1.0, constant: -32.0)
        ])
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?
            .outputs
            .clickedButton
            .asObservable()
            .subscribe(onNext: {
                BehaviorRelay.combineLatest(
                    self._market,
                    self._investmentAccount,
                    self._instructionType
                )
                .take(1)
                .asObservable().subscribe(onNext: {
                    [weak self] market, investmentAccount, instruction in
                    guard let self = self else { return }
                    let config = StockTradeJourneyFactory.generateOrderStatusConfig(market, priorInvestmentAccount: investmentAccount)
                    StockTradeJourneyFactory.transitJourney(viewController: self.viewControllable, intent: .navigateToOrderStatusJourney, config: config)
                    if let instruction = instruction {
                        if instruction == .modifyBuy || instruction == .cancelBuy {
                            self.tracker.trackEvent(.orderStatusClickCheckOrderStatus(defaultParameters: self.tracker.defaultParameters))
                        } else if instruction == .buy {
                            self.tracker.trackEvent(.buyConfirmCheckClick(defaultParameters: self.tracker.defaultParameters))
                        }
                    }
                }).disposed(by: self.bag)
            }).disposed(by: bag)
    }
}

extension BuyOrderConfirmationSecondaryButton: StockBuyView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        _instructionType.accept(instruction)
    }

    func updateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        _investmentAccount.accept(account)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        _market.accept(market)
    }
}
