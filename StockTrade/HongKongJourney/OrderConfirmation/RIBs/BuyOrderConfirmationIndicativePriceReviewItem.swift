/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationIndicativePriceReviewItem: WXInfoButtonReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    var _price: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    var _priceCurrency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)
    var _decimalPlaces: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    var _indicativePriceUpdateTime: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    var _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    var _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_indicative_price"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        stockBuyPresenter?.viewDeinit(view: self)
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        BehaviorRelay.combineLatest(
            _price.distinctUntilChanged(),
            _indicativePriceUpdateTime.flatMap(ignoreNil).distinctUntilChanged(),
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (price, date, market) -> NSAttributedString in
            let timeFormatter = GregorianDateFormatter()
            timeFormatter.dateFormat = "HH:mm"

            let dateFormatter = GregorianDateFormatter()
            dateFormatter.timeZone = market.timeZone

            // Use NSMutableAttributedString to handle line spacing
            let dateFormat = localizedString("quote_date_formatter")

            let string = "\(String(format: localizedString("alt_market_as"), date.stringWithDateFormat(dateFormat, timeZone: market.timeZone))) (\(market.timeZoneAbbreviation))"

            let attributedString = NSMutableAttributedString(string: string)
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineSpacing = 5
            paragraphStyle.alignment = .right
            attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSMakeRange(0, attributedString.length))
            return (price == nil) ? NSAttributedString(string: "") : attributedString
        }.asObservable().subscribe(onNext: { [weak self] subtitle in
            guard let self = self else { return }
            self.viewModel?.inputs.updateSubtitle(value: subtitle)
        }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _price.flatMap(ignoreNil).distinctUntilChanged(),
            _priceCurrency.flatMap(ignoreNil).distinctUntilChanged(),
            _decimalPlaces.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (price, currency, decimalPlaces) -> String in
            if let priceString = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency) {
                return priceString
            } else {
                return localizedString("default_item_value")
            }
        }.asObservable().subscribe(onNext: { [weak self] indicativePrice in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: indicativePrice)
        }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _market.flatMap(ignoreNil).distinctUntilChanged(),
            _orderType.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (market, orderType) -> String? in
            switch orderType {
            case .market:
                return localizedString("indicative_price_content_hk_market_market_order")
            case .atAuction:
                return localizedString("indicative_price_content_hk_market_atauction_order")
            case .limit, .atAuctionLimit:
                return localizedString("indicative_price_content_hk_market_market_order")
            }
        }.asObservable().subscribe(onNext: { [weak self] body in
            guard let self = self else { return }
            if let body = body {
                self.viewModel?.inputs.updateInfoScreenData(value: .init(body: body, buttonViewModel: nil))
                self.viewModel?.inputs.updateInfoScreenTitle(value: localizedString("indicative_price_title"))
                self.viewModel?.inputs.updateModuleVisibility(visible: true)
            } else {
                self.viewModel?.inputs.updateModuleVisibility(visible: false)
            }
        }).disposed(by: bag)
    }

    func didUpdatePrice(value: Decimal?) {
        _price.accept(value)
    }

    func didUpdatePriceCurrency(value: WXCurrencyCode?) {
        _priceCurrency.accept(value)
    }

    func didUpdateDecimalPlaces(value: Int?) {
        _decimalPlaces.accept(value)
    }

    func didUpdateIndicativePriceUpdateTime(value: Date?) {
        _indicativePriceUpdateTime.accept(value)
    }

    func didUpdateMarket(value: WXMarket?) {
        _market.accept(value)
    }

    func didUpdateOrderType(type: HSBCStockTradeOrderType?) {
        _orderType.accept(type)
    }

    func didUpdateBuyPrice(amount: Decimal, currency: WXCurrencyCode) {
        didUpdatePrice(value: amount)
        didUpdatePriceCurrency(value: currency)
    }
}

extension BuyOrderConfirmationIndicativePriceReviewItem: StockBuyView {
    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateOrderType(type: type)
        // WXMA-6271 For `.market` and `.atAuction`
        // We show indictive price instead of ordinary price
        switch type {
        case .market, .atAuction:
            viewModel?.inputs.updateModuleVisibility(visible: true)
        default:
            viewModel?.inputs.updateModuleVisibility(visible: false)
        }
    }

    func updatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdateBuyPrice(amount: price, currency: currency)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        didUpdateMarket(value: market)
    }
}

extension BuyOrderConfirmationIndicativePriceReviewItem: StockQuoteView {
    func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        guard let time = quote.exchangeUpdatedTime else { return }
        didUpdateIndicativePriceUpdateTime(value: time)
    }

    func updateDecimalPlacesSection(decimalPlaces: Int, error: HSBCErrorCode) {
        didUpdateDecimalPlaces(value: decimalPlaces)
    }
}
