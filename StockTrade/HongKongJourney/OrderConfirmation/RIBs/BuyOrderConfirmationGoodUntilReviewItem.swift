/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationGoodUntilReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    let _goodUntil: BehaviorRelay<Date?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        // Good until
        reviewItem.titleLabel.accessibilityIdentifier = "textview_good_until_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_good_until_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("good_until_title")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        BehaviorRelay.combineLatest(
            _goodUntil.flatMap(ignoreNil).distinctUntilChanged(),
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).map { date, market -> (String, String) in
            let dateFormat = localizedString("date_format_good_until")
            let altText = date.stringWithDateFormat(dateFormat, timeZone: market.timeZone)
            return ("\(altText)\n \(market.timeZoneAbbreviation)", altText)
        }.asObservable().subscribe(onNext: { [weak self] value, altText in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: value)
        }).disposed(by: bag)
    }
}

extension BuyOrderConfirmationGoodUntilReviewItem: StockBuyView {
    func updateGoodUntilSection(date: Date?, error: HSBCErrorCode) {
        _goodUntil.accept(date)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        _market.accept(market)
    }
}
