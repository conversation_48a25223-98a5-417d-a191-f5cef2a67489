/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift

class SellOrderConfirmationNavigationBar: WXNavigationBarWithImagebaseLeftRightButton {
    weak var stockSellPresenter: StockSellPresenterImplementation!
    var _duplicatedRequestIndicator: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    var _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    var _investmentAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)

    let _orderReferenceNumber: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _stockCode: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _stockName: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)
    let _price: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    let _quantity: BehaviorRelay<Int64?> = BehaviorRelay(value: nil)
    let _tradeAmount: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    let _tradeAmountCurrency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)

    override func initializeVariables() {
        super.initializeVariables()

        stockSellPresenter = StockSellPresenterImplementation.configure(view: self, routerImplementation: nil)
        leftButtonImage = ThemeManager.shared.currentTheme.systemIcons.close
        leftButtonImageContentDescription = "button_toolbar_close"
        titleLabelContentDescription = "text_toolbar_title"
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockSellPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        removeFromSuperview()
        stockSellPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateTitle(localizedString("stock_confirmation_title"))
        viewModel?.outputs.didClickLeftButton.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            if let instruction = self._instruction.value,
                let market = self._market.value,
                let investmentAccount = self._investmentAccount.value,
                let viewControllable = self.viewControllable {
                self.removeFromSuperview()
                if [.cancelSell, .modifySell].contains(instruction) {
                    let config = StockTradeJourneyFactory.generateOrderStatusConfig(market, priorInvestmentAccount: investmentAccount)
                    StockTradeJourneyFactory.transitJourney(viewController: viewControllable, intent: .navigateToOrderStatusJourney, config: config, animated: true)
                } else {
                    // finish by journey
                    StockTradeJourneyFactory.finishSellJourney()
                }
            }
        }).disposed(by: bag)
    }

    override func setupTealiumTaggings() {
        super.setupTealiumTaggings()

        let referenceNumberAndStockCodeAndStockName = BehaviorRelay.combineLatest(_orderReferenceNumber.distinctUntilChanged().flatMap(ignoreNil), _stockCode.distinctUntilChanged().flatMap(ignoreNil), _stockName.distinctUntilChanged().flatMap(ignoreNil)) { ($0, $1, $2) }

        let marketAndOrderTypeAndTradeAmount = BehaviorRelay.combineLatest(_market.distinctUntilChanged().flatMap(ignoreNil), _orderType.distinctUntilChanged().flatMap(ignoreNil), _tradeAmount.distinctUntilChanged()) { ($0, $1, $2) }

        BehaviorRelay.combineLatest(
            _instruction.distinctUntilChanged().flatMap(ignoreNil),
            referenceNumberAndStockCodeAndStockName,
            marketAndOrderTypeAndTradeAmount,
            _quantity.distinctUntilChanged().flatMap(ignoreNil),
            _price.distinctUntilChanged().flatMap(ignoreNil),
            _tradeAmountCurrency.distinctUntilChanged().flatMap(ignoreNil)
        ).take(1)
            .subscribe(onNext: { [weak self] instruction, referenceNumberAndStockCodeAndStockName, marketAndOrderTypeAndTradeAmount, quantity, price, tradeAmountCurrency in
                //  modify & cancel screen 's view page load tagging is independent
                guard let self = self, [.cancelSell, .modifySell].contains(instruction) else { return }
                let referenceNumber = "\(referenceNumberAndStockCodeAndStockName.0)"
                let stockName = "\(referenceNumberAndStockCodeAndStockName.2)"
                let orderType = "\(marketAndOrderTypeAndTradeAmount.1)"
                self.tracker.trackPage(EventInfo.orderStatusSellConfirmationPage(referenceNumber: referenceNumber, stockName: stockName, price: "\(String(describing: price))", quantity: "\(String(describing: quantity))", orderType: orderType, orderInstruction: instruction.rawValue, defaultParameters: self.tracker.defaultParameters))
            })
            .disposed(by: bag)

        BehaviorRelay.combineLatest(
            _duplicatedRequestIndicator.flatMap(ignoreNil).distinctUntilChanged(),
            _instruction.distinctUntilChanged().flatMap(ignoreNil),
            referenceNumberAndStockCodeAndStockName,
            marketAndOrderTypeAndTradeAmount,
            _quantity.distinctUntilChanged().flatMap(ignoreNil),
            _price.distinctUntilChanged().flatMap(ignoreNil),
            _tradeAmountCurrency.distinctUntilChanged().flatMap(ignoreNil)
        ).take(1)
            .subscribe(onNext: { [weak self] duplicatedRequestIndicator, instruction, referenceNumberAndStockCodeAndStockName, marketAndOrderTypeAndTradeAmount, quantity, price, tradeAmountCurrency in
                // split tagging logic because _duplicatedRequestIndicator is nil when modify & cancel screen
                guard let self = self, instruction == .sell else { return }
                let referenceNumber = "\(referenceNumberAndStockCodeAndStockName.0)"
                let stockCode = "\(referenceNumberAndStockCodeAndStockName.1)"
                let market = "\(marketAndOrderTypeAndTradeAmount.0)"
                let orderType = "\(marketAndOrderTypeAndTradeAmount.1)"
                let currency = tradeAmountCurrency.rawValue
                let duplicatedRequest = duplicatedRequestIndicator ?"duplicate order" : "non duplicate order"

                self.tracker.track(
                    "HKSell-23",
                    [
                        .applicationDecision: referenceNumber,
                        .formField1: "stock code: \(stockCode)",
                        .formField2: "market: \(market)",
                        .formField3: "order type: \(orderType)",
                        .formField4: "quantity: \(quantity)",
                        .formField5: "estimated total: \(marketAndOrderTypeAndTradeAmount.2 == nil ? "" : "\(marketAndOrderTypeAndTradeAmount.2!)")",
                        .amount: "price: \(price)",
                        .currencyCode: currency,
                        .eventContent: duplicatedRequest,
                        .pageSecurityLevel: "30"
                    ]
                )
                if duplicatedRequestIndicator {
                    self.tracker.appDynamicTrackEventWithoutArguments("Duplicate order submission - HTTP 200 - Confirmed duplicate order - Confirmation", String(describing: type(of: self)))
                }
            })
            .disposed(by: bag)
    }
}

extension SellOrderConfirmationNavigationBar: StockSellView {
    public func updateDuplicatedRequestIndicatorSection(duplicatedRequestIndicator: Bool, warnings: [HSBCWarning]?) {
        _duplicatedRequestIndicator.accept(duplicatedRequestIndicator)
    }

    public func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        _instruction.accept(instruction)
    }

    public func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        _market.accept(market)
    }

    func updateInvestmentAccountSection(account: WXInvestmentAccount, tradableQuantity: Int64, error: HSBCErrorCode) {
        _investmentAccount.accept(account)
    }

    func updateReferenceNumberSection(number: String, error: HSBCErrorCode) {
        _orderReferenceNumber.accept(number)
    }

    func updateStockCodeSection(code: String, error: HSBCErrorCode) {
        _stockCode.accept(code)
    }

    func updateStockNameSection(name: String, error: HSBCErrorCode) {
        _stockName.accept(name)
    }

    func updatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        _price.accept(price)
    }

    func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        _quantity.accept(quantity)
    }

    func updateCaculatedEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode, error: HSBCErrorCode) {
        _tradeAmount.accept(total?.value)
        _tradeAmountCurrency.accept(currency)
    }

    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        _orderType.accept(type)
    }
}
