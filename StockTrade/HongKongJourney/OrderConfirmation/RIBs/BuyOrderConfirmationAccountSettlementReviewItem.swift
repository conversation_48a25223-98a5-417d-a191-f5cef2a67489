/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationAccountSettlementReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_pay_from"
        // Pay from
        reviewItem.titleLabel.accessibilityIdentifier = "textview_pay_from_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_pay_from_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultTitle = localizedString("stock_review_pay_from")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateUpdateSettlementAccountSection(settlementAccount: WXSettlementAccount?) {
        if let account = settlementAccount {
            if account.isHongKongDefaultAccount() {
                viewModel?.inputs.updateValue(value: localizedString("account_selection_default_pay_from_account"))
            } else {
                viewModel?.inputs.updateValue(value: """
                \(LocalizationUtil.localizedAccountTypeName(type: account.type, currency: account.currency))\n\(account.number)
                """)
            }
        }
    }
}

extension BuyOrderConfirmationAccountSettlementReviewItem: StockBuyView {
    func updateSettlementAccountSection(account: WXSettlementAccount?, error: HSBCErrorCode) {
        didUpdateUpdateSettlementAccountSection(settlementAccount: account)
    }
}
