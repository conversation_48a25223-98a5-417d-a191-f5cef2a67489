/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationIconTextView: WXIconTextView {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        title = localizedString("stock_confirmation_messagescreen_title")
        body = localizedString("stock_confirmation_messagescreen_text")
    }

    override func setupViews() {
        super.setupViews()
        NSLayoutConstraint.activate([
            iconTextView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 0),
            iconTextView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: 0),
            iconTextView.topAnchor.constraint(equalTo: topAnchor, constant: 32),
            iconTextView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -32)
        ])
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }
}

extension BuyOrderConfirmationIconTextView: StockBuyView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        switch instruction {
        case .buy, .modifyBuy:
            viewModel.inputs.updateTitle(value: localizedString("stock_confirmation_messagescreen_title"))
            viewModel.inputs.updateValue(value: localizedString("stock_confirmation_messagescreen_text"))
        case .cancelBuy:
            viewModel.inputs.updateTitle(value: localizedString("order_cancle_confirm_caption"))
            viewModel.inputs.updateValue(value: localizedString("order_cancle_confirm_desc"))
        default:
            break
        }
    }
}
