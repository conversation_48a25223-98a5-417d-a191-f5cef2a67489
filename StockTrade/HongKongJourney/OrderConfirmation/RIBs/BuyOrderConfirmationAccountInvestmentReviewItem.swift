/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationAccountInvestmentReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_hold_securities_in"
        // Hold securities in
        reviewItem.titleLabel.accessibilityIdentifier = "textview_hold_securites_in_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_hold_securites_in_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_hold_securities_in")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateInvestmentAccount(investmentAccount: WXInvestmentAccount?) {
        if let account = investmentAccount {
            viewModel?.inputs.updateValue(value: """
            \(LocalizationUtil.localizedAccountTypeName(type: account.type, currency: account.currency))
            \(account.number)
            """)
        }
    }
}

extension BuyOrderConfirmationAccountInvestmentReviewItem: StockBuyView {
    func updateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        didUpdateInvestmentAccount(investmentAccount: account)
    }
}
