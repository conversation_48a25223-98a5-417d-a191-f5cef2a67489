/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationStockNameReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_stock_name"
        // Stock name
        reviewItem.titleLabel.accessibilityIdentifier = "textview_stock_name_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_stock_name_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_name")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateStockNameSection(name: String) {
        viewModel.inputs.updateValue(value: name)
    }
}

extension BuyOrderConfirmationStockNameReviewItem: StockBuyView {
    func updateStockNameSection(name: String, error: HSBCErrorCode) {
        didUpdateStockNameSection(name: name)
    }
}
