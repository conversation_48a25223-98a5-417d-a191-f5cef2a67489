/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationStockCodeReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _stockCode: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_stock_code"
        // Stock code
        reviewItem.titleLabel.accessibilityIdentifier = "textview_stock_code_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_stock_code_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_code")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        BehaviorRelay.combineLatest(
            _stockCode.flatMap(ignoreNil).distinctUntilChanged(),
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (code, market) -> String in
            switch market {
            case .hongKong:
                return "\(code) (HK)"
            case .china, .shangHai, .shenZhen:
                return "\(code) (CN)"
            case .america:
                return "\(code) (US)"
            default:
                return "\(code)"
            }
        }.asObservable().subscribe(onNext: { [weak self] value in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: value)
        }).disposed(by: bag)
    }
}

extension BuyOrderConfirmationStockCodeReviewItem: StockBuyView {
    func updateStockCodeSection(code: String, error: HSBCErrorCode) {
        _stockCode.accept(code)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        _market.accept(market)
    }
}
