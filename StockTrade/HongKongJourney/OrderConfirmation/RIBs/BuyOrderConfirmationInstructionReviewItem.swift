/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderConfirmationInstructionReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_instruction"
        // Instruction
        reviewItem.titleLabel.accessibilityIdentifier = "textview_instruction_title"
        reviewItem.valueLabel.accessibilityIdentifier = "textview_instruction_value"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_instruction")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }
}

extension BuyOrderConfirmationInstructionReviewItem: StockBuyView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        viewModel?.inputs.updateValue(value: LocalizationUtil.instructionDescription(type: instruction))
    }
}
