/** @RIB */
//
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
//

import MobileDesign
import UIKit

class ErrorMappingCollectionViewHongKong: WXCollectionView {
    var stringArray: [String] = []
//    var groups: [String: [SpecificErrorCode]] = [:]
    var groupsAPIError: [String: [HSBCErrorCode]] = [:]
//    var groupsOtherError: [String: [SpecificErrorCode]] = [:]
    var data: [WXSection]?

    override func initializeVariables() {
        super.initializeVariables()
        defaultCellWidth = 100
//        let errorCodes = ["CHL015", "LHS0055", "LHS0074", "LHS0075", "LHS0076", "LHS0085", "LHS0124", "LHS0259", "LRS0001", "LRS0002", "LRS0003", "LRS0004", "LRS0005", "LRS0006", "LRS0007", "LRS0008", "LRS0067", "LRS0123", "LRS0130", "LRS0132", "LRS0210", "LRS0211", "LRS0274", "LRS0275", "LRS0276", "LRS0277", "LRS0278", "LRS0310", "LRS0311", "LRS3298", "LRS3313", "LRS3330", "MKDEMIDC04", "MKDEPS01", "ORM386", "ORM497", "ORM505", "ORM506", "ORM510", "ORM523", "ORM524", "ORM526", "ORM527", "ORM529", "SPR201", "TMT004", "TMT005", "TRM204", "UHS0043", "UHS0072", "UHS0152", "UHS0207", "UHS0222", "UHS0249", "UHS0263", "UHS0392", "UHS0685", "UHS1234", "UHS1339", "UHS2801", "UHS3002", "UHS6105", "UHS6277", "UHS6501", "UHS6505", "UHS6623", "UHS7053", "UHS7235", "UHS8107", "URS0001", "URS0002", "URS0003", "URS0004", "URS0008", "URS0011", "URS0012", "URS0013", "URS0014", "URS0015", "URS0016", "URS0019", "URS0020", "URS0022", "URS0024", "URS0025", "URS0030", "URS0034", "URS0042", "URS0049", "URS0071", "URS0072", "URS0076", "URS0078", "URS0080", "URS0086", "URS0095", "URS0096", "URS0105", "URS0106", "URS0121", "URS0127", "URS0131", "URS0133", "URS0137", "URS0170", "URS0180", "URS0192", "URS0194", "URS0195", "URS0201", "URS0204", "URS0205", "URS0212", "URS0222", "URS0224", "URS0227", "URS0233", "URS0234", "URS0236", "URS0240", "URS0250", "URS0256", "URS0259", "URS0265", "URS0266", "URS0273", "URS0274", "URS0275", "URS0276", "URS0277", "URS0278", "URS0279", "URS0280", "URS0287", "URS0288", "URS0289", "URS0292", "URS0298", "URS0299", "URS0300", "URS0324", "URS0326", "URS0327", "URS0329", "URS0340", "URS0342", "URS0343", "URS0347", "URS0351", "URS0366", "URS0372", "URS0401", "URS0403", "URS0406", "URS0407", "URS0408", "URS0410", "URS0412", "URS0413", "URS0414", "URS0415", "URS0416", "URS0417", "URS0422", "URS0423", "URS0424", "URS0429", "URS0482", "URS0513", "URS0514", "URS0515", "URS0516", "URS0517", "URS0518", "URS0525", "URS0526", "URS0527", "URS0528", "URS0529", "URS0535", "URS0544", "URS0546", "URS0554", "URS0555", "URS0561", "URS0562", "URS0563", "URS0564", "URS0566", "URS0567", "URS0568", "URS0569", "URS0570", "URS0580", "URS0581", "URS0582", "URS0597", "URS0602", "URS0603", "URS0604", "URS0610", "URS3006", "URS3094", "URS3132", "URS3421", "ORM530", "URS0487",]
//
//        errorCodes.compactMap { errorCode -> SpecificErrorCode? in
//            SpecificErrorCode(rawValue: errorCode)
//        }.forEach {
//            guard let firstLetter = $0.rawValue.first else {
//                groups["#"] = (groups["#"] ?? []) + [$0]
//                return
//            }
//            let firstLetterStr = String(firstLetter)
//            groups[firstLetterStr] = (groups[firstLetterStr] ?? []) + [$0]
//        }

        groupsAPIError["API Error"] = HSBCErrorCode.allCases.filter { $0.rawValue >= HSBCErrorCode.internalServerError.rawValue }
//        groupsOtherError["Other Error"] = SpecificErrorCode.allCases
    }

    override func bindViewModel() {
        super.bindViewModel()

//        let groupA = groups.sorted { $0.key < $1.key }.compactMap { WXSection(title: $0.key, row: $0.value.compactMap { WXSectionRow(title: $0.rawValue, status: "", statusColor: ThemeManager.shared.currentTheme.colors.textPrimary.cgColor, detailTitle: "", detailValue: "", subDetailTitle: "", subDetailValue: "", meta: HSBCWarning(code: $0.rawValue, message: [], correlationId: "") as AnyObject) }) }

        let groupB = groupsAPIError.compactMap { WXSection(title: $0.key, row: $0.value.compactMap {
            WXSectionRow(title: "\($0.rawValue) (\($0))", status: "", statusColor: ThemeManager.shared.currentTheme.colors.textPrimary.cgColor, detailTitle: "", detailValue: "", subDetailTitle: "", subDetailValue: "", meta: HSBCErrorCode(rawValue: $0.rawValue) as AnyObject)
        }) }

//        let groupC = groupsOtherError.compactMap { WXSection(title: $0.key, row: $0.value.compactMap { WXSectionRow(title: $0.rawValue, status: "", statusColor: ThemeManager.shared.currentTheme.colors.textPrimary.cgColor, detailTitle: "", detailValue: "", subDetailTitle: "", subDetailValue: "", meta: HSBCWarning(code: $0.rawValue, message: [], correlationId: "") as AnyObject) }) }
        data = groupB
//        data = groupA + groupB + groupC
        viewModel?.inputs.updateData(data)
    }
}
