/** @View */
//
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
//

import MobileDesign
import UIKit

class ErrorMappingViewControllerHongKong: UIViewController {
    override var preferredStatusBarStyle: UIStatusBarStyle { .default }

    var views: [WXBaseView] = []

    private lazy var backButton: UIButton = {
        let backButton = UIButton(type: .custom)
        backButton.translatesAutoresizingMaskIntoConstraints = false
        backButton.setImage(ThemeManager.shared.currentTheme.systemIcons.chevronLeft, for: .normal)
        backButton.frame = CGRect(x: 0, y: 0, width: 30, height: 30)
        backButton.accessibilityLabel = localizedString("alt_back")
        backButton.addTarget(self, action: #selector(onBackTapped), for: .touchUpInside)
        return backButton
    }()

    var collectionView: ErrorMappingCollectionViewHongKong!
    var errorMessage: BuyStockOrderDuplicatedOrderFullScreenErrorMessage!

    init(tracker: WXTracking?) {
        super.init(nibName: nil, bundle: nil)
        StockTradeJourneyFactory.trackManager = tracker
        StockTradeJourneyFactory.locale = .English
        errorMessage = BuyStockOrderDuplicatedOrderFullScreenErrorMessage()
        errorMessage.setListener(self)
        collectionView = ErrorMappingCollectionViewHongKong()
        collectionView.setListener(self)
        views = [
            errorMessage as WXBaseView,
            collectionView as WXBaseView
        ]

        for view in views {
            view.viewDidLoad()
        }
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupNavigationBar()
        view.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        view.addSubview(collectionView)

        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    func setupNavigationBar() {
        // Added to add accessibilityID
        let titleLabel = UILabel()
        titleLabel.text = "Error mapping"
        titleLabel.font = ThemeManager.shared.currentTheme.fonts.bodyEmphasized
        titleLabel.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
        titleLabel.accessibilityIdentifier = "text_toolbar_title"
        navigationItem.titleView = titleLabel

        // Back button
        navigationItem.leftBarButtonItem = UIBarButtonItem(customView: backButton)

        backButton.setNeedsFocusUpdate()
    }

    deinit {
        for view in views {
            view.viewDeinit()
        }
    }

    func showViewController(viewController: UIViewController) {
        modalPresentationStyle = .overFullScreen
        present(viewController, animated: true, completion: nil)
    }

    @objc func onBackTapped() {
        navigationController?.popViewController(animated: true)
    }
}

extension ErrorMappingViewControllerHongKong: WXFullScreenErrorMessageDelegate {
    func showFullScreenErrorMessage(_ module: WXFullScreenErrorMessage, _ viewController: UIViewController?) {
        if isVisible(), let viewController = viewController {
            DispatchQueue.main.async { UIApplication.topViewController()?.present(viewController, animated: true)
            }
        }
    }

    func hideFullScreenErrorMessage(_ module: WXFullScreenErrorMessage, _ viewController: UIViewController?) {
        viewController?.dismiss(animated: true, completion: nil)
    }
}

extension ErrorMappingViewControllerHongKong: WXCollectionViewDelegate {
    func showReviewItemModule(_ module: WXCollectionView) {}

    func hideReviewItemModule(_ module: WXCollectionView) {}

    func didSelectIitem(_ value: AnyObject?) {
        errorMessage.showErrorMessage(.PRESENTER_REQUEST_POST_BUY_ORDER, errorCode: .internalServerError, warnings: [])
    }
}
