/** @UXComponent */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

protocol WXFullScreenErrorMessageViewModelInputs {
    func viewDidLoad(_ void: Void?)
    func viewDeinit(_ void: Void?)
    func updateModuleVisibility(_ visible: Bool)
    func didClickDismissButton(_ void: Void)
    func didDismiss(_ void: Void?)
}

protocol WXFullScreenErrorMessageViewModelOutputs {
    var viewDidLoad: Driver<Void> { get }
    var viewDeinit: Driver<Void> { get }
    var moduleVisibility: Driver<Bool> { get }
    var didClickDismissButton: Driver<Void> { get }
    var didDismissCompleted: Driver<Void> { get }
}

open class WXFullScreenErrorMessageViewModel: NSObject, ViewModelType, WXFullScreenErrorMessageViewModelOutputs, WXFullScreenErrorMessageViewModelInputs {
    var inputs: WXFullScreenErrorMessageViewModelInputs { self }

    var outputs: WXFullScreenErrorMessageViewModelOutputs { self }

    public var _moduleVisibility: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    public var _didClickDismissButton: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    var _viewDidLoad: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    var _viewDeinit: BehaviorRelay<Void?> = BehaviorRelay(value: nil)

    var viewDidLoad: Driver<Void> { _viewDidLoad.asDriver().flatMap(ignoreNil) }
    var viewDeinit: Driver<Void> { _viewDeinit.asDriver().flatMap(ignoreNil) }
    public var moduleVisibility: Driver<Bool> { _moduleVisibility.asDriver().flatMap(ignoreNil).distinctUntilChanged() }

    public var didClickDismissButton: Driver<Void> { _didClickDismissButton.asDriver().flatMap(ignoreNil) }

    public var _didDismissCompleted: BehaviorRelay<Void?> = BehaviorRelay(value: nil)
    public var didDismissCompleted: Driver<Void> { _didDismissCompleted.asDriver().flatMap(ignoreNil) }

    func viewDidLoad(_ void: Void?) {
        _viewDidLoad.accept(())
    }

    func viewDeinit(_ void: Void?) {
        _viewDeinit.accept(())
    }

    func updateModuleVisibility(_ visible: Bool) {
        _moduleVisibility.accept(visible)
    }

    func didClickDismissButton(_ void: Void) {
        _didClickDismissButton.accept(void)
    }

    func didDismiss(_ void: Void?) {
        _didDismissCompleted.accept(void)
    }
}

protocol WXFullScreenErrorMessageDelegate: AnyObject {
    func showFullScreenErrorMessage(_ module: WXFullScreenErrorMessage, _ viewController: UIViewController?)
    func hideFullScreenErrorMessage(_ module: WXFullScreenErrorMessage, _ viewController: UIViewController?)
}

open class WXFullScreenErrorMessage: NSObject, WXBaseView {
    public var viewModel: WXFullScreenErrorMessageViewModel!

    public let bag = DisposeBag()

    public var header: String = ""
    public var body: String = ""

    var defaultIconAccessibilityLabel: String = "defaultIconAccessibilityLabel"
    var dismissButtonTitle: String = ""

    var messagesViewController: MessageScreenViewController?
    weak var listener: WXFullScreenErrorMessageDelegate?
    var completionHandler: (() -> Void)?

    func setListener(_ listener: WXFullScreenErrorMessageDelegate) {
        self.listener = listener
    }

    public func initializeVariables() {
        header = ""
        body = ""
        dismissButtonTitle = "_dismiss"
    }

    open func setupTealiumTaggings() {
        /* Empty Implementation */
    }

    public func viewDidAppear() {
        /* Empty Implementation */
    }

    public func viewWillDisappear() {
        /* Empty Implementation */
    }

    public func viewDidLoad() {
        initializeVariables()
        setupViews()
        initViewModel()
        bindViewModel()
        viewModel.inputs.viewDidLoad(())
        setupAccessibilityIndentifiers()
        setupTealiumTaggings()
    }

    public func setupViews() {}

    public func setupAccessibilityIndentifiers() {
        /* Empty Implementation */
    }

    public func viewDeinit() {
        viewModel?.inputs.viewDeinit(())
    }

    public func initViewModel() {
        viewModel = WXFullScreenErrorMessageViewModel()
    }

    public func bindViewModel() {
        completionHandler = { [weak self] in
            guard let self = self else { return }
            self.viewModel?.inputs.didClickDismissButton(())
        }

        viewModel.outputs.moduleVisibility.asObservable().subscribe(onNext: { [weak self] moduleVisibility in
            guard let self = self else { return }
            if moduleVisibility {
                let secondaryButton = ButtonViewModel(title: AccessibleValue(self.dismissButtonTitle, accessibilityText: self.dismissButtonTitle, accessibilityValue: self.dismissButtonTitle))
                let viewData = MessageScreenViewController.ViewModel(title: self.header, bodyText: self.body, iconProvider: { $0.systemIcons.warningOnLight }, secondaryButton: secondaryButton)
                self.messagesViewController = MessageScreenViewController(viewModel: viewData)
                secondaryButton.action = self.completionHandler

                self.messagesViewController?.modalPresentationStyle = .fullScreen

                self.listener?.showFullScreenErrorMessage(self, self.messagesViewController)

            } else {
                self.listener?.hideFullScreenErrorMessage(self, self.messagesViewController)
            }
        }).disposed(by: bag)
    }

    public func dismiss() {
        messagesViewController?.dismiss(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.viewModel?.didDismiss(())
        })
    }
}
