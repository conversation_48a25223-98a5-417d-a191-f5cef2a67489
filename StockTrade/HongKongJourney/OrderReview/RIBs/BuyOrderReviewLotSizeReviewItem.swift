/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewLotSizeReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    let _isMDSDown: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)
    let _lotSize: BehaviorRelay<Int?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_lot_size"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_lot_size")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        BehaviorRelay.combineLatest(
            _isMDSDown.flatMap(ignoreNil).distinctUntilChanged(),
            _lotSize.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (isMDSDown, lotSize) -> String in
            isMDSDown ? localizedString("default_item_value") : String(format: localizedString("number_of_shares"), "\(lotSize)")
        }.asObservable().subscribe(onNext: { [weak self] value in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: value)
        }).disposed(by: bag)
    }

    func didUpdateIsMDSDownSection(isMDSDown: Bool) {
        _isMDSDown.accept(isMDSDown)
    }

    func didUpdateLotSizeSection(size: Int) {
        _lotSize.accept(size)
    }
}

extension BuyOrderReviewLotSizeReviewItem: StockQuoteView, StockBuyView {
    func updateIsMDSDownSection(isMDSDown: Bool, error: HSBCErrorCode) {
        didUpdateIsMDSDownSection(isMDSDown: isMDSDown)
    }

    func updateLotSizeSection(size: Int, error: HSBCErrorCode) {
        didUpdateLotSizeSection(size: size)
    }
}
