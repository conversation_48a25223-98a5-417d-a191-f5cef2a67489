/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileCore
import RxCocoa
import RxSwift
import UIKit

class BuyStockOrderDuplicatedOrderFullScreenErrorMessage: WXFullScreenErrorMessage, StockBuyView, WXTrackable, WXViewControllable {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    weak var viewControllable: UIViewController?

    // WXFullScreenErrorMessageProtocol
    var _errorCode: BehaviorRelay<HSBCErrorCode?> = BehaviorRelay(value: nil)
    var _presenterRequestType: BehaviorRelay<PresenterRequestType?> = BehaviorRelay(value: nil)
    var _warnings: BehaviorRelay<[HSBCWarning]?> = BehaviorRelay(value: nil)
    var _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    var _investmentAccount: BehaviorRelay<WXInvestmentAccount?> = BehaviorRelay(value: nil)

    func setViewControllable(_ viewControllable: UIViewController) {
        self.viewControllable = viewControllable
    }

    override func initializeVariables() {
        super.initializeVariables()
        header = localizedString("duplicated_order_error_message_header")
        body = localizedString("duplicated_order_error_message_body")
        dismissButtonTitle = localizedString("duplicated_order_error_message_cta")

        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?
            .outputs
            .didClickDismissButton
            .asObservable()
            .subscribe(onNext: { [weak self] _ in
                guard let self = self else { return }
                self.viewModel?.inputs.updateModuleVisibility(false)
            }).disposed(by: bag)

        viewModel?
            .outputs
            .moduleVisibility
            .asObservable()
            .subscribe(onNext: { [weak self] moduleVisibility in
                guard let self = self, moduleVisibility == false, let market = self._market.value, let investmentAccount = self._investmentAccount.value, let viewControllable = self.viewControllable else { return }
                // 1. dimiss error message screen with animation
                // 2. go to order status
                /**
                 // feature/WXMA-10743
                 // animated: optiona for StockOrderDuplicatedOrderFullScreenErrorMessage, dismiss error message with animtion, therfore animation should not perfrom from transitJourney
                 */
                DispatchQueue.main.async {
                    let config = StockTradeJourneyFactory.generateOrderStatusConfig(
                        market,
                        priorInvestmentAccount: investmentAccount
                    )

                    // 3. transit to order status screen without animation
                    StockTradeJourneyFactory.transitJourney(
                        viewController: viewControllable,
                        intent: .navigateToOrderStatusJourney,
                        config: config,
                        animated: false // !!!!!! no animation
                    )
                }
            }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _presenterRequestType.flatMap(ignoreNil).distinctUntilChanged(),
            _errorCode.flatMap(ignoreNil).distinctUntilChanged(),
            _warnings.flatMap(ignoreNil).distinctUntilChanged()
        ).asObservable().subscribe(onNext: { [weak self] type, errorCode, warnings in
            guard let self = self else { return }
            if [
                PresenterRequestType.PRESENTER_REQUEST_POST_BUY_ORDER,
                PresenterRequestType.PRESENTER_REQUEST_POST_SELL_ORDER
            ].contains(type), errorCode.rawValue >= HSBCErrorCode.internalServerError.rawValue {
                self.viewModel.inputs.updateModuleVisibility(true)
            }

        }).disposed(by: bag)
    }

    override func setupTealiumTaggings() {
        BehaviorRelay.combineLatest(
            _presenterRequestType.flatMap(ignoreNil).distinctUntilChanged(),
            _errorCode.flatMap(ignoreNil).distinctUntilChanged(),
            _warnings.flatMap(ignoreNil).distinctUntilChanged()
        ).asObservable().subscribe(onNext: { [weak self] presenterRequestType, errorCode, warnings in
            guard let self = self else { return }
            if [
                PresenterRequestType.PRESENTER_REQUEST_POST_BUY_ORDER,
                PresenterRequestType.PRESENTER_REQUEST_POST_SELL_ORDER
            ].contains(presenterRequestType), errorCode.rawValue >= HSBCErrorCode.internalServerError.rawValue {
                self.tracker.trackEvent(EventInfo.buyReviewClickConfirmHitHttpCode(errorCode: String(errorCode.rawValue), eventContent: "http 500 error", defaultParameters: self.tracker.defaultParameters))
                self.tracker.appDynamicTrackEventWithoutArguments("Duplicate order submission - HTTP" + String(errorCode.rawValue) + " Suspected duplicate order - Confirmation", String(describing: type(of: self)))
            }
        }).disposed(by: bag)
    }

    func didReciveErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]) {
        _warnings.accept(warnings)
        _errorCode.accept(errorCode)
        _presenterRequestType.accept(type)
    }

    // Presenter mehtod
    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        // WXFullScreenErrorMessageProtocol
        didReciveErrorMessage(type, errorCode: errorCode, warnings: warnings ?? [])
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        _market.accept(market)
    }

    func updateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        _investmentAccount.accept(account)
    }
}
