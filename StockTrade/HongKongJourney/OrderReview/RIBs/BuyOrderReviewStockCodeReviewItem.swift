/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewStockCodeReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _stockCode: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_stock_code"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_stock_code")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        BehaviorRelay.combineLatest(
            _stockCode.flatMap(ignoreNil).distinctUntilChanged(),
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).map { String(format: "%@ (%@)", $0.0, LocalizationUtil.localizedMarketShortName(market: $0.1))
        }.asObservable().subscribe(onNext: { [weak self] value in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: value)
        }).disposed(by: bag)
    }

    func didUpdateStockCodeSection(code: String) {
        _stockCode.accept(code)
    }

    func didUpdateStockMarketSection(market: WXMarket) {
        _market.accept(market)
    }
}

extension BuyOrderReviewStockCodeReviewItem: StockBuyView {
    func updateStockCodeSection(code: String, error: HSBCErrorCode) {
        didUpdateStockCodeSection(code: code)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        didUpdateStockMarketSection(market: market)
    }
}
