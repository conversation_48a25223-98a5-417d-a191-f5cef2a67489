/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewPriceReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    let _decimalPlaces: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    let _price: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    let _priceCurrency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_price"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultTitle = localizedString("stock_review_price")
        defaultValue = localizedString("default_item_value")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        BehaviorRelay.combineLatest(
            _price.flatMap(ignoreNil).distinctUntilChanged(),
            _priceCurrency.flatMap(ignoreNil).distinctUntilChanged(),
            _decimalPlaces.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (price, currency, decimalPlaces) -> String in
            // If it can be formatted, display the price string, else display empty string
            if let priceString = PriceStringFormattingUtil.formattedPriceString(price: price, decimalPlaces: decimalPlaces, currency: currency) {
                return priceString
            } else {
                return localizedString("default_item_value")
            }
        }.asObservable().subscribe(onNext: { [weak self] value in
            guard let self = self else { return }
            self.viewModel.inputs.updateValue(value: value)
        }).disposed(by: bag)
    }

    func didUpdatePriceSection(price: Decimal, currency: WXCurrencyCode) {
        _price.accept(price)
        _priceCurrency.accept(currency)
    }

    func didUpdateSelectedOrderTypeSection(type: HSBCStockTradeOrderType) {
        switch type {
        case .market, .atAuction:
            viewModel?.inputs.updateModuleVisibility(visible: false)
        default:
            viewModel?.inputs.updateModuleVisibility(visible: true)
        }
    }

    func didUpdateDecimalPlacesSection(decimalPlaces: Int) {
        _decimalPlaces.accept(decimalPlaces)
    }
}

extension BuyOrderReviewPriceReviewItem: StockBuyView {
    // stock buy
    func updatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdatePriceSection(price: price, currency: currency)
    }
}

extension BuyOrderReviewPriceReviewItem: StockQuoteView {
    // stock quote
    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateSelectedOrderTypeSection(type: type)
    }

    // stock quote
    func updateDecimalPlacesSection(decimalPlaces: Int, error: HSBCErrorCode) {
        didUpdateDecimalPlacesSection(decimalPlaces: decimalPlaces)
    }
}
