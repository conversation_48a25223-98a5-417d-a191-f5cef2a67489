/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewGoodUntilReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    let _goodUntilDate: BehaviorRelay<Date?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_good_until"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("good_until_title")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        BehaviorRelay.combineLatest(
            _goodUntilDate.flatMap(ignoreNil).distinctUntilChanged(),
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).map { date, market -> (String, String) in
            let dateFormat = localizedString("date_format_good_until")
            let altText = date.stringWithDateFormat(dateFormat, timeZone: market.timeZone)
            return ("\(altText)\("\n")\(market.timeZoneAbbreviation)", altText)
        }.asObservable().subscribe(onNext: { [weak self] value, accessibilityLabel in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: value)
            self.reviewItem.valueLabel.accessibilityLabel = accessibilityLabel
        }).disposed(by: bag)
    }

    func didUpdateGoodUntilSection(date: Date?) {
        _goodUntilDate.accept(date)
    }

    func didUpdateStockMarketSection(market: WXMarket) {
        _market.accept(market)
    }
}

extension BuyOrderReviewGoodUntilReviewItem: StockBuyView {
    func updateGoodUntilSection(date: Date?, error: HSBCErrorCode) {
        didUpdateGoodUntilSection(date: date)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        didUpdateStockMarketSection(market: market)
    }
}
