/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewMarketReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_market"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_market")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateStockMarket(market: WXMarket) {
        var value: String
        switch market {
        case .hongKong:
            value = localizedString("market_hong_kong_long")
        case .china, .shangHai, .shenZhen:
            value = localizedString("market_china_long")
        case .america:
            value = localizedString("market_america_long")
        default:
            value = localizedString("default_item_value")
        }
        viewModel.inputs.updateValue(value: value)
        viewModel?.inputs.updateModuleVisibility(visible: true)
    }
}

extension BuyOrderReviewMarketReviewItem: StockBuyView {
    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        didUpdateStockMarket(market: market)
    }
}
