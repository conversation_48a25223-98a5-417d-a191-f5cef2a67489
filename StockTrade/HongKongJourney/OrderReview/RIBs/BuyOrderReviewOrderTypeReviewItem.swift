/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewOrderTypeReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_order_type"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_order_type")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateSelectedOrderTypeSection(type: HSBCStockTradeOrderType) {
        viewModel.inputs.updateValue(value: LocalizationUtil.localizedTradeOrderTypeName(type: type))
    }
}

extension BuyOrderReviewOrderTypeReviewItem: StockBuyView {
    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateSelectedOrderTypeSection(type: type)
    }
}
