/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewOrderReferenceNumberReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_order_reference_number"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultTitle = localizedString("order_reference_number")
        defaultValue = localizedString("default_item_value")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateModuleVisibility(visible: false)
    }

    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        viewModel?.inputs.updateModuleVisibility(visible: [.cancelBuy, .modifyBuy].contains(instruction))
    }

    func didUpdateReferenceNumberSection(number: String) {
        viewModel?.inputs.updateValue(value: number)
    }
}

extension BuyOrderReviewOrderReferenceNumberReviewItem: StockBuyView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }

    func updateReferenceNumberSection(number: String, error: HSBCErrorCode) {
        didUpdateReferenceNumberSection(number: number)
    }
}
