/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewAlertBannerItem: WXBannerInfo {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!
    var _productType: BehaviorRelay<WXInvestmentProductType?> = BehaviorRelay(value: nil)

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        // Default set it to hidden
        viewModel?.inputs.updateModuleVisibility(visible: false)

        // Only shows if product type is `.warrantInline` , `.warrantVanilla` or `.cbbc`
        // `.take(1)` is to because we only need to show once, if user hit dismiss then we wont show again
        _productType.flatMap(ignoreNil).take(1).subscribe(onNext: { [weak self] type in
            guard let self = self else { return }
            var bannerMessage = ""
            switch type {
            case .warrantInline, .warrantVanilla:
                self.bannerView?.accessibilityIdentifier = "banner_warrant_stock_warning"
                bannerMessage = localizedString("banner_warrant_stock_warning")
            case .cbbc:
                self.bannerView?.accessibilityIdentifier = "banner_cbbc_stock_warning"
                bannerMessage = localizedString("banner_cbbc_stock_warning")
            default:
                self.bannerView?.accessibilityIdentifier = "banner_cbbc_stock_warning"
            }

            self.viewModel?.inputs.updateViewData(
                BannerView.ViewModel(
                    message: bannerMessage,
                    rightButtonAccessibleValue: AccessibleValue(
                        localizedString("button_cta_close"),
                        accessibilityText: localizedString("button_cta_close"),
                        accessibilityValue: "button_cta_close"
                    ),
                    rightButtonAction: { [weak self] in
                        guard let self = self else { return }
                        self.viewModel?.inputs.updateModuleVisibility(visible: false)
                    }
                ))
        }).disposed(by: bag)
    }

    func didUpdateInvestmentProductType(investmentProductType: WXInvestmentProductType) {
        _productType.accept(investmentProductType)
    }
}

extension BuyOrderReviewAlertBannerItem: StockBuyView {
    func updateInvestmentProductType(investmentProductType: WXInvestmentProductType) {
        didUpdateInvestmentProductType(investmentProductType: investmentProductType)
    }
}
