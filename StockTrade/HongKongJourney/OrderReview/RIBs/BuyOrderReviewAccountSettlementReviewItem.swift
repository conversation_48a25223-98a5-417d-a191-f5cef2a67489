/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewAccountSettlementReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        accessibilityIdentifier = "review_item_pay_from_name"
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultTitle = localizedString("stock_review_pay_from")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateSettlementAccount(settlementAccount: WXSettlementAccount?) {
        if let account = settlementAccount {
            if account.isHongKongDefaultAccount() {
                viewModel?.inputs.updateValue(value: localizedString("account_selection_default_pay_from_account"))
            } else {
                viewModel?.inputs.updateValue(value: """
                \(LocalizationUtil.localizedAccountTypeName(type: account.type, currency: account.currency))
                \(account.number)
                """)
            }
        } else {
            // With out any account
            viewModel?.inputs.updateValue(value: localizedString("default_item_value"))
        }
    }
}

extension BuyOrderReviewAccountSettlementReviewItem: StockBuyView {
    func updateSettlementAccountSection(account: WXSettlementAccount?, error: HSBCErrorCode) {
        didUpdateSettlementAccount(settlementAccount: account)
    }
}
