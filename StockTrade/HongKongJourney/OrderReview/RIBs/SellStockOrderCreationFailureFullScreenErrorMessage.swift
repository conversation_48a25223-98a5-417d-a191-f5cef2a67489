/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileCore
import RxCocoa
import RxSwift
import UIKit

class SellStockOrderCreationFailureFullScreenErrorMessage: WXFullScreenErrorMessage, StockSellView, WXTrackable, WXViewControllable {
    weak var stockSellPresenter: StockSellPresenterImplementation!
    weak var viewControllable: UIViewController?

    // WXFullScreenErrorMessageProtocol
    var _errorCode: BehaviorRelay<HSBCErrorCode?> = BehaviorRelay(value: nil)
    var _presenterRequestType: BehaviorRelay<PresenterRequestType?> = BehaviorRelay(value: nil)
    var _warnings: BehaviorRelay<[HSBCWarning]?> = BehaviorRelay(value: nil)

    func setViewControllable(_ viewControllable: UIViewController) {
        self.viewControllable = viewControllable
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockSellPresenter = StockSellPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockSellPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockSellPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?
            .outputs
            .didClickDismissButton
            .asObservable()
            .subscribe(onNext: { [weak self] _ in
                guard let self = self else { return }
                // go back to order details input
                self.viewModel.updateModuleVisibility(false)

            }).disposed(by: bag)

        BehaviorRelay.combineLatest(
            _presenterRequestType.flatMap(ignoreNil).distinctUntilChanged(),
            _errorCode.flatMap(ignoreNil).distinctUntilChanged(),
            _warnings.flatMap(ignoreNil).distinctUntilChanged()
        ).asObservable().subscribe(onNext: { [weak self] type, errorCode, warnings in
            guard let self = self else { return }
            if [
                PresenterRequestType.PRESENTER_REQUEST_POST_BUY_ORDER,
                PresenterRequestType.PRESENTER_REQUEST_POST_SELL_ORDER
            ].contains(type), errorCode == HSBCErrorCode.badRequest {
                let tuple = ErrorMessagesHandler.getErrorMessage(errorCode, warnings: warnings)
                // (header, body, cta)
                self.header = tuple.0
                self.body = tuple.1
                self.dismissButtonTitle = tuple.2
                self.viewModel.inputs.updateModuleVisibility(true)
            }

        }).disposed(by: bag)
    }

    override func setupTealiumTaggings() {
        Driver.combineLatest(
            viewModel!.outputs.moduleVisibility,
            _presenterRequestType.asDriver().flatMap(ignoreNil).distinctUntilChanged(),
            _errorCode.asDriver().flatMap(ignoreNil).distinctUntilChanged(),
            _warnings.asDriver().distinctUntilChanged()
        ).asObservable().subscribe(onNext: { [weak self] moduleVisibility, presenterRequestType, errorCode, warnings in
            guard let self = self, moduleVisibility == true else { return }
            if [
                PresenterRequestType.PRESENTER_REQUEST_POST_BUY_ORDER,
                PresenterRequestType.PRESENTER_REQUEST_POST_SELL_ORDER
            ].contains(presenterRequestType), errorCode == HSBCErrorCode.badRequest {
                self.tracker.trackEvent(EventInfo.sellReviewClickConfirmHitHttpCode(errorCode: String(errorCode.rawValue), eventContent: "http 400 error", defaultParameters: self.tracker.defaultParameters))
                self.tracker.appDynamicTrackEventWithoutArguments("Order submission - HTTP" + String(errorCode.rawValue) + " - Business Exception - Confirmation ", String(describing: type(of: self)))
            }

        }).disposed(by: bag)
    }

    func didReciveErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]) {
        _warnings.accept(warnings)
        _errorCode.accept(errorCode)
        _presenterRequestType.accept(type)
    }

    // Presenter mehtod
    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        // WXFullScreenErrorMessageProtocol
        didReciveErrorMessage(type, errorCode: errorCode, warnings: warnings ?? [])
    }
}
