/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import UIKit

class BuyOrderReviewErrorMessage: WXErrorMessage, StockBuyView, StockQuoteView, WXErrorMessageProtocol, WXTrackable {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        // Biz Logic
        stockBuyPresenter?.viewDidLoad(view: self)
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        // Biz Logic
        stockBuyPresenter?.viewDeinit(view: self)
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateModuleVisibility(visible: false)
    }

    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        if type != .PRESENTER_REQUEST_POST_BUY_ORDER, errorCode.rawValue != 400, errorCode.rawValue < 500 {
            showRawErrorMessage(type, errorCode: errorCode, warnings: warnings)
        }
    }

    func showErrorMessage(_ type: PresenterRequestType, errorMeta: ErrorCodeMeta) {
        switch type {
        case .PRESENTER_REQUEST_VALIDATE_BUY_ORDER, .PRESENTER_REQUEST_POST_BUY_ORDER, .PRESENTER_REQUEST_CANCEL_BUY_ORDER, .PRESENTER_REQUEST_MODIFY_BUY_ORDER:
            if let header = errorMeta.header, let body = errorMeta.body, let cta = errorMeta.cta {
                viewModel?.inputs.updateErrorMessage(title: header, body: body, buttonTitle: cta)
                viewModel?.inputs.updateModuleVisibility(visible: true)
                viewModel?.inputs.updateShouldPopBack(shouldPopBack: errorMeta.shouldPopBack)
            } else {
                preconditionFailure("\(#function) was called, but error meta is not valid \(errorMeta)")
            }
        default:
            break
        }
    }
}
