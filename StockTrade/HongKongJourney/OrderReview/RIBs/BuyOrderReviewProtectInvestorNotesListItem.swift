/** @RIB */
//
//

import MobileDesign
import UIKit

class BuyOrderReviewProtectInvestorNotesListItem: WXTextWithLeadingIcon {
    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "stock_review_protect_investor"
    }

    override func initializeVariables() {
        super.initializeVariables()
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
        label.lineBreakMode = .byWordWrapping
        label.numberOfLines = 0

        // *** Create instance of `NSMutableParagraphStyle`
        let paragraphStyle = NSMutableParagraphStyle()
        // *** set LineSpacing property in points ***
        paragraphStyle.lineSpacing = 1 // Whatever line spacing you want in points
        let attributes = [
            NSAttributedString.Key.font: ThemeManager.shared.currentTheme.fonts.caption,
            NSAttributedString.Key.foregroundColor: ThemeManager.shared.currentTheme.colors.textPrimary,
            NSAttributedString.Key.paragraphStyle: paragraphStyle
        ]
        let attributedString = NSMutableAttributedString(string: localizedString("stock_review_protect_investor"), attributes: attributes)
        viewModel?.inputs.updateAttributedString(value: attributedString)
        viewModel?.inputs.updateModuleVisibility(visible: true)
    }
}
