//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class BuyOrderReviewConfirmationNotesListItem: WXBaseUIView {
    let label: UILabel = {
        let label = EdgeInsetLabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.lineBreakMode = .byWordWrapping
        label.backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted.withAlphaComponent(0)
        label.numberOfLines = 0
        label.textInsets = UIEdgeInsets(top: 16, left: 12, bottom: 16, right: 16)
        return label
    }()

    let icon: UIImageView = {
        let icon = UIImageView(image: ThemeManager.shared.currentTheme.systemIcons.informationOnLight)
        icon.translatesAutoresizingMaskIntoConstraints = false
        return icon
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    required init() {
        super.init(frame: .zero)
        initializeVariables()
        setupViews()
    }

    func initializeVariables() {
        translatesAutoresizingMaskIntoConstraints = false
    }

    func setupViews() {
        addSubview(icon)
        addSubview(label)
        NSLayoutConstraint.activate([
            icon.topAnchor.constraint(equalTo: topAnchor, constant: 16),
            icon.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            icon.widthAnchor.constraint(equalToConstant: 20),
            icon.heightAnchor.constraint(equalToConstant: 20),
            label.leadingAnchor.constraint(equalTo: icon.trailingAnchor),
            label.topAnchor.constraint(equalTo: topAnchor),
            label.trailingAnchor.constraint(equalTo: trailingAnchor),
            heightAnchor.constraint(equalTo: label.heightAnchor)
        ])

        // *** Create instance of `NSMutableParagraphStyle`
        let paragraphStyle = NSMutableParagraphStyle()
        // *** set LineSpacing property in points ***
        paragraphStyle.lineSpacing = 1 // Whatever line spacing you want in points
        paragraphStyle.paragraphSpacing = 10

        let htmlString = """
        <span style=\"font-family: '-apple-system', 'HelveticaNeue'; font-size:\(ThemeManager.shared.currentTheme.fonts.caption)\">\(localizedString("review_tandc_risk_acceptance_acknowledgement_note_html_format"))</span>
        """
        if let data = htmlString.data(using: .unicode),
            let attributedString = try? NSAttributedString(data: data, options: [.documentType: NSAttributedString.DocumentType.html], documentAttributes: nil) {
            label.attributedText = attributedString
        }
    }
}
