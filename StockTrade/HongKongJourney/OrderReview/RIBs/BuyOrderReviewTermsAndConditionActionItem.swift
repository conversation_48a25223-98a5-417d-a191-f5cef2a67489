/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import UIKit
class BuyOrderReviewTermsAndConditionActionItem: WXReviewActionItem, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "textview_terms_and_conditions"
    }

    override func initializeVariables() {
        super.initializeVariables()
        defaultTitle = localizedString("disclaimer_t_and_c_title")
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?
            .outputs
            .didTapButton
            .asObservable()
            .subscribe(onNext: {
                self.tracker.trackPage(EventInfo.buyImportantNotesPageLoad(defaultParameters: self.tracker.defaultParameters))
                if let viewController = self.viewControllable {
                    _ = WebComponentFactory.startWebComponent(aClass: BuyOrderReviewTermsAndConditionActionItem.self, vc: viewController, title: localizedString("disclaimer_t_and_c_title"), htmlFileName: localizedString("order_terms_conditions_customer_declarations_html"))
                }
            }).disposed(by: bag)
    }
}
