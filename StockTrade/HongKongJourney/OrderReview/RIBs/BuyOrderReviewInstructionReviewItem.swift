/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewInstructionReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_instruction"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_instruction")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        viewModel?.inputs.updateValue(value: LocalizationUtil.instructionDescription(type: instruction))
    }
}

extension BuyOrderReviewInstructionReviewItem: StockBuyView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }
}
