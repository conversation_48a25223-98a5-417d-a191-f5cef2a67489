/** @RIB */
//
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewNotesListItem: WXTextWithLeadingIcon {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "textview_note_order_type"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        _orderType.flatMap(ignoreNil).distinctUntilChanged().map { orderType -> String in
            switch orderType {
            case .market:
                self.viewModel.inputs.updateModuleVisibility(visible: true)
                return localizedString("order_type_market_note")
            case .atAuction:
                self.viewModel.inputs.updateModuleVisibility(visible: true)
                return localizedString("order_type_at_auction_note")
            default:
                self.viewModel.inputs.updateModuleVisibility(visible: false)
                return localizedString("default_item_value")
            }
        }.asObservable().subscribe(onNext: { [weak self] value in
            guard let self = self else { return }
            self.viewModel.inputs.updateString(value: value)
        }).disposed(by: bag)
    }

    override func setupViews() {
        super.setupViews()
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundHighlighted
    }

    func didUpdateSelectedOrderTypeSection(type: HSBCStockTradeOrderType) {
        _orderType.accept(type)
    }
}

extension BuyOrderReviewNotesListItem: StockBuyView {
    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateSelectedOrderTypeSection(type: type)
    }
}
