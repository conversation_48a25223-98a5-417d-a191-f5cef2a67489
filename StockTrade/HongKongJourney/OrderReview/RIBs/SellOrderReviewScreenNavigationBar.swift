/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift

class SellOrderReviewScreenNavigationBar: WXNavigationBarWithImagebaseLeftRightButton {
    weak var stockSellPresenter: StockSellPresenterImplementation!
    var _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)

    override func initializeVariables() {
        super.initializeVariables()

        stockSellPresenter = StockSellPresenterImplementation.configure(view: self, routerImplementation: nil)
        leftButtonImage = ThemeManager.shared.currentTheme.systemIcons.chevronLeft
        title = localizedString("stock_review_title")
        leftButtonImageContentDescription = "button_toolbar_back"
        titleLabelContentDescription = "text_toolbar_title"
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockSellPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockSellPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()

        viewModel?.outputs.didClickLeftButton.asObservable().subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.removeFromSuperview()
            self._instruction.asObservable().take(1).subscribe(onNext: {
                [weak self] instruction in
                if instruction == HSBCStockTradeOrderInstruction.cancelSell {
                    StockTradeJourneyFactory.finishSellJourney()
                } else {
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.viewControllable?.navigationController?.popViewController(animated: true)
                    }
                }
            }).disposed(by: self.bag)
        }).disposed(by: bag)
    }
}

extension SellOrderReviewScreenNavigationBar: StockSellView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        _instruction.accept(instruction)
    }
}
