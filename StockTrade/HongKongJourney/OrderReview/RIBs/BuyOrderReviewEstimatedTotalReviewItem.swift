/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewEstimatedTotalReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    var _estimatedTotal: BehaviorRelay<WXDecimal?> = BehaviorRelay(value: nil)
    var _priceCurrency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)
    var _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_estimated_total"
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_estimated_total")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        BehaviorRelay.combineLatest(
            _estimatedTotal.flatMap(ignoreNil).distinctUntilChanged(),
            _priceCurrency.flatMap(ignoreNil).distinctUntilChanged(),
            _market.flatMap(ignoreNil).distinctUntilChanged()
        ).map { (total, currency, market) -> String in
            // If it can be formatted, display the price string, else display empty string
            if let priceString = PriceStringFormattingUtil.formattedPriceString(price: total.value, currency: currency, market: market, isEstimatedTotal: true) {
                return priceString
            } else {
                return localizedString("default_item_value")
            }
        }.asObservable().subscribe(onNext: { [weak self] value in
            guard let self = self else { return }
            self.viewModel?.inputs.updateValue(value: value)
        }).disposed(by: bag)
    }

    func didUpdateCaculatedEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode) {
        _estimatedTotal.accept(total)
        _priceCurrency.accept(currency)
    }

    func didUpdateStockMarketSection(market: WXMarket) {
        _market.accept(market)
    }
}

extension BuyOrderReviewEstimatedTotalReviewItem: StockBuyView {
    func updateCaculatedEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdateCaculatedEstimatedTotalSection(total: total, currency: currency)
    }

    func updateStockMarketSection(market: WXMarket, error: HSBCErrorCode) {
        didUpdateStockMarketSection(market: market)
    }
}
