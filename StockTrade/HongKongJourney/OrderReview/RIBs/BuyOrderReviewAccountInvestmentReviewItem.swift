/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewAccountInvestmentReviewItem: WXReviewItem {
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "review_item_securities_name"
    }

    override func initializeVariables() {
        super.initializeVariables()

        stockBuyPresenter = StockBuyPresenterImplementationSingapore.configure(view: self, routerImplementation: nil)
        defaultValue = localizedString("default_item_value")
        defaultTitle = localizedString("stock_review_hold_securities_in")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    func didUpdateInvestmentAccount(investmentAccount: WXInvestmentAccount?) {
        if let account = investmentAccount {
            viewModel?.inputs.updateValue(value: """
            \(LocalizationUtil.localizedAccountTypeName(type: account.type, currency: account.currency))
            \(account.number)
            """)
        }
    }
}

extension BuyOrderReviewAccountInvestmentReviewItem: StockBuyView {
    func updateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        didUpdateInvestmentAccount(investmentAccount: account)
    }
}
