/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewImportantNotesAndRiskActionItem: WXReviewActionItem, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "textview_important_notes"
    }

    override func initializeVariables() {
        super.initializeVariables()
        defaultTitle = localizedString("important_risks_and_notes")
    }

    override func didTap() {
        super.didTap()
        if let viewController = viewControllable {
            tracker.trackEvent(EventInfo.buyImportantNotesPageLoad(defaultParameters: tracker.defaultParameters))
            _ = WebComponentFactory.startWebComponent(aClass: BuyOrderReviewImportantNotesAndRiskActionItem.self, vc: viewController, title: localizedString("important_risks_and_notes"), htmlFileName: localizedString("important_risks_and_notes_html"))
        }
    }
}
