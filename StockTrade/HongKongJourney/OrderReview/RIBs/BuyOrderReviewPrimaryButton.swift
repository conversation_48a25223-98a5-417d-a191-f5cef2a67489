/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class BuyOrderReviewPrimaryButton: WXPrimaryButton, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    weak var stockBuyPresenter: StockBuyPresenterImplementation!

    let _instruction: BehaviorRelay<HSBCStockTradeOrderInstruction?> = BehaviorRelay(value: nil)
    var _productType: BehaviorRelay<WXInvestmentProductType?> = BehaviorRelay(value: nil)
    var _orderType: BehaviorRelay<HSBCStockTradeOrderType?> = BehaviorRelay(value: nil)
    var _referenceNumber: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    var _stockName: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _price: BehaviorRelay<Decimal?> = BehaviorRelay(value: nil)
    let _quantity: BehaviorRelay<Int?> = BehaviorRelay(value: nil)
    let _paymentAccountName: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _holdSecuritiesAccountName: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    let _goodUntilDate: BehaviorRelay<Date?> = BehaviorRelay(value: nil)
    let _orderStatus: BehaviorRelay<HSBCOrderStatus?> = BehaviorRelay(value: nil)
    let _currency: BehaviorRelay<WXCurrencyCode?> = BehaviorRelay(value: nil)

    var instruction: Driver<HSBCStockTradeOrderInstruction> { _instruction.asDriver().flatMap(ignoreNil).distinctUntilChanged() }

    let _isUserConsent: BehaviorRelay<Bool?> = BehaviorRelay(value: nil)

    var isUserConsent: Driver<Bool> { _isUserConsent.asDriver().flatMap(ignoreNil).distinctUntilChanged() }

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "button_confirm"
    }

    override func initializeVariables() {
        super.initializeVariables()
        defaultButtonState = .enabled
        title = localizedString("stock_review_confirm")
        if let viewController = viewControllable {
            stockBuyPresenter = StockBuyPresenterImplementation.configure(view: self, routerImplementation: StockBuyRouterImplementation(viewController: viewController, tracker: tracker))
        }
    }

    override func setupViews() {
        super.setupViews()
        NSLayoutConstraint.activate([
            primaryButton.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            primaryButton.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -16),
            primaryButton.topAnchor.constraint(equalTo: topAnchor),
            primaryButton.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockBuyPresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockBuyPresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        isUserConsent.asObservable().subscribe(onNext: { [weak self] isUserConsent in
            guard let self = self else { return }
            self.viewModel.inputs.updateModuleState(isUserConsent ? .enabled : .disabled)
        }).disposed(by: bag)

        viewModel?
            .outputs
            .didTapButton
            .asObservable()
            .subscribe(onNext: {
                self.instruction.asObservable().take(1).subscribe(onNext: { [weak self] instruction in
                    guard let self = self else { return }
                    switch instruction {
                    case .modifyBuy:
                        self.stockBuyPresenter?.attemptModifyOrder(self)
                    case .cancelBuy:
                        self.stockBuyPresenter?.attemptCancelOrder(self)
                    case .buy:
                        self.stockBuyPresenter?.attemptPlaceOrder(self)
                    default:
                        break
                    }
                }).disposed(by: self.bag)
            }).disposed(by: bag)
    }

    override func setupTealiumTaggings() {
        BehaviorRelay.combineLatest(
            _instruction.flatMap(ignoreNil),
            _productType.flatMap(ignoreNil),
            _orderType.flatMap(ignoreNil),
            _holdSecuritiesAccountName.flatMap(ignoreNil)
        ).take(1).subscribe(onNext: { [weak self] instruction, productType, orderType, holdSecuritesAccountName in
            guard let self = self else { return }
            switch instruction {
            case .buy:
                self.tracker.trackEvent(EventInfo.buyReviewConfirmClick(defaultParameters: self.tracker.defaultParameters))
            case .modifyBuy:
                switch productType {
                case .cbbc:
                    self.tracker.trackEvent(EventInfo.orderStatusModifyOrderReviewCbbcClickConfirm(defaultParameters: self.tracker.defaultParameters))
                case .warrantInline, .warrantVanilla:
                    self.tracker.trackEvent(EventInfo.orderStatusModifyOrderReviewWarrantClickConfirm(defaultParameters: self.tracker.defaultParameters))
                default:
                    break
                }
                switch orderType {
                case .market:
                    self.tracker.trackEvent(EventInfo.orderStatusModifyOrderReviewMarketOrderClickConfirm(defaultParameters: self.tracker.defaultParameters))
                case .atAuction:
                    self.tracker.trackEvent(EventInfo.orderStatusModifyOrderReviewAtAuctionClickConfirm(defaultParameters: self.tracker.defaultParameters))
                default:
                    break
                }
            case .cancelBuy:
                self.tracker.trackEvent(EventInfo.orderStatusReviewCancelClickComfirm(defaultParameters: self.tracker.defaultParameters))
            default:
                break
            }
        }).disposed(by: bag)
    }

    func showLoadingIndicator(_ type: PresenterRequestType) {
        switch type {
        case .PRESENTER_REQUEST_POST_BUY_ORDER, .PRESENTER_REQUEST_MODIFY_BUY_ORDER, .PRESENTER_REQUEST_CANCEL_BUY_ORDER:
            viewModel.inputs.updateModuleState(.loading(announcement: "loading"))
        default:
            break
        }
    }

    func hideLoadingIndicator(_ type: PresenterRequestType) {
        switch type {
        case .PRESENTER_REQUEST_POST_BUY_ORDER, .PRESENTER_REQUEST_MODIFY_BUY_ORDER, .PRESENTER_REQUEST_CANCEL_BUY_ORDER:
            viewModel.inputs.updateModuleState(.success(announcement: "success", onAnimationCompletionHandler: {}))
        default:
            break
        }
    }

    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        switch type {
        case .PRESENTER_REQUEST_POST_BUY_ORDER, .PRESENTER_REQUEST_MODIFY_BUY_ORDER, .PRESENTER_REQUEST_CANCEL_BUY_ORDER:
            viewModel.inputs.updateModuleState(.enabled)
        default:
            break
        }
    }

    func didUpdateInstructionSection(instruction: HSBCStockTradeOrderInstruction) {
        _instruction.accept(instruction)
    }

    func didUpdateUserConsentSection(isUserConsent: Bool) {
        _isUserConsent.accept(isUserConsent)
    }

    func didUpdateSelectedOrderTypeSection(type: HSBCStockTradeOrderType) {
        _orderType.accept(type)
    }

    func didUpdateInvestmentProductType(investmentProductType: WXInvestmentProductType) {
        _productType.accept(investmentProductType)
    }

    func didUpdateReferenceNumberSection(number: String) {
        _referenceNumber.accept(number)
    }

    func didUpdateStockNameSection(name: String) {
        _stockName.accept(name)
    }

    func didUpdatePriceSection(price: Decimal, currency: WXCurrencyCode) {
        _price.accept(price)
    }

    func didUpdateQuantitySection(quantity: Int64) {
        _quantity.accept(Int(quantity))
    }

    func didUpdateSettlementAccountSection(account: WXSettlementAccount?) {
        if let type = account?.type, let currency = account?.currency {
            _paymentAccountName.accept(LocalizationUtil.localizedAccountTypeName(type: type, currency: currency, shouldDisplayCurrency: false))
        }
    }

    func didUpdateInvestmentAccountSection(account: WXInvestmentAccount) {
        _holdSecuritiesAccountName.accept(LocalizationUtil.localizedAccountTypeName(type: account.type, currency: account.currency, shouldDisplayCurrency: false))
    }

    func didUpdateGoodUntilSection(date: Date?) {
        _goodUntilDate.accept(date)
    }

    func didUpdateEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode) {
        _currency.accept(currency)
    }
}

extension BuyOrderReviewPrimaryButton: StockBuyView {
    func updateInstructionSection(instruction: HSBCStockTradeOrderInstruction, error: HSBCErrorCode) {
        didUpdateInstructionSection(instruction: instruction)
    }

    func updateUserConsentSection(isUserConsent: Bool) {
        didUpdateUserConsentSection(isUserConsent: isUserConsent)
    }

    func updateSelectedOrderTypeSection(type: HSBCStockTradeOrderType, error: HSBCErrorCode) {
        didUpdateSelectedOrderTypeSection(type: type)
    }

    func updateInvestmentProductType(investmentProductType: WXInvestmentProductType) {
        didUpdateInvestmentProductType(investmentProductType: investmentProductType)
    }

    func updateReferenceNumberSection(number: String, error: HSBCErrorCode) {
        didUpdateReferenceNumberSection(number: number)
    }

    func updateStockNameSection(name: String, error: HSBCErrorCode) {
        didUpdateStockNameSection(name: name)
    }

    func updatePriceSection(price: Decimal, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdatePriceSection(price: price, currency: currency)
    }

    func updateQuantitySection(quantity: Int64, error: HSBCErrorCode) {
        didUpdateQuantitySection(quantity: quantity)
    }

    func updateSettlementAccountSection(account: WXSettlementAccount?, error: HSBCErrorCode) {
        didUpdateSettlementAccountSection(account: account)
    }

    func updateInvestmentAccountSection(account: WXInvestmentAccount, error: HSBCErrorCode) {
        didUpdateInvestmentAccountSection(account: account)
    }

    func updateGoodUntilSection(date: Date?, error: HSBCErrorCode) {
        didUpdateGoodUntilSection(date: date)
    }

    func updateEstimatedTotalSection(total: WXDecimal?, currency: WXCurrencyCode, error: HSBCErrorCode) {
        didUpdateEstimatedTotalSection(total: total, currency: currency)
    }
}
