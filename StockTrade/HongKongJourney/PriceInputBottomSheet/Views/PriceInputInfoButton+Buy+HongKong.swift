/** @RIB */
//
//  PriceInputInfoButton+<PERSON><PERSON><PERSON>.swift
//  StockTrade
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import UIKit

class PriceInputInfoButtonBuyHongKong: WXInfoButton, WXViewControllable {
    weak var viewControllable: UIViewController?

    override func bindViewModel() {
        super.bindViewModel()
        rx.tap.bind { [weak self] in
            guard let self = self else { return }
            let vc = PriceInputInfoScreenViewControllerHongKong()
            vc.setListener(self)
            let nav = vc.embedInNavigationController()
            self.viewControllable?.present(nav, animated: true, completion: nil)
        }.disposed(by: bag)
    }
}

extension PriceInputInfoButtonBuyHongKong: WXInfoScreenViewControllerListener {
    func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    func showModule(_ module: WXInfoScreenViewController) {
        /* empty implementation */
    }
}
