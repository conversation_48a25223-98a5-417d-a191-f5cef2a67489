/** @View */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class DisclaimerViewControllerHongKong: WXInfoScreenViewController {
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        defaultTitle = localizedString("quote_disclaimer_title")
        defaultBodyText = ""
        defaultSecondaryButtonTitle = nil
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    func didUpdateMarket(market: WXMarket?) {
        if let market = market {
            var body: String = ""
            switch market {
            case .hongKong:
                body = localizedString("quote_disclaimer_aastock_content")

            case .america, .china, .shangHai, .shenZhen:
                body = localizedString("quote_disclaimer_content")
            case .notSelected:
                break
            case .singapore:
                break
            case .others:
                break
            }

            viewModel?.inputs.updateMessage(title: localizedString("quote_disclaimer_title"), body: body)
        }
    }
}

extension DisclaimerViewControllerHongKong: StockQuoteView {
    func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateMarket(market: quote.stockMarket)
    }
}
