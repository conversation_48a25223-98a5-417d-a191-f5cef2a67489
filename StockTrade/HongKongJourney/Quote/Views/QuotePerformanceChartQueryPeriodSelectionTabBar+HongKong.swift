/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import UIKit
class QuotePerformanceChartQueryPeriodSelectionTabBarHongKong: WXTabBar {
    weak var stockQuotePresenter: StockQuotePresenterImplementation!
    var queryPeriods: [StockQuoteHistoryPeriod]!

    override func initializeVariables() {
        super.initializeVariables()
        queryPeriods = [.oneDay, .oneWeek, .oneMonth, .threeMonths, .sixMonths]
        titles = [
            AccessibleValue("1D", accessibilityText: localizedString("alt_price_change_1d"), accessibilityValue: "1D"),
            AccessibleValue("1W", accessibilityText: localizedString("alt_price_change_1w"), accessibilityValue: "1W"),
            AccessibleValue("1M", accessibilityText: localizedString("alt_price_change_1m"), accessibilityValue: "1M"),
            AccessibleValue("3M", accessibilityText: localizedString("alt_price_change_3m"), accessibilityValue: "3M"),
            AccessibleValue("6M", accessibilityText: localizedString("alt_price_change_6m"), accessibilityValue: "6M")
        ]
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)

        NSLayoutConstraint.activate([
            heightAnchor.constraint(greaterThanOrEqualToConstant: 41)
        ])
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
        stockQuotePresenter?.didEditSection(section: .queryHistoryPeriod(period: .oneDay))
    }

    override func tabDidSelect(at index: Int) {
        super.tabDidSelect(at: index)
        stockQuotePresenter?.didEditSection(section: .queryHistoryPeriod(period: queryPeriods[index]))
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    func didUpdateSelectedHistoryQueryPeriodSection(period: StockQuoteHistoryPeriod) {
        guard let index = queryPeriods.firstIndex(of: period) else { return }
        viewModel?.inputs.didSetTab(index: index)
    }
}

extension QuotePerformanceChartQueryPeriodSelectionTabBarHongKong: StockQuoteView {
    func updateSelectedHistoryQueryPeriodSection(period: StockQuoteHistoryPeriod) {
        didUpdateSelectedHistoryQueryPeriodSection(period: period)
    }
}
