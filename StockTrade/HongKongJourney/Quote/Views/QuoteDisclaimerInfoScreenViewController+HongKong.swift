/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import Foundation
class QuoteDisclaimerInfoScreenViewControllerHongKong: WXInfoScreenViewController {
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    override func initializeVariables() {
        defaultTitle = localizedString("quote_disclaimer_title")
        defaultBodyText = localizedString("default_item_value")
        defaultSecondaryButtonTitle = nil
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    func didUpdateMarket(market: WXMarket?) {
        if let market = market {
            var body = ""
            switch market {
            case .hongKong:
                body = localizedString("quote_help_hk_market_content")
                viewModel?.inputs.updateMessage(title: localizedString("quote_disclaimer_hk_title"), body: body)
                viewModel?.inputs.updateNavigationTitle(title: localizedString("quote_disclaimer_hk_title"))
            default:
                body = localizedString("quote_help_us_cn_market_content")
                viewModel?.inputs.updateMessage(title: localizedString("quote_disclaimer_title"), body: body)
                viewModel?.inputs.updateNavigationTitle(title: localizedString("quote_disclaimer_title"))
            }
        }
    }
}

extension QuoteDisclaimerInfoScreenViewControllerHongKong: StockQuoteView {
    func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        didUpdateMarket(market: quote.stockMarket)
    }
}
