/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class QuoteScreenChartViewHongKong: WXBaseUIView, WXBaseView {
    var periodSelectionTabBar: QuotePerformanceChartQueryPeriodSelectionTabBarHongKong!
    var chartView: QuoteLineChartViewHongKong!
    var loadingIndicator: QuoteChartLoadingIndicatorHongKong!
    var noChartDataWarningView: QuoteChartNoDataWarningHongKong!
//    var chartDisclaimerView: ChartDisclaimerViewHongKong!

    weak var viewControllable: UIViewController?
    var views: [WXBaseView] = []

    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        return stackView
    }()

    convenience init(viewControllable: UIViewController?, tracker: WXTracking, market: WXMarket = .hongKong) {
        self.init(frame: .zero)
        self.viewControllable = viewControllable
        periodSelectionTabBar = QuotePerformanceChartQueryPeriodSelectionTabBarHongKong()
        chartView = QuoteLineChartViewHongKong(viewControllable: viewControllable, market: market)
        loadingIndicator = QuoteChartLoadingIndicatorHongKong()
        loadingIndicator.setViewControllable(nil)
        noChartDataWarningView = QuoteChartNoDataWarningHongKong()
        noChartDataWarningView.setViewControllable(nil)
        noChartDataWarningView.setListener(self)
//        chartDisclaimerView = ChartDisclaimerViewHongKong(viewControllable: viewControllable, tracker: tracker)

        views = [
            periodSelectionTabBar as WXBaseView,
            chartView as WXBaseView,
            loadingIndicator as WXBaseView,
            noChartDataWarningView as WXBaseView
//            chartDisclaimerView as WXBaseView
        ]
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    required init() {
        super.init(frame: .zero)
    }

    func initializeVariables() {}

    func setupTealiumTaggings() {
        /* Empty Implementation */
    }

    func viewDidAppear() {
        for view in views {
            view.viewDidAppear()
        }
    }

    func viewWillDisappear() {
        for view in views {
            view.viewWillDisappear()
        }
    }

    func viewDidLoad() {
        initializeVariables()
        setupViews()
        initViewModel()
        bindViewModel()
        setupAccessibilityIndentifiers()

        for view in views {
            view.viewDidLoad()
        }
        setupTealiumTaggings()
    }

    func setupViews() {
        loadingIndicator.setListener(self)
        noChartDataWarningView.setListener(self)
        chartView.setListener(self)
//        chartDisclaimerView.setListener(self)

        addSubview(stackView)
        addSubview(loadingIndicator)

        // FIXIT: Refactor the layout constraint setup
        stackView.addArrangedSubview(periodSelectionTabBar)
        stackView.addArrangedSubview(noChartDataWarningView)
        stackView.addArrangedSubview(chartView)
        stackView.setCustomSpacing(16, after: chartView)
//        stackView.addArrangedSubview(chartDisclaimerView)

        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: topAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor),
            loadingIndicator.topAnchor.constraint(equalTo: topAnchor),
            loadingIndicator.leadingAnchor.constraint(equalTo: leadingAnchor),
            loadingIndicator.trailingAnchor.constraint(equalTo: trailingAnchor),
            loadingIndicator.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    func setupAccessibilityIndentifiers() {}

    func viewDeinit() {
        for view in views {
            view.viewDeinit()
        }
    }

    func initViewModel() {}

    func bindViewModel() {}
}

// extension QuoteScreenChartViewHongKong: WXExternalLinkLabelViewModelDelegate {
//    func showModule(_ module: WXExternalLinkLabelView) {
//        module.isHidden = false
//    }
//
//    func hideModule(_ module: WXExternalLinkLabelView) {
//        module.isHidden = true
//    }
// }

extension QuoteScreenChartViewHongKong: WXLoadingIndicatorDelegate {
    func showLocaingIndicator(_ module: WXLoadingIndicator) {
        module.isHidden = false
        layoutIfNeeded()
    }

    func hideLocaingIndicator(_ module: WXLoadingIndicator) {
        module.isHidden = true
        layoutIfNeeded()
    }
}

extension QuoteScreenChartViewHongKong: WXInlinedWarningMessageViewDelegate {
    func showInlinedWarningMessageViewModule(_ module: WXInlinedWarningMessageView) {
        module.isHidden = false
        layoutIfNeeded()
    }

    func hideInlinedWarningMessageViewModule(_ module: WXInlinedWarningMessageView) {
        module.isHidden = true
        layoutIfNeeded()
    }
}

extension QuoteScreenChartViewHongKong: WXLineChartViewDelegate {
    func showChartModule(_ module: WXLineChartView) {
        module.isHidden = false
    }

    func hideChartModule(_ module: WXLineChartView) {
        module.isHidden = true
    }
}

class QuoteChartLoadingIndicatorHongKong: WXLoadingIndicator, WXViewControllable {
    weak var viewControllable: UIViewController?

    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        loadingDataLabel.isHidden = false
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        overlayViewBackgroundColor = ThemeManager.shared.currentTheme.colors.divider.withAlphaComponent(0.3)
    }

    override func setupViews() {
        super.setupViews()
        translatesAutoresizingMaskIntoConstraints = false
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "predictive_search_result_view_loading_indicator"
    }
}

extension QuoteChartLoadingIndicatorHongKong: StockQuoteView {
    func showLoadingIndicator(_ type: PresenterRequestType) {
        if type == .PRESENTER_REQUEST_GET_HISTORY_DATA {
            viewModel?.inputs.updateModuleVisibility(visible: true)
        }
    }

    func hideLoadingIndicator(_ type: PresenterRequestType) {
        if type == .PRESENTER_REQUEST_GET_HISTORY_DATA {
            viewModel?.inputs.updateModuleVisibility(visible: false)
        }
    }
}
