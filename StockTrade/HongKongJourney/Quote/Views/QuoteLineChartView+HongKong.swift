/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import RxCocoa
import RxSwift
import UIKit

class QuoteLineChartViewHongKong: WXLineChartView, StockQuoteView, WXViewControllable, WXTrackable {
    weak var viewControllable: UIViewController?
    weak var stockQuotePresenter: StockQuotePresenterImplementation!
    private var market: WXMarket!

    var _market: BehaviorRelay<WXMarket?> = BehaviorRelay(value: nil)
    var _productName: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    var _productSymbol: BehaviorRelay<String?> = BehaviorRelay(value: nil)
    var _productType: BehaviorRelay<WXInvestmentProductType?> = BehaviorRelay(value: nil)

    convenience init(viewControllable: UIViewController?, market: WXMarket = .hongKong) {
        self.init(frame: .zero)
        self.market = market
        self.viewControllable = viewControllable
        isAccessibilityElement = true
        accessibilityLabel = localizedString("alt_chart_to_maximise")
    }

    override func initializeVariables() {
        super.initializeVariables()
        timeZone = market.timeZone
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        let xAxisHeight: CGFloat = 30
        chartView.chartInsets = UIEdgeInsets(top: 30, left: 50, bottom: xAxisHeight + 10, right: 20)

        NSLayoutConstraint.activate([
            heightAnchor.constraint(equalToConstant: 320)
        ])
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func bindViewModel() {
        super.bindViewModel()
        chartView.touchable = false
        viewModel?.inputs.updateTabOptions(["1D", "1W", "1M", "3M", "6M"])
    }

    override func didTapChartView() {
        super.didTapChartView()

        // removed ignoreNil and distintict make sure data is consumed when  click to avoid data triiger by order event
        BehaviorRelay.combineLatest(
            _market,
            _productName,
            _productSymbol,
            _productType
        ).take(1).subscribe(onNext: { [weak self] market, name, symbol, type in
            guard let self = self, let market = market, let name = name, let symbol = symbol, let type = type else { return }

            // Present horizontal chart view controller
            let productDetailhorizontalChartViewController = ProductDetailhorizontalChartViewController(market: market, productName: name, productCode: symbol, productType: type, tracker: self.tracker)
            productDetailhorizontalChartViewController.modalPresentationStyle = .fullScreen
            self.viewControllable?.navigationController?.present(productDetailhorizontalChartViewController, animated: true, completion: nil)
        }).disposed(by: bag)
    }

    func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        // presenter life cycle conflicted with RIBs, due to migration process, bounced data if presenter is dispatching nil
        if let market = quote.stockMarket {
            viewModel?.inputs.updateMarket(market)
        }

        if let name = quote.stockName {
            _productName.accept(name)
        }
        if let market = quote.stockMarket {
            _market.accept(market)
        }

        if let code = quote.stockCode {
            _productSymbol.accept(code)
        }

        if let type = quote.productType {
            _productType.accept(type)
        }
    }

    // MARK: - Quote history query section

    func updateSelectedHistoryQueryPeriodSection(period: StockQuoteHistoryPeriod) {
        switch period {
        case .oneDay:
            xAxisLabelFormat = localizedString("date_format_chart_time")
        default:
            xAxisLabelFormat = localizedString("date_format_chart_date")
        }
        viewModel?.inputs.updatePeriod(period)
    }

    // TODO: Create a new struct for containing the data
    func updateHistoryQuerySection(records: [GraphData], error: HSBCErrorCode) {
        // Filter out broken data (without date and OHLC)
        let nonNullRecords = records.filter { $0.date != nil && $0.open != nil && $0.high != nil && $0.low != nil && $0.close != nil }
        if nonNullRecords.count > 1 {
            viewModel?.inputs.updateModuleVisibility(visible: true)
            viewModel?.inputs.updateGraphData(nonNullRecords)
        } else {
            viewModel?.inputs.updateModuleVisibility(visible: false)
        }
    }

    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        if type == .PRESENTER_REQUEST_GET_HISTORY_DATA {
            viewModel?.inputs.updateModuleVisibility(visible: false)
        }
    }
}
