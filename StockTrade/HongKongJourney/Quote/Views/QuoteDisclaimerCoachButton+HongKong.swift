/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import RxSwift
import UIKit

class QuoteDisclaimerCoachButtonHongKong: WXInfoButton, WXViewControllable {
    weak var viewControllable: UIViewController?

    override func bindViewModel() {
        super.bindViewModel()
        didTap.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            let vc = QuoteDisclaimerInfoScreenViewControllerHongKong()
            vc.setListener(self)
            let nav = vc.embedInNavigationController()
            self.viewControllable?.present(nav, animated: true, completion: nil)
        }).disposed(by: bag)
    }

    override func setupAccessibilityIndentifiers() {
        accessibilityLabel = String(format: "%@ %@", localizedString("alt_help"), localizedString("quote_disclaimer_title"))
        setNeedsFocusUpdate()
    }
}

extension QuoteDisclaimerCoachButtonHongKong: WXInfoScreenViewControllerListener {
    func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    func showModule(_ module: WXInfoScreenViewController) {
        /* empty implementation */
    }
}
