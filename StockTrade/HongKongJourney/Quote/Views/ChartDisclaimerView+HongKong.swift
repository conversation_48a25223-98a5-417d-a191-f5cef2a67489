/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileCore
import MobileDesign
import UIKit

class ChartDisclaimerViewHongKong: WXExternalLinkLabelView, StockQuoteView, WXViewControllable {
    weak var viewControllable: UIViewController?
    weak var stockQuotePresenter: StockQuotePresenterImplementation!
    private var market: WXMarket?

    override func initializeVariables() {
        super.initializeVariables()
        backgroundColor = ThemeManager.shared.currentTheme.colors.backgroundStandard
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    func updateHistoryQuerySection(records: [GraphData], error: HSBCErrorCode) {
        let nonNullRecords = records.filter { $0.date != nil && $0.open != nil && $0.high != nil && $0.low != nil && $0.close != nil }
        viewModel?.inputs.updateModuleVisibility(visible: nonNullRecords.count > 1)
    }

    func updateStockQuoteSection(quote: StockQuoteDetail, error: HSBCErrorCode) {
        if let market = quote.stockMarket {
            let highlightString: String
            switch market {
            case .hongKong:
                highlightString = localizedString("quote_aastock")
            case .china, .shangHai, .shenZhen, .america:
                highlightString = localizedString("quote_third_provider")
            case .others, .notSelected, .singapore:
                logStatic("BUG: others, .notSelected shown", level: .debug, namespace: .stockTrade)
                return
            }

            let displayString = String(format: localizedString("quote_full_disclaimer_mark"), highlightString)

            let rangeOfHighlightString = (displayString as NSString).range(of: highlightString)
            let rangeOfFullString = NSRange(location: 0, length: displayString.count)

            let attributedString = NSMutableAttributedString(string: displayString)
            attributedString.addAttribute(NSAttributedString.Key.link, value: "didClickHighlightWords", range: rangeOfHighlightString)
            if let highlightTextColor = highlightTextColor {
                attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: highlightTextColor, range: rangeOfHighlightString)
            }
            if let textFont = textFont {
                attributedString.addAttribute(.font, value: textFont, range: rangeOfFullString)
            }

            viewModel?.inputs.updateAttributedText(value: attributedString)
            if let stockMarket = quote.stockMarket {
                self.market = stockMarket
            }
        }
    }

    override func didClickHighlightWords(_ string: String) {
        super.didClickHighlightWords(string)

        if let viewControllable = self.viewControllable {
            let disclaimerViewController = DisclaimerViewControllerHongKong()
            disclaimerViewController.setListener(self)
            let nvc = disclaimerViewController.embedInNavigationController()
            disclaimerViewController.modalPresentationStyle = .fullScreen
            viewControllable.present(nvc, animated: true, completion: nil)
        }
    }
}

extension ChartDisclaimerViewHongKong: WXInfoScreenViewControllerListener {
    func hideModule(_ module: WXInfoScreenViewController) {
        module.dismiss(animated: true, completion: nil)
    }

    func showModule(_ module: WXInfoScreenViewController) {
        /* empty implementation */
    }
}
