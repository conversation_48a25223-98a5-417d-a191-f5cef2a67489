/** @RIB */
//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import UIKit

class QuoteChartNoDataWarningHongKong: WXInlinedWarningMessageView, StockQuoteView, WXViewControllable {
    weak var viewControllable: UIViewController?
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    override func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
        warningMessage = localizedString("quote_error")
        // supress autolayout by setting the default height
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
        accessibilityIdentifier = "status_view_no_chart_data"
    }

    override func bindViewModel() {
        super.bindViewModel()
        // Hidden by default, shows only if negative conditions hit
        viewModel?.inputs.updateModuleVisibility(visible: false)
    }

    // Mirroring the code implementation @ `QuoteLineChartView`
    func updateHistoryQuerySection(records: [GraphData], error: HSBCErrorCode) {
        // Filter out broken data (without date and OHLC)
        let nonNullRecords = records.filter { $0.date != nil && $0.open != nil && $0.high != nil && $0.low != nil && $0.close != nil }
        if nonNullRecords.count > 1 {
            viewModel?.inputs.updateModuleVisibility(visible: false)
        } else {
            viewModel?.inputs.updateModuleVisibility(visible: true)
        }
    }

    func showErrorMessage(_ type: PresenterRequestType, errorCode: HSBCErrorCode, warnings: [HSBCWarning]?) {
        if type == .PRESENTER_REQUEST_GET_HISTORY_DATA, errorCode != .noError {
            viewModel?.inputs.updateModuleVisibility(visible: true)
        }
    }
}
