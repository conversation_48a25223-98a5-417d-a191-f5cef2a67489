/** @RIB */
//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
import UIKit

class QuoteLoadingIndicatorHongKong: WXLoadingIndicator, StockQuoteView, WXViewControllable {
    weak var viewControllable: UIViewController?
     
    weak var stockQuotePresenter: StockQuotePresenterImplementation!

    override func setupAccessibilityIndentifiers() {
        super.setupAccessibilityIndentifiers()
    }

    override func initializeVariables() {
        super.initializeVariables()
        stockQuotePresenter = StockQuotePresenterImplementation.configure(view: self, routerImplementation: nil)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        // Biz Logic
        stockQuotePresenter?.viewDidLoad(view: self)
    }

    override func viewDeinit() {
        super.viewDeinit()
        stockQuotePresenter?.viewDeinit(view: self)
    }

    override func setupViews() {
        super.setupViews()
        // ribs specific
        loadingDataLabel.isHidden = false
    }

    override func bindViewModel() {
        super.bindViewModel()
        viewModel?.inputs.updateModuleVisibility(visible: true)
    }

    func showLoadingIndicator(_ type: PresenterRequestType) {
        if type == .PRESENTER_REQUEST_GET_QUOTE {
            viewModel?.inputs.updateModuleVisibility(visible: true)
        }
    }

    func hideLoadingIndicator(_ type: PresenterRequestType) {
        viewModel?.inputs.updateModuleVisibility(visible: false)
    }
}
