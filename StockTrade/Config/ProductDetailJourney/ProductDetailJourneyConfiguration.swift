//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import Foundation
import MobileCore

public class ProductDetailJourneyConfiguration {
    var tpHTTPClient: MobileCore.HTTPClient?
    var wealthITHTTPClient: MobileCore.HTTPClient?
    var networkClient: Networking
    var wealthITNetworkClient: Networking
    var apiEnvironment: HSBCEnvironment
    var applicationName: String
    var stockMarket: HSBCMarket
    var stockCode: String
    var stockName: String
    var productType: HSBCInvestmentProductType
    var productDetailEndJourneyCompletionHandler: WealthXJourneyDefaultCompletionHandler
    var stockSellEndJourneyCompletionHandler: WealthXJourneyDefaultCompletionHandler
    var stockBuyEndJourneyCompletionHandler: WealthXJourneyDefaultCompletionHandler
    var locale: HSBCLocaleType
    var tracker: Tracking!
    
    public init(networkClient: Networking, wealthITNetworkClient: Networking, apiEnvironment: HSBCEnvironment, applicationName: String, stockMarket: HSBCMarket, stockCode: String, stockName: String, productType: HSBCInvestmentProductType, locale: HSBCLocaleType, tracker: Tracking, productDetailEndJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler, stockSellEndJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler, stockBuyEndJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler) {
        self.networkClient = networkClient
        self.wealthITNetworkClient = wealthITNetworkClient
        self.apiEnvironment = apiEnvironment
        self.applicationName = applicationName
        self.stockMarket = stockMarket
        self.stockCode = stockCode
        self.stockName = stockName
        self.productType = productType
        self.locale = locale
        self.productDetailEndJourneyCompletionHandler = productDetailEndJourneyCompletionHandler
        self.stockSellEndJourneyCompletionHandler = stockSellEndJourneyCompletionHandler
        self.stockBuyEndJourneyCompletionHandler = stockBuyEndJourneyCompletionHandler
        self.tracker = tracker
    }
    
    
    public init(tpHTTPClient: MobileCore.HTTPClient, wealthITHTTPClient: MobileCore.HTTPClient, apiEnvironment: HSBCEnvironment, applicationName: String, stockMarket: HSBCMarket, stockCode: String, stockName: String, productType: HSBCInvestmentProductType, locale: HSBCLocaleType, tracker: Tracking, productDetailEndJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler, stockSellEndJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler, stockBuyEndJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler) {
        self.tpHTTPClient = tpHTTPClient
        self.wealthITHTTPClient = wealthITHTTPClient
        networkClient = Network(httpClient: tpHTTPClient)
        wealthITNetworkClient = Network(httpClient: wealthITHTTPClient)
        self.apiEnvironment = apiEnvironment
        self.applicationName = applicationName
        self.stockMarket = stockMarket
        self.stockCode = stockCode
        self.stockName = stockName
        self.productType = productType
        self.locale = locale
        self.productDetailEndJourneyCompletionHandler = productDetailEndJourneyCompletionHandler
        self.stockSellEndJourneyCompletionHandler = stockSellEndJourneyCompletionHandler
        self.stockBuyEndJourneyCompletionHandler = stockBuyEndJourneyCompletionHandler
        self.tracker = tracker
    }
    
}
