//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileCore
// import wealthxcore
import UIKit

public struct WealthXData {
    public var market: WXMarket?
    public var investmentAccount: WXInvestmentAccount?
}

public enum WXConfigRegion {
    case hk
    case sg
}

public typealias WealthXJourneyCompletionHandler = ((_ withIntent: WealthXJourneyExitIntent, _ data: WealthXData?) -> Void)

typealias WealthXJourneyTransitionHandler = ((_ withIntent: WealthXJourneyTransitIntent, _ configuration: JourneyConfiguration?, _ endJourneyCompletionHandler: WealthXJourneyCompletionHandler?) -> UIViewController?)

public class JourneyConfiguration: Hashable {
    public static func == (lhs: JourneyConfiguration, rhs: JourneyConfiguration) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    public func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }

    var _networkClient: Networking
    var _wealthITNetworkClient: Networking
    var networkClient: HTTPClient
    var wealthITNetworkClient: HTTPClient
    var accountsProxy: String
    var ordersProxy: String
    var productsProxy: String
    var wealthITProxy: String
    var topMoversProxy: String

    var applicationName: String
    var stockMarket: WXMarket
    var endJourneyCompletionHandler: WealthXJourneyCompletionHandler?
    var locale: WXLocaleType
    var tracker: WXTracking
    var sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult
    var priorInvestmentAccount: WXInvestmentAccount?
    var priorSettlementAccount: WXSettlementAccount?
    var region: WXConfigRegion

    public init(
        networkClient: HTTPClient,
        wealthITNetworkClient: HTTPClient,
        accountsProxy: String,
        ordersProxy: String,
        productsProxy: String,
        wealthITProxy: String,
        topMoversProxy: String,
        applicationName: String,
        stockMarket: WXMarket,
        locale: WXLocaleType,
        tracker: WXTracking,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult,
        priorInvestmentAccount: WXInvestmentAccount?,
        priorSettlementAccount: WXSettlementAccount?,
        region: WXConfigRegion
    ) {
        self.networkClient = networkClient
        self.wealthITNetworkClient = wealthITNetworkClient
        _networkClient = Network(httpClient: self.networkClient)
        _wealthITNetworkClient = Network(httpClient: self.wealthITNetworkClient)
        self.accountsProxy = accountsProxy
        self.ordersProxy = ordersProxy
        self.productsProxy = productsProxy
        self.wealthITProxy = wealthITProxy
        self.topMoversProxy = topMoversProxy
        self.applicationName = applicationName
        self.stockMarket = stockMarket
        self.locale = locale
        self.tracker = tracker
        self.sfcRegulatoryCheckingResult = sfcRegulatoryCheckingResult
        self.priorInvestmentAccount = priorInvestmentAccount
        self.priorSettlementAccount = priorSettlementAccount
        self.region = region
    }

    public func copy(with zone: NSZone? = nil) -> Any {
        let copy = JourneyConfiguration(self)
        return copy
    }

    convenience init(_ configuration: JourneyConfiguration) {
        self.init(
            networkClient: configuration.networkClient,
            wealthITNetworkClient: configuration.wealthITNetworkClient,
            accountsProxy: configuration.accountsProxy,
            ordersProxy: configuration.ordersProxy,
            productsProxy: configuration.productsProxy,
            wealthITProxy: configuration.wealthITProxy,
            topMoversProxy: configuration.topMoversProxy,
            applicationName: configuration.applicationName,
            stockMarket: configuration.stockMarket,
            locale: configuration.locale,
            tracker: configuration.tracker,
            sfcRegulatoryCheckingResult: configuration.sfcRegulatoryCheckingResult,
            priorInvestmentAccount: configuration.priorInvestmentAccount,
            priorSettlementAccount: configuration.priorSettlementAccount,
            region: configuration.region
        )
        endJourneyCompletionHandler = configuration.endJourneyCompletionHandler
    }

    public convenience init(
        networkClient: HTTPClient,
        wealthITNetworkClient: HTTPClient,
        accountsProxy: String,
        ordersProxy: String,
        productsProxy: String,
        wealthITProxy: String,
        topMoversProxy: String,
        applicationName: String,
        stockMarket: WXMarket,
        locale: WXLocaleType,
        tracker: WXTracking,
        sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult,
        priorInvestmentAccount: WXInvestmentAccount?,
        priorSettlementAccount: WXSettlementAccount?,
        region: WXConfigRegion,
        endJourneyCompletionHandler: WealthXJourneyCompletionHandler?
    ) {
        self.init(networkClient: networkClient, wealthITNetworkClient: wealthITNetworkClient, accountsProxy: accountsProxy, ordersProxy: ordersProxy, productsProxy: productsProxy, wealthITProxy: wealthITProxy, topMoversProxy: topMoversProxy, applicationName: applicationName, stockMarket: stockMarket, locale: locale, tracker: tracker, sfcRegulatoryCheckingResult: sfcRegulatoryCheckingResult, priorInvestmentAccount: priorInvestmentAccount, priorSettlementAccount: priorSettlementAccount, region: region)
        self.endJourneyCompletionHandler = endJourneyCompletionHandler
    }

//    convenience init(networkClient: HTTPClient,
//                            wealthITNetworkClient: HTTPClient,
//                            apiEnvironment: HSBCEnvironment = .production,
//                            applicationName: String,
//                            stockMarket: HSBCMarket,
//                            locale: HSBCLocaleType,
//                            tracker: WXTracking,
//                            sfcRegulatoryCheckingResult: SFCRegulatoryCheckingResult,
//                            priorInvestmentAccount: HSBCInvestmentAccount?,
//                            priorSettlementAccount: HSBCSettlementAccount?,
//                            endJourneyCompletionHandler: @escaping WealthXJourneyCompletionHandler) {
//        self.init(networkClient: Network(httpClient: networkClient), wealthITNetworkClient: Network(httpClient: wealthITNetworkClient), apiEnvironment: apiEnvironment, applicationName: applicationName, stockMarket: stockMarket, locale: locale, tracker: tracker, sfcRegulatoryCheckingResult: sfcRegulatoryCheckingResult, priorInvestmentAccount: priorInvestmentAccount, priorSettlementAccount: priorSettlementAccount)
//        self.endJourneyCompletionHandler = endJourneyCompletionHandler
//    }
}
