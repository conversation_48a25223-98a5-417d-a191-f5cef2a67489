//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import MobileCore
// import wealthxcore

public typealias WealthXJourneyDefaultCompletionHandler = ((_ withIntent: WealthXJourneyExitIntent, _ configuration: Configuration?) -> Void)

public class Configuration {
    var networkClient: Networking
    var wealthITNetworkClient: Networking
    var apiEnvironment: HSBCEnvironment
    var applicationName: String
    var stockMarket: HSBCMarket
    var stockCode: String
    var productType: HSBCInvestmentProductType
    var endJourneyCompletionHandler: WealthXJourneyDefaultCompletionHandler?
    var locale: HSBCLocaleType
    var tracker: Tracking!
    var priorSettlementAccount: PriorAccount?
    var priorInvestmentAccount: PriorAccount?

    public init(networkClient: Networking,
                wealthITNetworkClient: Networking,
                apiEnvironment: HSBCEnvironment = .production,
                applicationName: String,
                stockMarket: HSBCMarket,
                stockCode: String,
                productType: HSBCInvestmentProductType,
                locale: HSBCLocaleType,
                tracker: Tracking) {
        self.networkClient = networkClient
        self.wealthITNetworkClient = wealthITNetworkClient
        self.apiEnvironment = apiEnvironment
        self.applicationName = applicationName
        self.stockMarket = stockMarket
        self.stockCode = stockCode
        self.productType = productType
        self.locale = locale
        StockStrings.setLocale(locale: locale.rawValue)
        self.tracker = tracker
    }
    
    public convenience init(networkClient: MobileCore.HTTPClient,
                            wealthITNetworkClient: MobileCore.HTTPClient,
                            apiEnvironment: HSBCEnvironment = .production,
                            applicationName: String,
                            stockMarket: HSBCMarket,
                            stockCode: String,
                            productType: HSBCInvestmentProductType,
                            locale: HSBCLocaleType,
                            tracker: Tracking,
                            endJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler) {
        self.init(networkClient: Network(httpClient: networkClient), wealthITNetworkClient: Network(httpClient: wealthITNetworkClient), apiEnvironment: apiEnvironment, applicationName: applicationName, stockMarket: stockMarket, stockCode: stockCode, productType: productType, locale: locale, tracker: tracker)
        self.endJourneyCompletionHandler = endJourneyCompletionHandler
    }
    
    public convenience init(networkClient: MobileCore.HTTPClient,
                            wealthITNetworkClient: MobileCore.HTTPClient,
                            apiEnvironment: HSBCEnvironment = .production,
                            applicationName: String,
                            stockMarket: HSBCMarket,
                            stockCode: String,
                            productType: HSBCInvestmentProductType,
                            locale: HSBCLocaleType,
                            tracker: Tracking,
                            priorSettlementAccount: PriorAccount?,
                            priorInvestmentAccount: PriorAccount?,
                            endJourneyCompletionHandler: @escaping WealthXJourneyDefaultCompletionHandler) {
        self.init(networkClient: Network(httpClient: networkClient), wealthITNetworkClient: Network(httpClient: wealthITNetworkClient), apiEnvironment: apiEnvironment, applicationName: applicationName, stockMarket: stockMarket, stockCode: stockCode, productType: productType, locale: locale, tracker: tracker)
        
        self.endJourneyCompletionHandler = endJourneyCompletionHandler
        self.priorSettlementAccount = priorSettlementAccount
        self.priorInvestmentAccount = priorInvestmentAccount
    }
}
