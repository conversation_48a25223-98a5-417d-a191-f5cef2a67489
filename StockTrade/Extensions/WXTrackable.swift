//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation

public protocol WXTrackable {
    var tracker: WXTracking { get }
}

extension WXTrackable {
    public var tracker: WXTracking {
        // We need to support the old way of doing things
        // Hence, try to resolve the tracker if it can't be resolved
        // Then do it the original way.
        guard let tracker = Resolver.optional(WXTracking.self) else {
            return StockTradeJourneyFactory.trackManager
        }
        return tracker
    }
}
