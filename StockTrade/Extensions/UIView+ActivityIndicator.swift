//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift
import UIKit
public protocol ActivityIndicatorProtocol {
    var activityIndicator: LoadingSpinner? { get set }
    func showActivityIndicator(_ show: Bool)
}

open class UIViewActivityIndicator: UIView, ActivityIndicatorProtocol {
    public var activityIndicator: LoadingSpinner?
    public func showActivityIndicator(_ show: Bool) {
        if show {
            if activityIndicator == nil {
                isUserInteractionEnabled = false
                activityIndicator = LoadingSpinner(size: .medium, appearanceType: .dark, styleProvider: { $0.defaultLoadingSpinnerStyle })
                activityIndicator?.startAnimating()
                activityIndicator?.translatesAutoresizingMaskIntoConstraints = false
            }

            guard let activityIndicator = activityIndicator else {
                return
            }

            addSubview(activityIndicator)
            NSLayoutConstraint.activate([
                activityIndicator.centerXAnchor.constraint(equalTo: centerXAnchor),
                activityIndicator.centerYAnchor.constraint(equalTo: centerYAnchor)
            ])
        } else {
            isUserInteractionEnabled = true
            activityIndicator?.removeFromSuperview()
        }
    }
}

extension Reactive where Base: UIViewActivityIndicator {
    var showIndicator: Binder<Bool> {
        Binder(base) { view, showIndicator in
            view.showActivityIndicator(showIndicator)
        }
    }
}
