//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

protocol WXViewControllable {
    var viewControllable: UIViewController? { get set }
    mutating func setViewControllable(_ viewControllable: UIViewController?)
}

extension WXViewControllable {
    mutating func setViewControllable(_ viewControllable: UIViewController?){
        self.viewControllable = viewControllable
    }
}
