//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

extension UIContentSizeCategory {
    public var scaleInPercentage: Int {
        var percentage: Int = 100
        switch self {
        case .extraSmall:
            percentage = 70

        case .small:
            percentage = 80

        case .medium:
            percentage = 90

        case .large:
            percentage = 100

        case .extraLarge:
            percentage = 120

        case .extraExtraLarge:
            percentage = 140

        case .extraExtraExtraLarge:
            percentage = 150

        // Accessibility percentages

        case .accessibilityMedium:
            percentage = 160

        case .accessibilityLarge:
            percentage = 170

        case .accessibilityExtraLarge:
            percentage = 180

        case .accessibilityExtraExtraLarge:
            percentage = 190

        case .accessibilityExtraExtraExtraLarge:
            percentage = 200
        default:
            break
        }
        return percentage
    }

    public var htmlFontSize: String {
        "\(scaleInPercentage)%"
    }
}
