//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import UIKit

public extension String {
    var isPureNumber: Bool {
        let pattern = "[0-9]*"
        if let range = self.range(of: pattern, options: .regularExpression) {
            let isNumeric = self[range].count == count
            return isNumeric
        }
        return false
    }

    func dateWithDateFormat(_ format: String, timeZone: TimeZone = TimeZone.current, locale: Locale = Locale(identifier: StockTradeJourneyFactory.locale.rawValue)) -> Date? {
        let formatter = GregorianDateFormatter()
        formatter.dateFormat = format
        formatter.timeZone = timeZone
        formatter.locale = locale
        return formatter.date(from: self)
    }

    var isBlank: Bool {
        let trimmedStr = trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmedStr.isEmpty
    }
}
