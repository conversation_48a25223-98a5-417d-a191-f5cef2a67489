//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import Foundation
import MobileDesign
import RxCocoa
import RxSwift
import UIKit

class UIStackViewActivityIndicator: UIStackView, ActivityIndicatorProtocol {
    var activityIndicator: LoadingSpinner?
    func showActivityIndicator(_ show: Bool) {
        if show {
            if activityIndicator == nil {
                activityIndicator = LoadingSpinner(size: .medium, appearanceType: .dark)
                activityIndicator?.startAnimating()
                activityIndicator?.translatesAutoresizingMaskIntoConstraints = false
            }
            
            guard let activityIndicator = activityIndicator else {
                return
            }
            
            addSubview(activityIndicator)
            NSLayoutConstraint.activate([
                activityIndicator.centerXAnchor.constraint(equalTo: centerXAnchor),
                activityIndicator.centerYAnchor.constraint(equalTo: centerYAnchor),
            ])
        } else {
            activityIndicator?.removeFromSuperview()
        }
        
    }
}

extension Reactive where Base: UIStackViewActivityIndicator {
  var showIndicator: Binder<Bool> {
    return Binder(base) { view, showIndicator in
        view.showActivityIndicator(showIndicator)
    }
  }
}

class UIStackViewActivityIndicatorWithLabel: UIStackViewActivityIndicator {
    var loadingDataLabel: UILabel?
    override func showActivityIndicator(_ show: Bool) {
        super.showActivityIndicator(show)
        if show {
            if loadingDataLabel == nil {
                loadingDataLabel = UILabel()
                loadingDataLabel?.translatesAutoresizingMaskIntoConstraints = false
                loadingDataLabel?.font = ThemeManager.shared.currentTheme.fonts.body
                loadingDataLabel?.textColor = ThemeManager.shared.currentTheme.colors.textPrimary
                loadingDataLabel?.text = localizedString("generic_loading_data")
                loadingDataLabel?.textAlignment = .center
                loadingDataLabel?.numberOfLines = 1
            }
            guard let loadingDataLabel = loadingDataLabel,
                let activityIndicator = activityIndicator else {
                    return
            }
            addSubview(loadingDataLabel)
            NSLayoutConstraint.activate([
                loadingDataLabel.topAnchor.constraint(equalTo: activityIndicator.bottomAnchor, constant: 10),
                loadingDataLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            ])
        } else {
            loadingDataLabel?.removeFromSuperview()
        }
    }
}
