//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

// MARK: - Auto Layout

extension UIView {
    func layoutMatchParent(parentView: UIView, edgeInsets: UIEdgeInsets = UIEdgeInsets.zero) {
        translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            leadingAnchor.constraint(equalTo: parentView.leadingAnchor, constant: edgeInsets.left),
            topAnchor.constraint(equalTo: parentView.topAnchor, constant: edgeInsets.top),
            trailingAnchor.constraint(equalTo: parentView.trailingAnchor, constant: edgeInsets.right),
            bottomAnchor.constraint(equalTo: parentView.bottomAnchor, constant: edgeInsets.bottom)
        ])
    }

    func layoutLeftOf(view: UIView, minSpacing: CGFloat, flexible: Bool = false) -> UIView {
        if flexible {
            NSLayoutConstraint.activate([
                view.leadingAnchor.constraint(greaterThanOrEqualTo: trailingAnchor, constant: minSpacing)
            ])
        } else {
            NSLayoutConstraint.activate([
                view.leadingAnchor.constraint(equalTo: trailingAnchor, constant: minSpacing)
            ])
        }

        return self
    }

    func layoutAboveOf(view: UIView, minSpacing: CGFloat, fexible: Bool = false) -> UIView {
        if fexible {
            NSLayoutConstraint.activate([
                view.topAnchor.constraint(greaterThanOrEqualTo: bottomAnchor, constant: minSpacing)
            ])
        } else {
            NSLayoutConstraint.activate([
                view.topAnchor.constraint(equalTo: bottomAnchor, constant: minSpacing)
            ])
        }

        return self
    }

    func layoutRightOf(view: UIView, minSpacing: CGFloat, fexible: Bool = false) -> UIView {
        if fexible {
            NSLayoutConstraint.activate([
                leadingAnchor.constraint(greaterThanOrEqualTo: view.trailingAnchor, constant: minSpacing)
            ])
        } else {
            NSLayoutConstraint.activate([
                leadingAnchor.constraint(equalTo: view.trailingAnchor, constant: minSpacing)
            ])
        }

        return self
    }

    func layoutBottomOf(view: UIView, minSpacing: CGFloat, fexible: Bool = false) -> UIView {
        if fexible {
            NSLayoutConstraint.activate([
                topAnchor.constraint(greaterThanOrEqualTo: view.bottomAnchor, constant: minSpacing)
            ])
        } else {
            NSLayoutConstraint.activate([
                topAnchor.constraint(equalTo: view.bottomAnchor, constant: minSpacing)
            ])
        }

        return self
    }

    func layoutAlignLeftFor(views: [UIView], constant: CGFloat = 0) -> UIView {
        let constraints = views.map {
            $0.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: constant)
        }
        NSLayoutConstraint.activate(constraints)

        return self
    }

    func layoutAlignTopFor(views: [UIView], constant: CGFloat = 0) -> UIView {
        let constraints = views.map {
            $0.topAnchor.constraint(equalTo: self.topAnchor, constant: constant)
        }
        NSLayoutConstraint.activate(constraints)

        return self
    }

    func layoutAlignRightFor(views: [UIView], constant: CGFloat = 0) -> UIView {
        let constraints = views.map {
            $0.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: constant)
        }
        NSLayoutConstraint.activate(constraints)

        return self
    }

    func layoutAlignBottomFor(views: [UIView], constant: CGFloat = 0) -> UIView {
        let constraints = views.map {
            $0.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: constant)
        }
        NSLayoutConstraint.activate(constraints)

        return self
    }

    func allSubViewsOf<T: UIView>(type: T.Type) -> [T] {
        var all = [T]()
        func getSubview(view: UIView) {
            if let aView = view as? T {
                all.append(aView)
            }
            guard view.subviews.count > 0 else { return }
            view.subviews.forEach { getSubview(view: $0) }
        }
        getSubview(view: self)
        return all
    }

    func getSubviewsOf<T: UIView>(view: UIView) -> [T] {
        var subviews = [T]()
        for subview in view.subviews {
            subviews += getSubviewsOf(view: subview) as [T]
            if let subview = subview as? T {
                subviews.append(subview)
            }
        }
        return subviews
    }
}
