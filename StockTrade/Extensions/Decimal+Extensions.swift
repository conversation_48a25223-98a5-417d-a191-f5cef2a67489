//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import Foundation

extension Decimal {
    mutating func round(_ scale: Int, _ roundingMode: NSDecimalNumber.RoundingMode) {
        var localCopy = self
        NSDecimalRound(&self, &localCopy, scale, roundingMode)
    }

    func rounded(_ scale: Int, _ roundingMode: NSDecimalNumber.RoundingMode) -> Decimal {
        var result = Decimal()
        var localCopy = self
        NSDecimalRound(&result, &localCopy, scale, roundingMode)
        return result
    }

    static func divide(_ numerator: Decimal, _ denominator: Decimal) -> Decimal {
        return (numerator / denominator).rounded(0, .down)
    }

    static func mod(_ a: Decimal, _ b: Decimal) -> Decimal {
        var d = a / b
        var f: Decimal = 0
        NSDecimalRound(&f, &d, 0, .down)
        return a - (b * f)
    }
}
