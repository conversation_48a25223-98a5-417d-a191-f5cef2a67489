//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

/**
 STM-6238
 [STMA2.0][iOS][HK]QuantityInput] Cannot dismiss quantity input panel by swipe down
 */

open class DismissableViewController: UIViewController {
    var viewTranslation = CGPoint(x: 0, y: 0)
    override public func viewDidLoad() {
        super.viewDidLoad()

        let panGestureRecognizer = UIPanGestureRecognizer(target: self, action: #selector(handleDismiss))
        panGestureRecognizer.delegate = self

        let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(handleTapToDismiss))
        tapGestureRecognizer.delegate = self

        view.addGestureRecognizer(panGestureRecognizer)
        view.addGestureRecognizer(tapGestureRecognizer)
    }

    @objc private func handleTapToDismiss(sender: UITapGestureRecognizer) {
        switch sender.state {
        case .ended:
            handleDragOrTapToDismiss()
        default:
            break
        }
    }

    @objc private func handleDismiss(sender: UIPanGestureRecognizer) {
        // The rest of the code we will write here
        switch sender.state {
        case .changed:
            viewTranslation = sender.translation(in: view)
            if viewTranslation.y > 0 {
                UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 1, options: .curveEaseOut, animations: { [weak self] in
                    guard let self = self else { return }
                    self.view.transform = CGAffineTransform(translationX: 0, y: self.viewTranslation.y)
                })
            } else {
                viewTranslation.y = 0
            }
        case .ended:
            if viewTranslation.y < 200 {
                UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 1, options: .curveEaseOut, animations: { [weak self] in
                    guard let self = self else { return }
                    self.view.transform = .identity
                    self.viewTranslation.y = 0
                })
            } else {
                handleDragOrTapToDismiss()
            }
        default:
            break
        }
    }

    open func handleDragOrTapToDismiss() {
        dismiss(animated: true, completion: nil)
    }
}

extension DismissableViewController: UIGestureRecognizerDelegate {
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if touch.view == gestureRecognizer.view, gestureRecognizer.isKind(of: UITapGestureRecognizer.self) {
            return true
        }
        if gestureRecognizer.isKind(of: UIPanGestureRecognizer.self) {
            return viewTranslation.y == 0
        }

        return false
    }
}
