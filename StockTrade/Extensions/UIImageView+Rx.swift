//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//

import Foundation
import RxCocoa
import RxSwift
import UIKit

extension Reactive where Base: UIImageView {
    
    /// Bindable sink for `attributedString` property
    var tintColor: Binder<UIColor?> {
        return Binder(base) { view, attr in
            if let color = attr {
                view.tintColor = color
            }
        }
    }
        
}
