//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import UIKit

extension UIViewController {
    func isVisible() -> Bool {
        isViewLoaded && view.window != nil
    }

    func presentViewCotrollerWithNavbar(viewController: UIViewController, completion: (() -> Void)? = nil) {
        let navVC = WXCustomNavigationController()
        navVC.viewControllers = [viewController]
        navVC.modalPresentationStyle = .fullScreen
        present(navVC, animated: true, completion: completion)
    }

    func add(_ child: UIViewController, frame: CGRect? = nil) {
        addChild(child)

        if let frame = frame {
            child.view.frame = frame
        }

        view.addSubview(child.view)
        child.didMove(toParent: self)
    }

    func remove() {
        // Just to be safe, we check that this view controller
        // is actually added to a parent before removing it.
        guard parent != nil else {
            return
        }

        willMove(toParent: nil)
        view.removeFromSuperview()
        removeFromParent()
    }
}
