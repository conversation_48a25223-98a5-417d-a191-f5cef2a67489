//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

//
//  Copyright © 2020 HSBC Bank plc. All rights reserved.
//
import Foundation

public func localizedString(_ key: String) -> String {
    let locale = StockTradeJourneyFactory.locale.rawValue
    let hostAppBundlePath = Bundle.main.path(forResource: locale, ofType: "lproj")
    if let path = hostAppBundlePath, let bundle = Bundle(path: path) {
        let string = bundle.localizedString(forKey: key, value: key, table: "Localization")
        if !string.contains(key) {
            return string
        }
    }

    if let path = Bundle.signature.path(forResource: locale, ofType: "lproj"),
        let bundle = Bundle(path: path) {
        return bundle.localizedString(forKey: key, value: key, table: "Localization")
    }
    return key
}
