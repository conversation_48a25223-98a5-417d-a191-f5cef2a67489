//
//  Copyright © 2019 HSBC Bank plc. All rights reserved.
//

import UIKit

extension UIStackView {
    func removeArrangedSubview(_ view: UIView?) {
        guard let view = view else { return }
        if arrangedSubviews.contains(view) {
            view.removeFromSuperview()
            removeArrangedSubview(view)
        }
    }
    
    func insertArrangedSubview(_ view: UIView?, at index: Int = 0) {
        guard let view = view else { return }
        if arrangedSubviews.contains(view) {
            view.removeFromSuperview()
            removeArrangedSubview(view)
        }
        insertArrangedSubview(view, at: index)
    }
    
    @discardableResult
    func removeAllArrangedSubviews() -> [UIView] {
        return arrangedSubviews.reduce([UIView]()) { $0 + [removeArrangedSubViewProperly($1)] }
    }
    
    func removeArrangedSubViewProperly(_ view: UIView) -> UIView {
        removeArrangedSubview(view)
        NSLayoutConstraint.deactivate(view.constraints)
        view.removeFromSuperview()
        return view
    }
}
