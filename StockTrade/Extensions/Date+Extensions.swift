//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation

extension Date {
    var startOfDay: Date {
        Calendar.gregorian.startOfDay(for: self)
    }

    func dateString(timeZone: TimeZone = TimeZone.current) -> String {
        let formatter = GregorianDateFormatter()
        formatter.locale = Locale(identifier: "en")
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = timeZone
        return formatter.string(from: self)
    }

    func stringWithDateFormat(_ format: String, timeZone: TimeZone = TimeZone.current, locale: Locale = Locale(identifier: StockTradeJourneyFactory.locale.rawValue)) -> String {
        let formatter = GregorianDateFormatter()
        formatter.dateFormat = format
        formatter.timeZone = timeZone
        formatter.locale = locale
        return formatter.string(from: self)
    }
}
