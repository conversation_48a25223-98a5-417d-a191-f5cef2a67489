//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import XCTest

class AccountInvestmentInteractorImplementationTests: XCTestCase {
    var interactor: AccountInvestmentInteractorImplementation?

    override func setUp() {
        // Put setup code here. This method is called before the invocation of each test method in the class.
        interactor = AccountInvestmentInteractorImplementation()
    }

    override func tearDown() {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        interactor = nil
    }

    func test_getRequiredDocuments_happyFlowWith200() {
        let network = MockNetworking<RequiredDocumentRequest>(jsonFileName: "MOCKAccountInvestmentGetRequiredDocumentsHK")
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: [(WXInvestmentAccount(type: .ca1, number: "1", currency: .hongKongDollar, checksum: "1"), [HSBCDocumentDetail(documentTypeCode: .exb, eligibleTradingAccountIndicator: nil, tradingEligibilityExpiryDate: nil, expiryStatus: nil)])], documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getRequiredDocuments(entity: entity, completionHandler: handler)

        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.integrated380AccountsDocuments?.isEmpty == false)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getRequiredDocuments_happyFlowWith400() {
        let network = MockNetworking<RequiredDocumentRequest>(jsonFileName: "")
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        network.responseCode = 400
        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: [(WXInvestmentAccount(type: .ca1, number: "1", currency: .hongKongDollar, checksum: "1"), [HSBCDocumentDetail(documentTypeCode: .exb, eligibleTradingAccountIndicator: nil, tradingEligibilityExpiryDate: nil, expiryStatus: nil)])], documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getRequiredDocuments(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getRequiredDocuments_happyFlowWith500() {
        let network = MockNetworking<RequiredDocumentRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: [(WXInvestmentAccount(type: .ca1, number: "1", currency: .hongKongDollar, checksum: "1"), [HSBCDocumentDetail(documentTypeCode: .exb, eligibleTradingAccountIndicator: nil, tradingEligibilityExpiryDate: nil, expiryStatus: nil)])], documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getRequiredDocuments(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getAccount_with200() {
        let network = MockNetworking<AccountInvestmentListRequest>(jsonFileName: "MOCKAccountInvestmentGetAccountsHK")
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: [(WXInvestmentAccount(type: .ca1, number: "1", currency: .hongKongDollar, checksum: "1"), [HSBCDocumentDetail(documentTypeCode: .exb, eligibleTradingAccountIndicator: nil, tradingEligibilityExpiryDate: nil, expiryStatus: nil)])], documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getAccounts(entity: entity, completionHandler: handler)

        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.accounts?.isEmpty == false)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getAccount_with400() {
        let network = MockNetworking<AccountInvestmentListRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: [(WXInvestmentAccount(type: .ca1, number: "1", currency: .hongKongDollar, checksum: "1"), [HSBCDocumentDetail(documentTypeCode: .exb, eligibleTradingAccountIndicator: nil, tradingEligibilityExpiryDate: nil, expiryStatus: nil)])], documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getAccounts(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getAccount_with500() {
        let network = MockNetworking<AccountInvestmentListRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: [(WXInvestmentAccount(type: .ca1, number: "1", currency: .hongKongDollar, checksum: "1"), [HSBCDocumentDetail(documentTypeCode: .exb, eligibleTradingAccountIndicator: nil, tradingEligibilityExpiryDate: nil, expiryStatus: nil)])], documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getAccounts(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getCustomerConsentInfo_with200() {
        let network = MockNetworking<CustomerConsentInfoRequest>(jsonFileName: "MOCKAccountInvestmentGetCustomerConsentInfoHK")
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: nil, documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getCustomerConsentInfo(entity: entity, completionHandler: handler)

        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.bcanConsentStatus != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getCustomerConsentInfo_with400() {
        let network = MockNetworking<CustomerConsentInfoRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: nil, documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getCustomerConsentInfo(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getCustomerConsentInfo_with500() {
        let network = MockNetworking<CustomerConsentInfoRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: nil, documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: nil, stockConnectConsentStatus: nil, proposedBcanConsentStatus: nil, proposedStockConnectConsentStatus: nil, sgConsentStatus: nil)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.getCustomerConsentInfo(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_submitCustomerConsent_with200() {
        let network = MockNetworking<CustomerConsentSubmitRequest>(jsonFileName: "MOCKAccountInvestmentSubmitCustomerConsentHK")
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: nil, documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: .optIn, stockConnectConsentStatus: .optIn, proposedBcanConsentStatus: .optIn, proposedStockConnectConsentStatus: .optIn, sgConsentStatus: .optIn)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.submitCustomerConsent(entity: entity, completionHandler: handler)

        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.bcanConsentStatus != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_submitCustomerConsent_with400() {
        let network = MockNetworking<CustomerConsentSubmitRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: nil, documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: .optIn, stockConnectConsentStatus: .optIn, proposedBcanConsentStatus: .optIn, proposedStockConnectConsentStatus: .optIn, sgConsentStatus: .optIn)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.submitCustomerConsent(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_submitCustomerConsent_with500() {
        let network = MockNetworking<CustomerConsentSubmitRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        let entity = AccountInvestmentEntity(market: WXMarket.hongKong, accounts: nil, eligibleTradeInvestmentAccounts: nil, priorInvestmentAccount: nil, eligibleTradeIntegrated380Accounts: nil, a380NYSEValidOnlyInvestmentAccounts: nil, a380W8BenValidOnlyInvestmentAccounts: nil, integrated380AccountsDocuments: nil, documentsSubmitStatusList: nil, sfcRegulatoryCheckingResult: nil, bcanConsentStatus: .optIn, stockConnectConsentStatus: .optIn, proposedBcanConsentStatus: .optIn, proposedStockConnectConsentStatus: .optIn, sgConsentStatus: .optIn)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: AccountInvestmentEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: AccountInvestmentDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }
        interactor?.submitCustomerConsent(entity: entity, completionHandler: handler)

        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.market, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }
}
