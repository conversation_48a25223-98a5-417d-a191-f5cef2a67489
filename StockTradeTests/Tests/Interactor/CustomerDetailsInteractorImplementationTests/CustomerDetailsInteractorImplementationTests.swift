//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import XCTest

class CustomerDetailsInteractorImplementationTests: XCTestCase {
    var interactor: CustomerDetailsInteractorImplementation?

    override func setUp() {
        // Put setup code here. This method is called before the invocation of each test method in the class.
        interactor = CustomerDetailsInteractorImplementation()
    }

    override func tearDown() {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        interactor = nil
    }

    func test_getCustomerDetailsStatus_happyFlow() {
        interactor?.setNetworkClient(networkClient: nil, wealthITNetworkClient: nil, accountsProxy: "mmw-wealthx-hk-accounts-cert-proxy/v1", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        StockTradeJourneyFactory.pvcStatus = .block
        let entity = CustomerDetailsEntity(status: VulnerableCustomerStatus.none)

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: CustomerDetailsEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: CustomerDetailsDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getCustomerDetailsStatus(entity: entity, completionHandler: handler)

        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.status == StockTradeJourneyFactory.pvcStatus)
        XCTAssertTrue(resultWarnings == nil)
    }
}
