//
//  Copyright © 2022 HSBC plc. All rights reserved.
//

import XCTest

class StockTradeInteractorImplementationTestsBuy: XCTestCase {
    var interactor: StockTradeInteractorImplementation?

    override func setUp() {
        // Put setup code here. This method is called before the invocation of each test method in the class.
        interactor = StockTradeInteractorImplementation()
    }

    override func tearDown() {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        interactor = nil
    }

    func test_getBuyingPowerResultWith200() {
        let network = MockNetworking<BuyPowerURLRequest>(jsonFileName: "MOCKStockBuyingPower")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getBuyingPower(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.buyingPower != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getBuyingPowerResultWith400() {
        let network = MockNetworking<BuyPowerURLRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getBuyingPower(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getBuyingPowerResultWith500() {
        let network = MockNetworking<BuyPowerURLRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getBuyingPower(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getValidateCustomerEligibilityResultWith200() {
        let network = MockNetworking<CustomerEligibilityRequest>(jsonFileName: "MOCKStockBuyvalidateCustomerEligibility")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getValidateCustomerEligibilityResultInChinaMarket_isMDSDown_stockCode_hasPrefix_6_ExhcnageCodeShouldNotBeNil_determinsAsSHStock() {
        let network = MockNetworking<CustomerEligibilityRequest>(jsonFileName: "MOCKStockBuyvalidateCustomerEligibility")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .china
        entity.stockName = "SH Stock"
        entity.currency = .chineseYuan
        entity.stockCode = "600000"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.exchangeCode = nil
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
            let request = network.resultRequest
            let customerEligibilityRequest = request as! CustomerEligibilityRequest

            XCTAssert(customerEligibilityRequest.queryParameters["body"] != nil)

            XCTAssert((customerEligibilityRequest.queryParameters["body"]! as String).contains("SH") == true)
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getValidateCustomerEligibilityResultInChinaMarket_isMDSDown_stockCode_noPrefix_ExhcnageCodeShouldNotBeNil_determinsAsSZStock() {
        let network = MockNetworking<CustomerEligibilityRequest>(jsonFileName: "MOCKStockBuyvalidateCustomerEligibility")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .china
        entity.stockName = "SZ Stock"
        entity.currency = .chineseYuan
        entity.stockCode = "00001"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.exchangeCode = nil
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
            let request = network.resultRequest
            let customerEligibilityRequest = request as! CustomerEligibilityRequest

            XCTAssert(customerEligibilityRequest.queryParameters["body"] != nil)

            XCTAssert((customerEligibilityRequest.queryParameters["body"]! as String).contains("SZ") == true)
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getValidateCustomerEligibilityResultInChinaMarket_ExhcnageCodeShouldNotBeNil_WithResponseCode200() {
        let network = MockNetworking<CustomerEligibilityRequest>(jsonFileName: "MOCKStockBuyvalidateCustomerEligibility")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .china
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .chineseYuan
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.exchangeCode = .SHAS
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getValidateCustomerEligibilityResultWith400() {
        let network = MockNetworking<CustomerEligibilityRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getValidateCustomerEligibilityResultWith500() {
        let network = MockNetworking<CustomerEligibilityRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getvValidateBuyOrderResultWith200() {
        let network = MockNetworking<OrderPreviewRequest>(jsonFileName: "MOCKStockBuyValidateBuyOrder")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.goodUntil != nil)

        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getvValidateBuyOrderResultWith400() {
        let network = MockNetworking<OrderPreviewRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getvValidateBuyOrderResultWith500() {
        let network = MockNetworking<OrderPreviewRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getValidatePostBuyOrderResultWith200() {
        let network = MockNetworking<OrderConfirmationRequest>(jsonFileName: "MOCKStockBuyPostBuyOrder")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        interactor?.requestlimitCallTime = 0
        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.setNetworkClient(networkClient: nil, wealthITNetworkClient: nil, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        entity.investmentAccount = nil
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = nil
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.stockMarket = nil
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        entity.stockMarket = .hongKong
        entity.orderType = nil
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        entity.orderType = .limit
        entity.instruction = nil
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        entity.instruction = .buy
        entity.quantity = nil
        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        entity.quantity = 400

        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.goodUntil != nil)
        XCTAssertTrue(resultEntity?.referenceNumber != nil)
        XCTAssertEqual(resultWarnings, nil)
    }

    func test_whenMDSDown_postBuyOrderWithMarketOrderType_apiShouldreturn_200() {
        let network = MockNetworking<OrderConfirmationRequest>(jsonFileName: "MOCKStockBuyPostBuyOrder")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = nil
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = true
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = nil
        entity.orderType = .market
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = nil
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.goodUntil != nil)
        XCTAssertTrue(resultEntity?.referenceNumber != nil)
        XCTAssertEqual(resultWarnings, nil)
    }

    func test_getValidatePostBuyOrderResultWith200DuplicationIndicated() {
        let network = MockNetworking<OrderConfirmationRequest>(jsonFileName: "MOCKStockBuyPostBuyOrderDuplicated")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.goodUntil != nil)
        XCTAssertTrue(resultEntity?.referenceNumber != nil)
        XCTAssertNotEqual(resultEntity?.warnings, nil)
        XCTAssertEqual(resultEntity?.duplicatedRequestIndicator, true)
    }

    func test_getValidatePostBuyOrderResultWith400() {
        let network = MockNetworking<OrderConfirmationRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getValidatePostBuyOrderResultWith500() {
        let network = MockNetworking<OrderConfirmationRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.postTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getValidateCancelBuyOrderResultWith200() {
        let network = MockNetworking<OrderCancelRequest>(jsonFileName: "MOCKStockBuyCancelBuyOrder")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.setNetworkClient(networkClient: nil, wealthITNetworkClient: nil, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        entity.stockMarket = nil
        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        entity.stockMarket = .hongKong
        entity.instruction = nil
        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        entity.instruction = .buy
        entity.quantity = nil
        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        entity.quantity = 400
        entity.overrideObject = nil
        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        entity.overrideObject = overrideObject

        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.referenceNumber != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getValidateCancelBuyOrderResultWith400() {
        let network = MockNetworking<OrderCancelRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getValidateCancelBuyOrderResultWith500() {
        let network = MockNetworking<OrderCancelRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.cancelTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_getValidateModifyBuyOrderResultWith200() {
        let network = MockNetworking<OrderModifyRequest>(jsonFileName: "MOCKStockBuyModifyBuyOrder")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.setNetworkClient(networkClient: nil, wealthITNetworkClient: nil, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")
        entity.investmentAccount = nil
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = nil
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.overrideObject = nil
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        entity.overrideObject = overrideObject
        entity.stockMarket = nil
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        entity.stockMarket = .hongKong
        entity.orderType = nil
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        entity.orderType = .limit
        entity.instruction = nil
        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        entity.instruction = .buy

        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.referenceNumber != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getValidateModifyBuyOrderResultWith400() {
        let network = MockNetworking<OrderModifyRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getValidateModifyBuyOrderResultWith500() {
        let network = MockNetworking<OrderModifyRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.modifyTradeOrder(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.buyingPower, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    //  test strategy validateInvestorCharacterization
    /**

     api: validateCustomerEligibility
     json: CustomerEligibility
     */
    func test_validateSellOrderCustomerEligibilitySuccess() {
        let network = MockNetworking<OrderModifyRequestSingapore>(jsonFileName: "MOCKStockBuyvalidateCustomerEligibility")

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.goodUntil != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    //  test strategy validateInvestorCharacterization
    /**

     api: validateCustomerEligibility
     json: CustomerEligibility
     */
    func test_validateSellOrderCustomerEligibilityFailed() {
        let network = MockNetworking<OrderModifyRequestSingapore>(jsonFileName: "", responseCode: 400)
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeCustomerEligibilityCompletionHandler = { errorCode, newEntity, warnings, isCustomerEligible, isMobileValid, isEmailValid in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.validateCustomerEligibility(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .badRequest)
        XCTAssertTrue(resultEntity != nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    //  test strategy decimalPlacesFromString
    /**

     expect int from function
     */
    func test_decimalPlacesFromStringSuccess() {
        let count = interactor?.decimalPlacesFromString(string: "1.23")
        XCTAssertTrue(count == 2)
    }

    //  test strategy decimalPlacesFromString
    /**

     expect int from function
     */
    func test_decimalPlacesFromStringFailed() {
        let count = interactor?.decimalPlacesFromString(string: "")
        XCTAssertTrue(count == 0)
    }

    func test_updateInvestorCharacterizationWithValidInputs_shouldUpdateDataCorrectly() {
        let network = MockNetworking<OrderModifyRequestSingapore>(jsonFileName: "", responseCode: 200)
        let mockWarning: [HSBCWarning] = []
        network.warnings = mockWarning

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.acceptedInvestorTermsAndCondition = true
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.updateInvestorCharacterization(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.acceptedInvestorTermsAndCondition == entity.acceptedInvestorTermsAndCondition)
        XCTAssertEqual(resultWarnings, nil)
    }

    func test_updateInvestorCharacterizationWithErro_shouldThrowError() {
        let network = MockNetworking<OrderModifyRequestSingapore>(jsonFileName: "", responseCode: 400)
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.updateInvestorCharacterization(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .badRequest)
        XCTAssertTrue(resultEntity != nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }

    func test_updateInvestorCharacterizationTimeout_shouldThrowError() {
        let network = MockNetworking<OrderModifyRequestSingapore>(jsonFileName: "", responseCode: 500)
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning

        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockMarket = .hongKong
        entity.stockName = "HSBC HOLDINGS PLC"
        entity.currency = .hongKongDollar
        entity.stockCode = "00005"
        entity.instruction = .buy
        entity.isMDSDown = false
        entity.tradingPeriodType = .cts
        entity.investmentProductType = .stock
        entity.spreadCode = "01"
        entity.lotSize = 400
        entity.orderType = .limit
        entity.quantity = 400
        entity.proposedQuantity = 400
        entity.price = 40
        entity.referenceNumber = "482443"
        entity.goodUntil = Date()
        entity.investmentAccount = WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")
        entity.settlementAccount = WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d")

        let overrideObject = OverrideObject(stockCode: "00005", stockName: "HSBC HOLDINGS PLC", stockMarket: .hongKong, productType: .stock, orderType: .limit, price: entity.price, currency: entity.currency, quantity: 400, executedQuantity: 0, outstandingQuantity: 0, goodUntil: Date(), transactionDate: nil, investmentAccount: WXInvestmentAccount(type: .svinv, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), settlementAccount: WXSettlementAccount(type: .svsav, number: "005-032883-833", currency: .hongKongDollar, checksum: "9b99bcd2c82d9a10767c51ad1a8491bd760f3ae76a03efcf8f463e742e25727d"), referenceNumber: "")

        entity.overrideObject = overrideObject

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockTradeDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.updateInvestorCharacterization(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .noError)
        XCTAssertTrue(resultEntity != nil)
        XCTAssertEqual(resultWarnings, nil)
    }

    func test_getAvailableInvestmentAccountsResultWith200() {
        let network = MockNetworking<OrderPreviewRequest>(jsonFileName: "MockGetTradableUnits")
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockCode = "00005"
        entity.stockMarket = .hongKong

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockSellDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getAvailableInvestmentAccounts(entity: entity, completionHandler: handler)
        XCTAssertTrue(resultErrorCode == .noError)
        XCTAssertTrue(resultEntity?.stockCode != nil)
        XCTAssertTrue(resultWarnings == nil)
    }

    func test_getAvailableInvestmentAccountsResultWith400() {
        let network = MockNetworking<OrderPreviewRequest>(jsonFileName: "")
        network.responseCode = 400
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockCode = "00005"
        entity.stockMarket = .hongKong

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockSellDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getAvailableInvestmentAccounts(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .badRequest)
        XCTAssertEqual(resultEntity?.stockMarket, nil)
        XCTAssertNotEqual(resultWarnings, nil)
    }

    func test_getAvailableInvestmentAccountsResultWith500() {
        let network = MockNetworking<OrderPreviewRequest>(jsonFileName: "")
        network.responseCode = 500
        let mockWarning = [HSBCWarning(code: "9999", message: [], correlationId: "")]
        network.warnings = mockWarning
        interactor?.setNetworkClient(networkClient: network, wealthITNetworkClient: network, accountsProxy: "", ordersProxy: "", productsProxy: "", wealthITProxy: "", topMoversProxy: "")

        var entity = StockTradeEntity()
        entity.stockCode = "00005"
        entity.stockMarket = .hongKong

        var resultErrorCode: HSBCErrorCode?
        var resultEntity: StockTradeEntity?
        var resultWarnings: [HSBCWarning]?
        let handler: StockSellDefaultCompletionHandler = { errorCode, newEntity, warnings in
            resultErrorCode = errorCode
            resultEntity = newEntity
            resultWarnings = warnings
        }

        interactor?.getAvailableInvestmentAccounts(entity: entity, completionHandler: handler)
        XCTAssertEqual(resultErrorCode, .internalServerError)
        XCTAssertEqual(resultEntity?.matchingSettlementAccounts, nil)
        XCTAssertTrue(resultWarnings == mockWarning)
    }
}
