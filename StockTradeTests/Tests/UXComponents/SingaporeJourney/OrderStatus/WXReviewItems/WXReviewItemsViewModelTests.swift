//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import XCTest

class WXReviewItemsViewModelTests: XCTestCase {
    var viewModel: WXReviewItemsViewModel!

    override func setUp() {
        // Put setup code here. This method is called before the invocation of each test method in the class.
        viewModel = WXReviewItemsViewModel()
    }

    override func tearDown() {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        viewModel = nil
    }

    func testViewDidLoad_viewDidLoadIsCalled() {
        viewModel.viewDidLoad()
    }

    func testViewDeinit_viewDeinitIsCalled() {
        viewModel.viewDeinit()
    }

    func testUpdateModuleVisibilityWithTrue_moduleVisibilityShouldReturnTrue() {
        XCTAssertEqual(viewModel._moduleVisibility.value, nil)
        viewModel.inputs.updateModuleVisibility(visible: true)
        XCTAssertEqual(viewModel._moduleVisibility.value, true)
    }

    func testUpdateModuleVisibilityWithFalse_moduleVisibilityShouldReturnFalse() {
        XCTAssertEqual(viewModel._moduleVisibility.value, nil)
        viewModel.inputs.updateModuleVisibility(visible: false)
        XCTAssertEqual(viewModel._moduleVisibility.value, false)
    }

    func testUpdateDataWithEmptyData_dataShouldBeReturn0Count() {
        XCTAssertEqual(viewModel._data.value, nil)
        viewModel.inputs.updateDate(data: [])
        XCTAssertEqual(viewModel._data.value?.count, 0)
    }

    func testUpdateDataWithData_dataShouldBeReturnSpecific() {
        XCTAssertEqual(viewModel._data.value, nil)
        let id = WXReviewItemDate(title: "t", subtitle: "s", value: "v", accessibilityIdentifier: "a")
        viewModel.inputs.updateDate(data: [id])
        XCTAssertEqual(viewModel._data.value, [id])
    }
}
