//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import XCTest
class MockWXInfoScreenViewController: WXInfoScreenViewController {
    var styleNavigationControllerCallCount = 0
    override func styleNavigationController(_ navigationController: UINavigationController) {
        styleNavigationControllerCallCount += 1
    }
}

class WXInfoScreenViewControllerTests: XCTestCase {
    override func setUpWithError() throws {
        ThemeManager.shared.applyTheme(for: .wpb, customFontGenerator: nil)
    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    func test_embedInNavigationControllerCalled_styleNavigationController_shouldBeCalled() throws {
        let sut = MockWXInfoScreenViewController()
        let before = sut.styleNavigationControllerCallCount
        XCTAssertEqual(before, 0)
        _ = sut.embedInNavigationController()
        XCTAssertEqual(before + 1, sut.styleNavigationControllerCallCount)
    }
}
