//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

// import wealthxcore
import XCTest

class SellInvestorCharacterizationFormViewModelTests: XCTestCase {
    var mockView: MockInvestorCharacterizationFormScreen!
    var sut: SellInvestorCharacterizationFormViewModel!
    var mockStockSellPresenter: MockStockSellPresenterImplementation!

    override func setUp() {
        mockView = MockInvestorCharacterizationFormScreen()
        // TODO: Create a mock router in the future
        sut = SellInvestorCharacterizationFormViewModel(view: mockView)
        mockStockSellPresenter = MockStockSellPresenterImplementation.configure(view: sut, routerImplementation: nil)

        // Assign the mock presenter to viewModel
        sut.stockSellPresenter = mockStockSellPresenter
    }

    override func tearDown() {
        mockView = nil
        sut = nil
        mockStockSellPresenter = nil
    }

    func test_viewModelViewDidLoad_presenterViewDidLoadShouldBeCalled() {
        let callCountBeforeTest = mockStockSellPresenter.viewDidLoadCallCount
        sut.viewDidLoad()
        XCTAssertTrue(mockStockSellPresenter.viewDidLoadCallCount == callCountBeforeTest + 1)
    }

    func test_viewModelViewDeinit_presenterViewDeinitShouldBeCalled() {
        let callCountBeforeTest = mockStockSellPresenter.viewDeinitCallCount
        sut.viewDeinit()
        XCTAssertTrue(mockStockSellPresenter.viewDeinitCallCount == callCountBeforeTest + 1)
    }

    func test_viewModelDidUpdateAcceptICForm_processNextStep() {
        // Call viewDidLoad before starting the test
        sut.viewDidLoad()

        let callCountBeforeTest = mockView.proceedToNextStepCallCount
        let callCountBeforeTest2 = mockStockSellPresenter.attemptUpdateInvestorCharacterizationCallCount
        sut.didClickAcceptButton()
        sut.didAcceptICFormTermsAndCondition(true)

        XCTAssertTrue(mockView.proceedToNextStepCallCount == callCountBeforeTest + 1)
        XCTAssertTrue(mockStockSellPresenter.attemptUpdateInvestorCharacterizationCallCount == callCountBeforeTest2 + 1)
    }

    func test_viewModelDidUpdateAcceptICForm_showErrorDialog() {
        // Call viewDidLoad before starting the test
        sut.viewDidLoad()

        let callCountBeforeTest = mockView.showErrorDialogCallCount
        sut.didClickAcceptButton()
        sut.showErrorMessage(.PRESENTER_REQUEST_UPDATE_INVESTOR_CHARACTERIZATION, errorCode: .badGateway, warnings: [])

        XCTAssertTrue(mockView.showErrorDialogCallCount == callCountBeforeTest + 1)
    }

    func test_viewModelDidUpdateAcceptICForm_didClickBack() {
        // Call viewDidLoad before starting the test
        sut.viewDidLoad()

        let callCountBeforeTest = mockView.popToPreviousPageCallCount
        sut.didClickBackButton(animated: false)

        XCTAssertTrue(mockView.popToPreviousPageCallCount == callCountBeforeTest + 1)
    }
}
