//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import Foundation
import StockTrade
// import StockTrackManager

class MockStockTrackManager: StockTrackManager {
    var eventInfo: EventInfo?
    var trackPageCallCount = 0
    var trackEventCallCount = 0
    var trackAppDynamicTrackEventCallCount = 0
    override func trackPage(_ eventInfo: EventInfo) {
        self.eventInfo = eventInfo
        trackPageCallCount += 1
    }

    override func trackEvent(_ eventInfo: EventInfo) {
        self.eventInfo = eventInfo
        trackEventCallCount += 1
    }

    var appDynamicTrackEventString: String? = ""
    var methodNameString: String?
    override func appDynamicTrackEvent(_ message: String, _ methodName: String, _ withArguments: [String]) {
        appDynamicTrackEventString = message
        methodNameString = methodName
        trackAppDynamicTrackEventCallCount += 1
    }

    var trackCallCount: Int = 0
    var trackHandler: ((_ eventId: String, _ eventInfo: EventInfo?, _ overrideValues: [String: String]) -> Void)?
    func track(_ eventId: String, _ overrideValues: [TagKey: String] = [:]) {
        super.track(eventId, overrideValues)
        trackCallCount += 1
        var overrideValuesDict = [String: String]()
        for case (let key, let value) in overrideValues {
            overrideValuesDict[key.rawValue] = value
        }
        trackHandler?(eventId, eventInfo, overrideValuesDict)
    }
}

extension EventInfo: Equatable {
    public static func == (lhs: EventInfo, rhs: EventInfo) -> Bool {
        lhs.screenName == rhs.screenName &&
            lhs.pageURL == rhs.pageURL &&
            lhs.pageLanguage == rhs.pageLanguage &&
            lhs.customDict == rhs.customDict
    }
}

// class MockStockTrack: DefaultTracker {
//
//
//
// }
