//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

// import wealthxcore
import XCTest

class SellPriceInputViewModelTests: XCTestCase {
    var mockView: MockPriceInputView!
    var sut: SellPriceInputViewModel!

    var mockStockSellPresenter: MockStockSellPresenterImplementation!

    override func setUp() {
        // Put setup code here. This method is called before the invocation of each test method in the class.
        mockView = MockPriceInputView()

        sut = SellPriceInputViewModel(view: mockView, tracker: MockStockTrackManager(), instruction: .sell)
        mockStockSellPresenter = MockStockSellPresenterImplementation.configure(view: sut, routerImplementation: nil)
        sut.stockSellPresenter = mockStockSellPresenter
    }

    override func tearDown() {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        sut = nil
        mockStockSellPresenter = nil
        mockView = nil
    }

    func test_viewModelViewDidLoad_presenterViewDidLoadShouldBeCalled() {
        let callCountBeforeTest1 = mockStockSellPresenter.viewDidLoadCallCount
        sut.viewDidLoad()

        XCTAssertTrue(mockStockSellPresenter.viewDidLoadCallCount == callCountBeforeTest1 + 1)
    }

    func test_viewModelViewDeinit_presenterViewDeinitShouldBeCalled() {
        let callCountBeforeTest1 = mockStockSellPresenter.viewDeinitCallCount
        sut.viewDeinit()

        XCTAssertTrue(mockStockSellPresenter.viewDeinitCallCount == callCountBeforeTest1 + 1)
    }

    func test_didClickPriceIncreaseButton_shouldCallViewDismissMethod() {
        sut.viewDidLoad()
        let callCount = mockStockSellPresenter.attemptIncreaseProposedPriceCallCount
        sut.didClickPriceIncreaseButton()
        XCTAssertTrue(mockStockSellPresenter.attemptIncreaseProposedPriceCallCount == callCount + 1)
    }

    func test_didClickPriceDecreaseButton_shouldCallViewDismissMethod() {
        sut.viewDidLoad()

        let callCount = mockStockSellPresenter.attemptDecreaseProposedPriceCallCount
        sut.didClickPriceDecreaseButton()
        XCTAssertTrue(mockStockSellPresenter.attemptDecreaseProposedPriceCallCount == callCount + 1)
    }

    func test_didEnterPrice_shouldSetTheUpdateStringForValidInput() {
        sut.viewDidLoad()
        let expected = "123456789"
        mockStockSellPresenter.didEditSection(section: .market(market: .hongKong))
        mockStockSellPresenter.didEditSection(section: .currency(currency: .hongKongDollar))

        sut.inputs.didUpdatePrice(price: expected)
        if let value = sut._proposedPrice.value {
            print("==> sut._proposedPrice.value \(value)")
        } else {
            print("==> sut._proposedPrice.value null")
        }
        print("==> expected \(expected)")
        XCTAssert(sut._proposedPrice.value == Decimal(string: expected))
    }

    func test_didEnterPrice_shouldNotSetTheUpdateStringForValidInput() {
        sut.viewDidLoad()
        let invalidString = "1234567890"

        mockStockSellPresenter.didEditSection(section: .market(market: .hongKong))
        mockStockSellPresenter.didEditSection(section: .currency(currency: .hongKongDollar))

        sut.didUpdatePrice(price: invalidString)
        if let value = sut._proposedPrice.value {
            print("==> sut._proposedPrice.value \(value)")
        } else {
            print("==> sut._proposedPrice.value null")
        }
        print("==> invalidString \(invalidString)")
        XCTAssert(sut._proposedPrice.value != Decimal(string: invalidString))
    }
}
