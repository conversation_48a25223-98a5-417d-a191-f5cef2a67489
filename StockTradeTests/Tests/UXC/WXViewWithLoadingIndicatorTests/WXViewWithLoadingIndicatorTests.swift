//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import XCTest

class WXViewWithLoadingIndicatorTests: XCTestCase {
    var sut: WXViewWithLoadingIndicator!
    override func setUpWithError() throws {
        ThemeManager.shared.applyTheme(for: .wpb, customFontGenerator: nil)
        sut = WXViewWithLoadingIndicator()
        sut.viewDidLoad()
    }

    override func tearDown() {
        sut.viewDeinit()
        sut = nil
    }

    func test_viewDidLoad_viewDidLoadIsCalled() {
        sut.viewModel.inputs.viewDidLoad(())
        XCTAssertNotNil(sut.viewModel._viewDidLoad.value)
    }

    func test_viewDeinit_viewDeinitIsCalled() {
        sut.viewModel.inputs.viewDeinit(())
        XCTAssertNotNil(sut.viewModel._viewDeinit.value)
    }

    func test_updateLoadingIndicatorVisibility() {
        sut.viewModel.inputs.updateLoadingIndicatorVisibility(visible: true)
        XCTAssertTrue(sut.viewModel._loadingIndicatorVisibility.value == true)
    }

    func test_updateModuleVisibility() {
        sut.viewModel.inputs.updateChildViewsInteractivity(interactable: true)
        XCTAssertTrue(sut.viewModel._childViewsInteractivity.value == true)
    }
}
