//
//  Copyright © 2021 HSBC plc. All rights reserved.
//

import MobileDesign
import XCTest

class TradeOrderReviewInstructionReviewItemTests: XCTestCase {
    var sut: TradeOrderReviewInstructionReviewItem!
    override func setUpWithError() throws {
        ThemeManager.shared.applyTheme(for: .wpb, customFontGenerator: nil)
        sut = TradeOrderReviewInstructionReviewItem()
        sut.viewDidLoad()
    }

    override func tearDownWithError() throws {
        sut.viewDeinit()
    }

    func test_TradeOrderReviewInstruction_reviewItem_dataShouldBeVisible() throws {
        XCTAssertEqual(sut.viewModel._title.value, localizedString("stock_review_instruction"))
        XCTAssertEqual(sut.viewModel._value.value, nil)

        let instructionType: HSBCStockTradeOrderInstruction = .buy
        sut.didUpdateInstructionSection(instruction: instructionType)

        let exc1 = expectation(description: "rx_swift_call_1")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            exc1.fulfill()
        }
        waitForExpectations(timeout: 2, handler: nil)
        XCTAssertEqual(sut.viewModel._title.value, localizedString("stock_review_instruction"))
        XCTAssertEqual(sut.viewModel._value.value, localizedString(instructionType.desc))
        XCTAssertEqual(sut.accessibilityValue, localizedString(instructionType.desc))
    }
}
